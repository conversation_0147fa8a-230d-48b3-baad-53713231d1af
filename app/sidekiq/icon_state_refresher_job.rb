# frozen_string_literal: true

require 'sidekiq-scheduler'

class IconStateRefresherJob
  include Sidekiq::Job

  sidekiq_options lock: :until_executed,
                  lock_ttl: 5.minutes,
                  lock_args: ->(rt, rid) { [rt, rid] },
                  retry: 3,
                  dead: false

  def perform(record_type, record_id)
    record = record_type.safe_constantize&.find_by(id: record_id)

    return unless record

    case record
    when ChartingAppointment
      @charting_appointment = record
      @calendar_booking = record.calendar_booking
      @patient = record.course_of_treatment&.patient
    when CalendarBooking
      @calendar_booking = record
      @charting_appointment = record.charting_appointments.first
      @patient = record.patient
    end

    ActiveRecord::Base.transaction do
      @calendar_booking&.lock!
      @charting_appointment&.lock!

      existing = @calendar_booking&.icon_state || @charting_appointment&.icon_state
      existing&.destroy!

      IconState.create!(
        calendar_booking: @calendar_booking,
        charting_appointment: @charting_appointment,
        lab_work_state:,
        payments_state:,
        documents_state:,
        complaints_state:,
        medical_history_state:
      )
    end
  end

  private

  def lab_work_state
    if @charting_appointment
      lab_work = @charting_appointment.lab_works.last
    elsif @patient
      lab_work = @patient.lab_works.last
    else
      return 'blank'
    end

    case lab_work&.status
    when 'Checked By Dentist - Ready For Patient', 'Fitted Successfully - Auto Archive', 'Checked by Dentist - Awaiting Try-In'
      'positive'
    when 'Arrived At Practice - Dentist Check Required'
      'pending'
    when 'Late', 'Lab Error - Adjustment or Remake Required', 'Overdue', 'Archive Case - Not proceeding',
          'Adjustment Required (Amend or Create New Lab Docket)'
      'negative'
    else
      'blank'
    end
  end

  def payments_state
    flags = []

    if @patient&.balance&.> 0
      flags << 'positive'
    elsif @patient&.balance&.< 0
      flags << 'negative'
    end

    payment_plans = @patient&.payment_plans

    if payment_plans.present?
      payment_statuses = payment_plans.flat_map { |plan| plan.payments.map(&:status) }

      flags << 'negative' if payment_statuses.include?('failed')
      flags << 'pending' if payment_statuses.include?('pending')
      flags << 'positive' if payment_statuses.all? { |status| %w[succeeded refunded].include?(status) }
    end

    case @calendar_booking&.payment_request_status
    when 'unpaid'
      flags << 'negative'
    when 'paid'
      flags << 'positive'
    end

    return 'negative' if flags.include?('negative')
    return 'pending' if flags.include?('pending')
    return 'positive' if flags.include?('positive')

    'blank'
  end

  def documents_state
    return 'blank' unless @charting_appointment

    requests = @charting_appointment.signature_requests

    return 'positive' if requests.any? && requests.all?(&:signed?)
    return 'negative' if requests.any? { |sr| !sr.signed? && sr.due_by < Time.current }

    'blank'
  end

  def complaints_state
    return 'blank' unless @patient

    complaints = @patient.actions.select { |action| action.action_type == 'complaint' }

    return 'positive' if complaints.any? && complaints.all?(&:completed?)
    return 'pending' if complaints.any? { |complaint| !complaint.completed? && complaint.date_due && complaint.date_due > Time.current }
    return 'negative' if complaints.any? && !complaints.all?(&:completed?)

    'blank'
  end

  def medical_history_state
    return 'blank' if @calendar_booking&.start_time&.to_date != Date.current

    if @patient.medical_histories.any? { |md| (Time.now.in_time_zone('London') - md.created_at.in_time_zone('London')) <= 24.hours }
      'positive'
    else
      'pending'
    end
  end
end
