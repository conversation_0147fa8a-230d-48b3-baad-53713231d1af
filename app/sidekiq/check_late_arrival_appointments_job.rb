# frozen_string_literal: true

require 'sidekiq-scheduler'

class CheckLateArrivalAppointmentsJob
  include Sidekiq::<PERSON>

  def perform
    CalendarBooking.no_show_15_minutes_late.find_each do |booking|
      next if booking.in_surgery? || booking.attended? || booking.fta? || booking.patient_id.nil?

      name = booking.patient.full_name
      description = <<~HTML.strip
        #{name} has not been marked as attended, in surgery, or failed to attend (FTA),
        and it is now 15 minutes past their scheduled appointment time.
        Please check their status and update the diary accordingly to maintain accurate records and avoid disruption to the clinical schedule.
      HTML
      booking.all_related_users.each do |recipient|
        Notification.create!(
          recipient: recipient,
          title: "Has #{name} arrived for their appointment?",
          description: description,
          data: {
            type: 'appointment_update',
            booking_id: booking.id,
            patient_id: booking.patient_id,
            sender: name
          },
          actions: [
            { text: 'View Calendar', primary: true, action: 'redirect', href: '/admin/calendar_bookings/staff_calendar' },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end
    end
  end
end
