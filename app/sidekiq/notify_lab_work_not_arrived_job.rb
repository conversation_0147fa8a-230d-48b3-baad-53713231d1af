# frozen_string_literal: true

require 'sidekiq-scheduler'

class NotifyLabWorkNotArrivedJob
  include Sidekiq::Job

  def perform
    check(LabWork.due_today, :due_today)
    check(LabWork.late, :due)
  end

  private

  def check(lab_works, type)
    lab_works.each do |lab_work|
      next if lab_work.status != 'Sent To Lab'

      recipients = lab_work.all_related_users

      message =
        case type
        when :due_today
          <<~TEXT.strip
            Lab work for #{lab_work.patient.full_name} is due back today but has not been marked as arrived in Labwork.
            Please urgently check its status with the lab or courier and update Labwork immediately.
            Delays may impact the scheduled treatment and patient experience.
          TEXT
        when :due
          <<~TEXT.strip
            Lab work is due for #{lab_work.patient.full_name} but has not been marked as arrived in Labwork.
            Please verify its status with the lab or courier and update Labwork accordingly to avoid any disruption to the patient’s treatment.
          TEXT
        end

      title =
        case type
        when :due_today
          <<~TEXT.strip
            Lab work not arrived for #{lab_work.patient.full_name} due today
          TEXT
        when :due
          <<~TEXT.strip
            Lab work overdue for #{lab_work.patient.full_name} was due yesterday
          TEXT
        end

      recipients.each do |recipient|
        Notification.create(
          recipient: recipient,
          title: title,
          description: message,
          data: { type: 'labwork', sender: lab_work.lab.name, lab_name: lab_work.lab.name, color: '#EA580C' },
          actions: [
            { text: 'View Lab Work', primary: true, action: 'redirect', href: "/admin/lab_works/#{lab_work.id}" },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end
    end
  end
end
