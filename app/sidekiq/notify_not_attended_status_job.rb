# frozen_string_literal: true

require 'sidekiq-scheduler'

class NotifyNotAttendedStatusJob
  include Sidekiq::Job

  def perform
    CalendarBooking.starting_now_and_not_attended.find_each do |booking|
      next if booking.attended? || booking.patient_id.nil?

      name = booking.patient.full_name
      message = <<~HTML.strip
        #{name} was scheduled to arrive for their appointment at #{booking.start_time.strftime('%H:%M')} but has not yet been marked as attended.
        If they have arrived, please ensure the appointment status is updated on calendar as soon as possible to keep their records accurate.
      HTML

      recipients = booking.all_related_users
      recipients.each do |recipient|
        Notification.create(
          recipient: recipient,
          title: "Has #{name} arrived for their appointment?",
          description: message,
          data: { type: 'appointment_update', sender: name },
          actions: [
            { text: 'View Calendar', primary: true, action: 'redirect', href: '/admin/calendar_bookings/staff_calendar' },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end
    end
  end
end
