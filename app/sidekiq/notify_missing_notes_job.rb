# frozen_string_literal: true

require 'sidekiq-scheduler'

class NotifyMissingNotesJob
  include Sidekiq::Job

  def perform
    CalendarBooking.missing_notes_24h_after_end.find_each do |booking|
      description = <<~HTML.strip
        It appears no clinical notes have been recorded for #{booking.patient.full_name}’s appointment on
        <strong>#{booking.start_time.strftime('%d %B')} at #{booking.start_time.strftime('%H:%M')}</strong>,
        which took place over 24 hours ago. Please ensure the notes are entered in the system as soon as possible to maintain accurate and compliant patient records.
      HTML

      Notification.create(
        recipient: booking.practitioner,
        title: "Missing clinical notes for #{booking.patient.full_name} for yesterday’s appointment.",
        description: description,
        data: {
          type: 'clinical_notes',
          color: '#F97316',
          sender: booking.patient.full_name,
          avatar_url: booking.patient.image&.url
        },
        actions: [
          { text: 'View Patient Charting', primary: true, action: 'redirect', href: "/admin/patients/#{booking.patient.id}/charting" },
          { text: 'Mark Read', action: 'mark_as_read' },
          { text: 'Remind Me Later', action: 'remind_in' }
        ]
      )
    end
  end
end
