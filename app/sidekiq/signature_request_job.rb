# frozen_string_literal: true

require 'sidekiq-scheduler'

class SignatureRequestJob
  include Sidekiq::Job

  def perform
    range = Time.zone.today..(Time.zone.today + 4.days)

    ChartingAppointment
      .includes(:calendar_booking)
      .where.not(calendar_bookings: { status: :cancelled })
      .where(calendar_bookings: { start_time: range })
      .find_each do |appt|
      treatment_ids = appt.charted_treatments.map(&:treatment).pluck(:id).uniq

      required_forms = DocumentTemplate
                       .joins(:treatments)
                       .where(treatments: { id: treatment_ids })
                       .distinct
      completed_requests = SignatureRequest
                           .includes(:signable_document)
                           .where(charting_appointment_id: appt.id)

      signed_template_ids = completed_requests
                            .map { |req| req.signable_document&.document_template || [] }
                            .pluck(:id)
                            .uniq

      missing_forms = required_forms.reject { |form| signed_template_ids.include?(form.id) }

      next if missing_forms.empty?

      appt_time = appt.calendar_booking.start_time
      now = Time.zone.now
      send_now = appt_time <= (now + 3.days)

      if send_now
        missing_forms.each do |form|
          options = {
            title: 'document',
            document_template_id: form.id,
            template_text: form.text,
            due_by: appt_time.to_date,
            charting_appointment_id: appt.id
          }

          SecureSend::AdminSignatureRequestService.new(
            appt.course_of_treatment.patient,
            appt.course_of_treatment.dentist,
            options
          ).create_signable_document_and_request
        end
      else
        missing_forms.each do |form|
          options = {
            title: 'document',
            document_template_id: form.id,
            template_text: form.text,
            due_by: appt_time.to_date,
            charting_appointment_id: appt.id,
            sent_at: appt_time - 3.days
          }

          SecureSend::AdminSignatureRequestService.new(
            appt.course_of_treatment.patient,
            appt.course_of_treatment.dentist,
            options
          ).create_signable_document_and_request
        end
      end
    end
  end
end
