# frozen_string_literal: true

require 'sidekiq-scheduler'

class NotifyNotInSurgeryStatusJob
  include Sidekiq::Job

  def perform
    check(CalendarBooking.starting_now_and_not_in_surgery, :start)
    check(CalendarBooking.five_minutes_late_and_not_in_surgery, :late)
  end

  private

  def check(bookings, type)
    bookings.find_each do |booking|
      next if booking.in_surgery? || booking.patient_id.nil?

      message =
        case type
        when :start
          <<~TEXT.strip
            #{booking.patient.full_name}’s appointment is due to start,
            but they have not been marked as in surgery. If the patient is now in surgery,
            please update their appointment status accordingly.
          TEXT
        when :late
          <<~TEXT.strip
            #{booking.patient.full_name}’s appointment was scheduled to begin 5 minutes ago,
            but they have not been marked as in surgery. If the patient is now in surgery,
            please update their appointment status.
          TEXT
        end

      patient_friendly_names = booking.charting_appointments
                                      .includes(charted_treatments: :treatment)
                                      .flat_map(&:charted_treatments)
                                      .map(&:treatment)
                                      .compact
                                      .map(&:patient_friendly_name)
                                      .uniq

      recipients = booking.all_related_users

      recipients.each do |recipient|
        Notification.create(
          recipient: recipient,
          title: "Patient #{booking.patient.full_name} is not in surgery",
          description: message,
          data: { type: 'appointment_status',
                  sender: booking.patient.full_name,
                  style: type == :start ? 'started' : 'late',
                  duration: booking.duration_in_minutes,
                  start_time: booking.start_time.strftime('%H:%M'),
                  end_time: booking.end_time.strftime('%H:%M'),
                  treatment: patient_friendly_names },
          actions: [
            { text: 'View Calendar', primary: true, action: 'redirect', href: '/admin/calendar_bookings/staff_calendar' },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end
    end
  end
end
