# frozen_string_literal: true

class PracticeSetupJob < ApplicationJob
  queue_as :default

  # TODO: run for all DELIVERY_METHODS like in import script

  def perform(practice_id)
    practice = Practice.find(practice_id)

    DEFAULT_TEMPLATES.each do |template|
      NotificationTemplate.create(
        name: template[:name],
        delivery_method: 'email',
        subject: template[:subject],
        title: template[:title],
        text: template[:text],
        section: template[:section],
        default: true,
        practice: practice
      )
    end

    DEFAULT_SMS_TEMPLATES.each do |template|
      NotificationTemplate.create(
        name: template[:name],
        delivery_method: 'sms',
        text: template[:text],
        section: template[:section],
        default: true,
        practice: practice
      )
    end
  end
end
