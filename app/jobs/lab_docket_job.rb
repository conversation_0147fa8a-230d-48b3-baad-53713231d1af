# frozen_string_literal: true

class LabDocketJob < ApplicationJob
  queue_as :default

  def perform(lab_docket_id)
    lab_docket = LabDocket.find(lab_docket_id)
    lab_docket.patient_asset&.destroy

    return unless lab_docket.lab_work&.patient

    asset = lab_docket.build_patient_asset(
      patient: lab_docket.lab_work.patient,
      lab_docket: lab_docket,
      label: 'Lab Dockets'
    )

    total = lab_docket.lab_docket_items.map(&:lab_docket_treatments).flatten.map do |item|
      li = item.lab_item
      li.price.to_f * (item.units || 1).to_f
    end.sum

    pdf_html = ApplicationController.render(
      template: 'admin/lab_dockets/pdf',
      layout: false,
      assigns: {
        lab_docket: lab_docket,
        lab_docket_items: lab_docket.lab_docket_items,
        lab_work: lab_docket.lab_work,
        lab: lab_docket.lab_work.lab,
        total: total
      }
    )

    pdf_content = WickedPdf.new.pdf_from_string(pdf_html)

    asset.file.attach(
      io: StringIO.new(pdf_content),
      filename: "lab-docket-#{lab_docket_id}.pdf",
      content_type: 'application/pdf'
    )

    asset.save
  end
end
