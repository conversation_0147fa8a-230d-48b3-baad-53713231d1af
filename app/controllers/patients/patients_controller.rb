# frozen_string_literal: true

module Patients
  class PatientsController < Patients::ApplicationController
    layout 'patient_page'

    def dashboard
      @patient = current_patient
      @practice = @patient.current_practice || @patient.practices.first
      @sent_for_viewing = @patient.treatment_plan_options.sent_for_viewing.exists?
      @next_appointment = CalendarBooking.where(patient_id: @patient.id, practice_id: @practice.id)
                                         .where('start_time >= ?', Time.current)
                                         .min_by do |booking|
        (booking.start_time - Time.current).abs
      end

      @signature_requests = @patient.signature_requests
                                    .where(
                                      '(status = :pending AND (due_by > :today OR due_by IS NULL))',
                                      pending: 'pending',
                                      today: Date.current
                                    )
                                    .order(created_at: :desc)

      @treatment_plans = @patient.treatment_plans
                                 .left_joins(:treatment_plan_options)
                                 .where(
                                   '(treatment_plans.status = :pending AND treatment_plan_options.status = :sent)',
                                   pending: 0,
                                   sent: 'Sent for Viewing'
                                 )
                                 .distinct
                                 .order(created_at: :desc)

      @update_medical_history = false
      if @next_appointment
        cutoff_time = @next_appointment.start_time.to_date - 1.day + 9.hours
        latest_history = @patient.medical_histories.order(created_at: :desc).first
        @update_medical_history = Time.current >= cutoff_time && (latest_history.nil? || latest_history.created_at < cutoff_time)
      end
    end

    def medical_histories
      @patient = current_patient
      @practice = @patient.current_practice || @patient.practices.first
      @medical_histories = @patient.medical_histories.distinct.order(created_at: :desc)
    end

    def treatment_plans
      @patient = current_patient
      @practice = @patient.current_practice || @patient.practices.first
      @treatment_plans = @patient.treatment_plans.distinct
    end

    def signature_requests
      @patient = current_patient
      @practice = @patient.current_practice || @patient.practices.first
      @signature_requests = @patient.signature_requests
    end

    def documents
      @patient = current_patient
      @practice = @patient.current_practice || @patient.practices.first
      @treatment_plans = @patient.treatment_plans
                                 .left_joins(:treatment_plan_options)
                                 .where(
                                   "(treatment_plans.status = :completed) OR
                                    (treatment_plans.status = :pending AND treatment_plan_options.status IN (:option_statuses))",
                                   completed: 1,
                                   pending: 0,
                                   option_statuses: ['Sent for Viewing', 'Accepted']
                                 )
                                 .distinct
                                 .order(created_at: :desc)

      @signature_requests = @patient.signature_requests
                                    .where(
                                      "(status = :completed) OR
                                       (status = :closed) OR
                                       (status = :pending AND (due_by > :today OR due_by IS NULL))",
                                      completed: 'completed',
                                      closed: 'closed',
                                      pending: 'pending',
                                      today: Date.current
                                    )
                                    .order(created_at: :desc)
      @medical_histories = @patient.medical_histories.order(created_at: :desc)
    end

    def edit
      @patient = current_patient
      @practice = @patient.current_practice || @patient.practices.first
    end

    def update_field
      patient = current_patient
      params_hash = patient_params.to_h

      if params_hash.present?
        field_name = params_hash.keys.first
        field_value = params_hash[field_name]

        consent_fields = %w[email_consent sms_consent whatsapp_consent instagram_consent facebook_consent marketing_consent]

        patient.assign_attributes(field_name => field_value)
        patient.consent_updated_at = Time.current if consent_fields.include?(field_name) && field_name != 'consent_updated_at'

        if patient.save
          render json: { success: true, patient: { id: patient.id, field_name => field_value } }
        else
          render json: { success: false, errors: ['Failed to update field'] }, status: :unprocessable_entity
        end
      else
        render json: { success: false, errors: ['No field provided for update'] }, status: :unprocessable_entity
      end
    end

    def update
      @patient = current_patient

      @patient.image.attach(params.dig(:patient, :image)) if params[:patient]&.[](:image).present?

      if @patient.save
        redirect_to dashboard_patients_patients_path, notice: 'Patient updated successfully.'
      else
        redirect_to dashboard_patients_patients_path, alert: 'Failed to update patient.'
      end
    end

    def update_password
      @patient = current_patient

      verification_id = params[:verification_id]
      code = params[:verification_code]
      new_password = params[:password]

      practice = @patient.practices.first
      sinch_service = Sinch::VerificationService.new(practice)
      response = sinch_service.report_verification_code(verification_id, code)

      if response['status'] == 'SUCCESSFUL'
        @patient.update(password: new_password)
        redirect_url = dashboard_patients_patients_path
        render json: { success: true, redirect_url: redirect_url }
      else
        render json: { success: false, error: 'Invalid or expired verification code.' }, status: :unauthorized
      end
    end

    def send_sms
      @patient = current_patient

      if @patient&.mobile_phone.present?
        practice = @patient.practices.first
        sinch_service = Sinch::VerificationService.new(practice)
        response = sinch_service.send_verification_code(@patient.mobile_phone)

        if response['id']
          render json: { success: true, verification_id: response['id'] }
        else
          render json: { success: false, error: response[:error] || 'Failed to send verification code' }
        end
      else
        render json: { success: false, error: 'No mobile phone number found for this patient' }
      end
    end

    private

    def patient_params
      params.require(:patient).permit(
        :title,
        :first_name,
        :middle_name,
        :last_name,
        :previous_last_name,
        :preferred_name,
        :date_of_birth,
        :email,
        :mobile_phone,
        :alternative_phone,
        :work_phone,
        :ethnicity,
        :biological_sex,
        :gender,
        :pronouns,
        :ni_number,
        :insurance_number,
        :address_line_1,
        :address_line_2,
        :county,
        :city,
        :postcode,
        :country,
        :school_name,
        :school_phone_number,
        :school_address_line_1,
        :school_address_line_2,
        :school_county,
        :school_country,
        :school_city,
        :school_postcode,
        :image,
        :sms_consent,
        :email_consent,
        :whatsapp_consent,
        :instagram_consent,
        :emergency_contact_name,
        :emergency_contact_number,
        :facebook_consent,
        :marketing_consent,
        :patient_gp_id,
        :occupation,
        :on_stop,
        assigned_staff_ids: [],
        practice_ids: [],
        alert_ids: [],
        cot_payment_plan_ids: []
      )
    end
  end
end
