# frozen_string_literal: true

module Webhooks
  class SinchController < ApplicationController
    skip_before_action :verify_authenticity_token

    def incoming_messages
      project_id = params['project_id']
      practice = Practice.find_by(sinch_project_id: project_id) || Practice.first
      channel = params.dig('message', 'channel_identity', 'channel')
      unformated_sender = params.dig('message', 'channel_identity', 'identity')
      sender = if unformated_sender&.start_with?('+') || channel.downcase == 'messenger' || channel.downcase == 'instagram'
                 unformated_sender
               else
                 "+#{unformated_sender}"
               end
      unformatted_recipient = params.dig('message', 'sender_id')
      recipient = if channel == 'WHATSAPP'
                    practice.communication_accounts.where(account_type: 'whatsapp').first&.identifier
                  elsif unformatted_recipient&.start_with?('+') || channel.downcase == 'messenger' || channel.downcase == 'instagram'
                    unformatted_recipient
                  else
                    "+#{unformatted_recipient}"
                  end
      message = params.dig('message', 'contact_message', 'text_message', 'text')
      message_id = params.dig('message', 'id')

      if sender.blank? || message.blank? || channel.blank?
        Rails.logger.error("Invalid payload: #{params.inspect}")
        return head :bad_request
      end

      conversation = Conversation.from_sender(sender, channel)
      conversation = Conversation.create(practice_id: practice.id) if conversation.blank?

      message = conversation.conversation_messages.create(
        content: ActionView::Base.full_sanitizer.sanitize(message),
        message_type: 'inbound',
        from: sender,
        to: recipient,
        source: channel,
        delivery_status: 'received',
        external_id: message_id
      )

      message.send_pusher_update

      head :ok
    end

    def messages_delivery
      data = params['message_delivery_report']

      message_id = data['message_id']
      status = data['status']

      if message_id.present?
        message = ConversationMessage.find_by(external_id: message_id)

        case status
        when 'DELIVERED'
          message&.update(delivery_status: 'delivered') unless message&.delivery_status == 'read'
        when 'READ'
          message&.update(delivery_status: 'read')
        when 'FAILED'
          message&.update(delivery_status: 'failed', failure_reason: data.dig('reason', 'description'))
        else
          message
        end
      end

      head :ok
    end

    def read_receipts
      data = params['event-data']

      message_id = data.dig('message', 'headers', 'message-id')
      event_type = data['event']

      if message_id.present?
        message = ConversationMessage.find_by(external_id: message_id)

        message&.update(delivery_status: 'read') if event_type == 'opened'
      end
      head :ok
    end

    def delivery_receipts
      data = params['event-data']

      message_id = data.dig('message', 'headers', 'message-id')
      event_type = data['event']

      if message_id.present?
        message = ConversationMessage.find_by(external_id: message_id)

        message&.update(delivery_status: 'delivered') if event_type == 'delivered'
      end
      head :ok
    end

    def failed_receipts
      data = params['event-data']

      message_id = data.dig('message', 'headers', 'message-id')
      event_type = data['event']

      if message_id.present?
        message = ConversationMessage.find_by(external_id: message_id)

        if event_type == 'failed'
          message&.update(delivery_status: 'failed',
                          failure_reason: data.dig('delivery-status', 'description').presence ||
                            data.dig('delivery-status', 'message'))
        end
      end
      head :ok
    end

    def incoming_mail
      sender = params['sender']
      sent_to = params['To']
      message = params['stripped-text']
      subject = params['Subject']
      message_id = params['Message-Id']

      if sender.blank? || message.blank?
        Rails.logger.error('Invalid payload!')
        return head :bad_request
      end

      # user = Patient.find_by(email: sender)
      conversation = Conversation.from_sender(sender, 'email')
      if conversation.blank?
        practice = CommunicationAccount.find_by(identifier: sent_to)&.practice || Practice.first
        conversation = Conversation.create(practice_id: practice.id)
      end

      parsed_message = EmailReplyParser.parse_reply(message)
      sanitized_message = ActionView::Base.full_sanitizer.sanitize(parsed_message)
      message = conversation.conversation_messages.create(
        content: sanitized_message,
        message_type: 'inbound',
        from: sender,
        email_subject: subject,
        to: sent_to,
        source: 'email',
        delivery_status: 'received',
        external_id: message_id
      )

      message.send_pusher_update
    end
  end
end
