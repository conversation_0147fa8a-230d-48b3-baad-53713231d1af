# frozen_string_literal: true

module Admin
  module Patients
    class ChartingAppointmentsController < Admin::ApplicationController
      before_action :set_patient

      def index
        # NOTE: 'ALL' default view
        @unbooked_charting_appointments = policy_scope(ChartingAppointment)
                                          .includes(
                                            :calendar_booking,
                                            :lab_works,
                                            :signature_requests,
                                            charted_treatments: [:treatment],
                                            dentist: [:image_attachment],
                                            course_of_treatment: [
                                              patient: [
                                                :medical_histories,
                                                :actions,
                                                { payment_plans: [payments: :refunds],
                                                  payments: :refunds,
                                                  invoices: [:invoice_items, { payments: :refunds }] }
                                              ]
                                            ]
                                          )
                                          .where(course_of_treatment: { patient_id: @patient.id,
                                                                        archived: false, accepted: true }, calendar_booking_id: nil)
                                          .order('charting_appointments.created_at DESC')

        @calendar_bookings = policy_scope(CalendarBooking)
                             .includes(
                               :booked_by,
                               :recall,
                               :treatment,
                               patient: [
                                 :medical_histories,
                                 :actions,
                                 :payments,
                                 :lab_works,
                                 { payment_plans: [payments: :refunds],
                                   payments: :refunds,
                                   invoices: [:invoice_items, { payments: :refunds }] }
                               ],
                               practitioner: [:image_attachment],
                               charting_appointments: [
                                 :lab_works,
                                 :signature_requests,
                                 { charted_treatments: [:treatment] }
                               ]
                             )
                             .where(patient_id: @patient.id)
                             .order('calendar_bookings.start_time ASC')

        @filter = params[:filter]

        # NOTE: this page displays both charting appointments and calendar bookings and has a single filter for both
        case @filter
        when 'upcoming'
          @calendar_bookings = @calendar_bookings
                               .where.not(status: %i[cancelled completed fta])
          @unbooked_charting_appointments = []
        when 'unbooked'
          @calendar_bookings = []
        when 'completed'
          @calendar_bookings = @calendar_bookings.completed
          @unbooked_charting_appointments = []
        when 'cancelled'
          @calendar_bookings = @calendar_bookings.cancelled
          @unbooked_charting_appointments = []
        when 'missed'
          @calendar_bookings = @calendar_bookings.fta
          @unbooked_charting_appointments = []
        end

        @status_groups = build_status_groups if @filter.blank? || @filter == 'all'
      end

      private

      def set_patient
        @patient = Patient.find(params[:patient_id])

        self.page_title = @patient.full_name_with_title
      end

      def build_status_groups
        unbooked_appointments = @unbooked_charting_appointments.map { |a| { type: :unbooked, record: a } }

        upcoming_bookings = @calendar_bookings.where.not(status: %i[cancelled completed fta])
        completed_bookings = @calendar_bookings.completed
        cancelled_bookings = @calendar_bookings.cancelled
        missed_bookings = @calendar_bookings.fta

        [
          { status: 'Unbooked', appointments: unbooked_appointments, icon: 'calendar-circle-plus', color: 'amber' },
          { status: 'Upcoming', appointments: upcoming_bookings.map do |b|
                                                { type: :booked, record: b }
                                              end, icon: 'calendar', color: 'orange' },
          { status: 'Completed', appointments: completed_bookings.map do |b|
                                                 { type: :booked, record: b }
                                               end, icon: 'circle-check', color: 'green' },
          { status: 'Cancelled', appointments: cancelled_bookings.map { |b| { type: :booked, record: b } }, icon: 'xmark', color: 'red' },
          { status: 'Missed', appointments: missed_bookings.map do |b|
                                              { type: :booked, record: b }
                                            end, icon: 'face-sad-sweat', color: 'purple' }
        ]
      end
    end
  end
end
