# frozen_string_literal: true

module Admin
  module Patients
    class PaymentsController < Admin::ApplicationController
      before_action :set_patient, only: %i[new create send_to_terminal cancel_terminal]
      before_action :set_invoices, only: %i[new create]
      before_action :set_payment, only: %i[destroy edit_reason refund link_to_payment link_to_invoice]

      def new
        @practice = @patient.current_practice
        @payment = Payment.new
        @terminals = fetch_terminals(@practice)
      end

      def create
        @payment = PaymentBuilder.new(
          payment_params, @patient, current_user,
          return_url: admin_patient_account_url(@patient),
          stripe_token: params[:payment][:stripe_token]
        ).build_full_payment

        respond_to do |format|
          if @payment.save
            format.html do
              flash[:success] = 'Payment created!'
              redirect_to admin_patient_account_path(@patient)
            end
            format.json do
              render json: { url: admin_patient_account_path(@patient) }, status: :ok
            end
          else
            format.html do
              flash[:error] = ['Error creating payment', @payment.errors&.full_messages]
              render :new
            end
            format.json do
              render json: {
                error: @payment.errors.full_messages.to_sentence
              }, status: :unprocessable_entity
            end
          end
        end
      end

      def destroy
        if @payment.destroy
          flash[:success] = 'Payment deleted successfully!'
        else
          flash[:error] = 'Failed to delete payment!'
        end
        redirect_to admin_patient_account_path(@payment.patient)
      end

      def link_to_payment
        invoice = Invoice.find(params[:invoice_id])
        patient = invoice.patient

        return if @payment.nil?

        if @payment.update(invoice_id: invoice.id)
          flash[:success] = 'Payment linked to invoice successfully!'
        else
          flash[:error] = 'Failed to link payment to invoice!'
        end

        redirect_to admin_patient_account_path(patient)
      end

      def link_to_invoice
        invoice = Invoice.find(params[:invoice_id])

        return if invoice.nil?

        if @payment.update(invoice_id: invoice.id)
          flash[:success] = 'Payment linked to invoice successfully!'
        else
          flash[:error] = 'Failed to link payment to invoice!'
        end

        redirect_to admin_patient_account_path(@payment.patient)
      end

      def edit_reason
        return if @payment.nil?

        if @payment.update(summary: params[:summary])
          flash[:success] = 'Payment reason updated successfully!'
        else
          flash[:error] = 'Failed to update payment reason!'
        end

        redirect_to admin_patient_account_path(@payment.patient)
      end

      def refund
        if @payment.refunds.create(amount: params[:refund_amount].to_f)
          flash[:success] = 'Payment refunded successfully!'
        else
          flash[:error] = 'Failed to refund payment!'
        end

        redirect_to admin_patient_account_path(@payment.patient)
      end

      def send_to_terminal
        modified_params = payment_params.merge(payment_mean: 'terminal')
        terminal_id = params[:payment][:terminal]

        @payment = PaymentBuilder.new(
          modified_params, @patient, current_user,
          return_url: admin_patient_account_url(@patient)
        ).build_full_payment

        respond_to do |format|
          if @payment.save
            begin
              practice = @patient.current_practice
              payment_intent = create_terminal_payment_intent(@payment, terminal_id, practice)
              @payment.update(stripe_payment_intent_id: payment_intent.id)

              format.json do
                render json: {
                  success: true,
                  payment_id: @payment.id,
                  stripe_payment_intent_id: payment_intent.id,
                  message: 'Payment sent to terminal successfully'
                }, status: :ok
              end
            rescue Stripe::StripeError => e
              @payment.destroy
              format.json do
                render json: {
                  success: false,
                  error: "Stripe error: #{e.message}"
                }, status: :unprocessable_entity
              end
            end
          else
            format.json do
              render json: {
                success: false,
                error: @payment.errors.full_messages.to_sentence
              }, status: :unprocessable_entity
            end
          end
        end
      end

      def cancel_terminal
        payment_intent_id = params[:payment_intent_id]

        begin
          practice = @patient.current_practice
          Stripe::PaymentIntent.cancel(payment_intent_id, {}, { stripe_account: practice.stripe_account_id })

          payment = Payment.find_by(stripe_payment_intent_id: payment_intent_id)
          payment&.update(status: 'cancelled')

          render json: { success: true, message: 'Terminal payment cancelled successfully' }
        rescue Stripe::StripeError => e
          render json: { success: false, error: e.message }, status: :unprocessable_entity
        rescue StandardError => e
          render json: { success: false, error: e }, status: :internal_server_error
        end
      end

      private

      def set_patient
        @patient = Patient.find(params[:patient_id])

        self.page_title = @patient.full_name_with_title
      end

      def set_invoices
        @invoices = @patient.invoices.not_paid
      end

      def set_payment
        @payment = Payment.find(params[:id])
      end

      def payment_params
        params.require(:payment).permit(
          :amount,
          :invoice_id,
          :course_of_treatment_id,
          :payment_method,
          :payment_date,
          :due_date,
          :currency,
          :payment_mean,
          :name,
          :summary
        )
      end

      def fetch_terminals(practice)
        return [] unless practice.stripe_account_id

        begin
          terminals = Stripe::Terminal::Reader.list({}, { stripe_account: practice.stripe_account_id })
          terminals.data.map { |terminal| [terminal.label || terminal.id, terminal.id] }
        rescue Stripe::StripeError => e
          Rails.logger.error "Failed to fetch terminals: #{e.message}"
          []
        end
      end

      def create_terminal_payment_intent(payment, terminal_id, practice)
        amount_in_cents = (payment.amount * 100).to_i

        payment_intent = Stripe::PaymentIntent.create({
                                                        amount: amount_in_cents,
                                                        currency: payment.currency.downcase,
                                                        payment_method_types: ['card_present'],
                                                        capture_method: 'automatic',
                                                        metadata: {
                                                          payment_id: payment.id,
                                                          patient_id: payment.patient.id
                                                        }
                                                      }, { stripe_account: practice.stripe_account_id })

        Stripe::Terminal::Reader.process_payment_intent(
          terminal_id,
          { payment_intent: payment_intent.id },
          { stripe_account: practice.stripe_account_id }
        )

        payment_intent
      end
    end
  end
end
