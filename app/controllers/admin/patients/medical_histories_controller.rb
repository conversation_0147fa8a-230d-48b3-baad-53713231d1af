# frozen_string_literal: true

module Admin
  module Patients
    class MedicalHistoriesController < Admin::ApplicationController
      before_action :set_patient

      def index
        @medical_histories = @patient.medical_histories.order(created_at: :desc)
      end

      def new
        @medical_history = @patient.medical_histories.new(created_by: current_user)
        @questions = MedicalHistoryQuestion.where(medical_history_answer_id: nil,
                                                  practice_id: [nil, @patient.current_practice_id]).order(:position)
      end

      def create
        @medical_history = @patient.medical_histories.new(
          created_by: current_user,
          patient_comments: params[:medical_history][:patient_comments],
          our_comments: params[:medical_history][:our_comments],
          additional_information: params[:medical_history][:additional_information]
        )

        if @medical_history.save
          parse_responses(params[:responses] || {})

          redirect_to admin_patient_medical_histories_path(@patient),
                      notice: 'Medical History created successfully!'
        else
          flash.now[:alert] = "Error creating Medical History: #{@medical_history.errors.full_messages.join(', ')}"
          @questions = MedicalHistoryQuestion.where(medical_history_answer_id: nil,
                                                    practice_id: [nil, @patient.current_practice_id])
          render :new
        end
      end

      def show
        @medical_history = @patient.medical_histories.find(params[:id])
      end

      def modal_data
        @medical_history = @patient.medical_histories.find(params[:id])

        # Format patient data
        patient_data = {
          id: params[:patient_id],
          name: @patient.full_name,
          date_of_birth: @patient.date_of_birth&.strftime('%B %d, %Y'),
          initials: get_patient_initials(@patient.full_name)
        }

        # Format medical history data
        medical_history_data = {
          completion_date: @medical_history.created_at.strftime('%B %d, %Y')
        }

        # Format questions data from the frozen JSON
        questions_data = format_questions_for_modal(@medical_history.data || [])

        # Format alerts data
        alerts_data = format_alerts_for_modal(@medical_history)

        render json: {
          patient: patient_data,
          medical_history: medical_history_data,
          questions: questions_data,
          alerts: alerts_data
        }
      end

      def add_alert
        @medical_history = @patient.medical_histories.find(params[:id])
        question_id = params[:question_id]
        alert_text = params[:alert_text]

        if alert_text.blank?
          render json: { success: false, message: 'Alert text is required' }
          return
        end

        # Create the alert record
        alert = @medical_history.medical_history_ai_alerts.create!(
          alert_text: alert_text,
          question_id: question_id
        )

        if alert.persisted?
          render json: { success: true, message: 'Alert added successfully' }
        else
          render json: { success: false, message: 'Failed to add alert' }
        end
      end

      def remove_alert
        @medical_history = @patient.medical_histories.find(params[:id])
        question_id = params[:question_id]

        # Delete all alerts for this question
        alerts_deleted = @medical_history.medical_history_ai_alerts.where(question_id: question_id).destroy_all

        if alerts_deleted.any?
          render json: { success: true, message: 'Alert removed successfully' }
        else
          render json: { success: false, message: 'No alerts found to remove' }
        end
      end

      def card_summary_partial
        @medical_history = @patient.medical_histories.find(params[:id])
        render partial: 'admin/patients/medical_histories/card_summary', locals: { history: @medical_history }
      end

      def card_alerts_partial
        @medical_history = @patient.medical_histories.find(params[:id])
        render partial: 'admin/patients/medical_histories/card_alerts', locals: { history: @medical_history }
      end

      def card_changes_partial
        @medical_history = @patient.medical_histories.find(params[:id])
        render partial: 'admin/patients/medical_histories/card_changes', locals: { history: @medical_history }
      end

      def send_to_email
        MedicalHistory.send_to_email(@patient, current_user)

        redirect_to admin_patient_medical_histories_path(@patient), notice: 'Medical History sent successfully!'
      end

      def send_to_sms
        MedicalHistory.send_to_sms(@patient, current_user)

        redirect_to admin_patient_medical_histories_path(@patient), notice: 'Medical History sent successfully!'
      end

      def pdf
        @medical_history = MedicalHistory.find(params[:id])

        # Use the same data formatting as modal_data for consistency
        patient_data = {
          id: params[:patient_id],
          name: @patient.full_name,
          date_of_birth: @patient.date_of_birth&.strftime('%B %d, %Y'),
          initials: get_patient_initials(@patient.full_name)
        }

        medical_history_data = {
          completion_date: @medical_history.created_at.strftime('%B %d, %Y')
        }

        questions_data = format_questions_for_modal(@medical_history.data || [])
        alerts_data = format_alerts_for_modal(@medical_history)

        # Separate alerts and changes from the alerts_data
        alerts = alerts_data.select { |item| item[:type] == 'alert' }
        changes = alerts_data.select { |item| item[:type] == 'change' }

        @formatted_data = {
          patient: patient_data,
          medical_history: medical_history_data,
          questions: questions_data,
          alerts: alerts,
          changes: changes
        }

        respond_to do |format|
          format.html do
            render pdf: "medical-history-#{@medical_history.id}",
                   disposition: 'attachment',
                   template: 'admin/patients/medical_histories/comprehensive_pdf'
          end
        end
      end

      private

      def set_patient
        @patient = Patient.find(params[:patient_id])

        self.page_title = @patient.full_name_with_title
      end

      def parse_responses(responses_hash)
        @all_questions = MedicalHistoryQuestion.where(practice_id: [nil, @patient.current_practice_id])
                                               .includes(:medical_history_answers)
                                               .all

        responses_hash.each do |question_id, data|
          question = @all_questions.find { |q| q.id == question_id.to_i }
          parse_question_responses(question, data)
        end
      end

      def parse_question_responses(question, data)
        case question.question_type
        when 'multiple_answers', 'single_answer'
          selected_answer_ids = Array(data['answers'])
          selected_answer_ids.each do |answer_id|
            @medical_history.medical_histories_medical_history_answers.create!(
              medical_history_answer_id: answer_id
            )
          end

        when 'text'
          (data['answers'] || {}).each do |answer_id, typed_value|
            @medical_history.medical_histories_medical_history_answers.create!(
              medical_history_answer_id: answer_id,
              value: typed_value
            )
          end

        when 'medication_select'
          selected_meds = Array(data['answers']).reject(&:blank?)
          return if selected_meds.empty?

          answer = question.medical_history_answers
                           .first_or_create!(answer: '')

          @medical_history.medical_histories_medical_history_answers.create!(
            medical_history_answer_id: answer.id,
            value: selected_meds.join(',')
          )
        end

        if data['followups'].present?
          data['followups'].each do |sub_q_id, sub_data|
            sub_question = @all_questions.find { |q| q.id == sub_q_id.to_i }
            parse_question_responses(sub_question, sub_data)
          end
        end
      end

      def get_patient_initials(full_name)
        return '--' if full_name.blank?

        names = full_name.split(' ')
        if names.length >= 2
          "#{names.first[0]}#{names.last[0]}".upcase
        else
          names.first[0..1].upcase
        end
      end

      def format_questions_for_modal(questions_data)
        # Get all questions for this medical history
        question_ids = @medical_history.medical_histories_medical_history_answers
                                       .joins(:medical_history_answer)
                                       .pluck('medical_history_answers.medical_history_question_id')
                                       .uniq

        # Get AI alerts and changes associated with questions
        ai_alerts = @medical_history.medical_history_ai_alerts.where.not(question_id: nil).group(:question_id).count
        ai_changes = @medical_history.medical_history_ai_changes.where.not(question_id: nil).group(:question_id).count

        result = questions_data.map.with_index do |question_data, index|
          # Try to match this question to an actual question ID
          question_id = find_question_id_for_data(question_data, question_ids, index)

          {
            id: question_id,
            question: question_data['question'],
            selected_answer: question_data['answer'],
            details: question_data['details'],
            has_alert: question_id && ai_alerts[question_id].present?,
            has_change: question_id && ai_changes[question_id].present?,
            priority: calculate_question_priority(question_id, ai_alerts, ai_changes),
            followup_questions: format_followup_questions(question_data['follow_ups'] || [])
          }
        end

        result.sort_by { |q| [-q[:priority], q[:question]] }
      end

      def find_question_id_for_data(question_data, _question_ids, _fallback_index)
        questions = MedicalHistoryQuestion.where(practice_id: [nil, @patient.current_practice_id])
        matched_question = questions.find { |q| q.question == question_data['question'] }

        matched_question.id
      end

      def calculate_question_priority(question_id, ai_alerts, ai_changes)
        return 0 unless question_id # No priority if no question ID

        priority = 0
        priority += 2 if ai_alerts[question_id].present?   # Alerts have highest priority
        priority += 1 if ai_changes[question_id].present?  # Changes have medium priority
        priority
      end

      def format_followup_questions(followups)
        return [] if followups.blank?

        followups.map do |followup|
          {
            question: followup['question'],
            answer: followup['answer']
          }
        end
      end

      def format_alerts_for_modal(medical_history)
        items = []

        # Get AI alerts with associated questions
        medical_history.medical_history_ai_alerts.includes(:medical_history_question).find_each do |alert|
          question_text = alert.medical_history_question&.question || 'Unknown Question'
          items << {
            id: alert.id,
            type: 'alert',
            title: question_text.truncate(50),
            description: alert.alert_text,
            question_id: alert.question_id
          }
        end

        # Get AI changes with associated questions
        medical_history.medical_history_ai_changes.includes(:medical_history_question).find_each do |change|
          question_text = change.medical_history_question&.question || 'Unknown Question'
          items << {
            id: change.id,
            type: 'change',
            title: question_text.truncate(50),
            description: change.change_text,
            question_id: change.question_id
          }
        end

        items
      end

      def format_comprehensive_pdf_data(medical_history)
        # Get questions data from frozen medical history data
        questions_data = medical_history.data || []
        formatted_questions = format_questions_for_modal(questions_data)

        # Get alerts and changes data
        alerts_data = format_alerts_for_modal(medical_history)

        # Separate alerts and changes
        alerts = alerts_data.select { |item| item[:type] == 'alert' }
        changes = alerts_data.select { |item| item[:type] == 'change' }

        {
          patient: {
            name: @patient.full_name,
            initials: get_patient_initials(@patient.full_name),
            date_of_birth: @patient.date_of_birth&.strftime('%B %d, %Y') || 'Not specified',
            completion_date: medical_history.created_at.strftime('%B %d, %Y')
          },
          questions: formatted_questions,
          alerts: alerts,
          changes: changes,
          medical_history: medical_history
        }
      end

      def update_question_alert_status(data, question_id, has_alert)
        data.each do |question_data|
          question_data['has_alert'] = has_alert if question_data['id'] == question_id
        end
        data
      end
    end
  end
end
