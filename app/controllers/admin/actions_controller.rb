# frozen_string_literal: true

module Admin
  class ActionsController < ApplicationController
    before_action :authenticate_user!
    layout 'admin', only: [:index]

    def index
      @actions = Action.includes(:actionable, :created_by)
                       .where.not(actionable: nil)
                       .order(
                         Arel.sql(
                           "CASE
                             WHEN date_due IS NULL THEN 3
                             WHEN date_due < NOW() THEN 1
                             ELSE 2
                           END,
                           date_due ASC NULLS LAST,
                           created_at DESC"
                         )
                       )

      # Filter by user_id if provided
      if params[:user_id].present?
        user_id = params[:user_id].to_i
        @actions = @actions.where(
          'assigned_to_json::text LIKE ? OR created_by_id = ?',
          "%#{user_id}%",
          user_id
        )
      end

      respond_to do |format|
        format.html do
          if request.xhr?
            if params[:completed_only].present?
              # For completed actions requests, render only completed actions
              completed_actions = @actions.select(&:completed)
              render partial: 'completed_actions_list', locals: { actions: completed_actions }
            else
              # For AJAX requests, render just the actions list partial with non-completed actions only
              # This matches the original sidebar behavior where completed actions are in a separate container
              non_completed_actions = @actions.reject(&:completed)
              render partial: 'actions_list', locals: { actions: non_completed_actions }
            end
          else
            # For regular requests, render the full page
            render :index
          end
        end
        format.json { render json: @actions }
      end
    end

    def create
      @action = Action.new(action_params)
      @action.created_by_id = current_user.id

      respond_to do |format|
        if @action.save
          format.html do
            flash[:success] = 'Action created!'
            redirect_to request.referer
          end
          format.json do
            render json: {
              success: true,
              action: @action,
              message: 'Action created!',
              update_banners: @action.action_type.in?(%w[alert complaint])
            }
          end
        else
          format.html do
            flash[:error] = ['Error creating action', @action.errors.first&.full_message]
            redirect_to request.referer
          end
          format.json do
            render json: {
              success: false,
              error: @action.errors.full_messages.join(', ')
            }, status: :unprocessable_entity
          end
        end
      end
    end

    def mark_as_completed
      @action = Action.find(params[:id])
      @action.update(completed: !@action.completed)

      respond_to do |format|
        format.html do
          flash[:success] = "Action marked as #{@action.completed ? 'completed' : 'uncompleted'}"
          redirect_to request.referer
        end
        format.json do
          render json: {
            success: true,
            action: @action,
            message: "Action marked as #{@action.completed ? 'completed' : 'uncompleted'}",
            update_banners: @action.action_type.in?(%w[alert complaint])
          }
        end
      end
    end

    # Handle AJAX requests for completing actions
    def complete
      @action = Action.find(params[:id])
      success = @action.update(action_params.merge(completed: true))

      respond_to do |format|
        format.json do
          if success
            render json: { success: true, action: @action, message: 'Action completed successfully' }
          else
            render json: { success: false, error: @action.errors.full_messages.join(', ') }, status: :unprocessable_entity
          end
        end
      end
    end

    def edit
      @action = Action.find(params[:id])
      respond_to do |format|
        format.json do
          action_json = @action.as_json(include: :actionable)
          if @action.patient.present?
            action_json['patient'] = {
              id: @action.patient.id,
              first_name: @action.patient.first_name,
              last_name: @action.patient.last_name
            }
          end

          render json: {
            success: true,
            action: action_json
          }
        end
      end
    end

    def update
      @action = Action.find(params[:id])

      respond_to do |format|
        if @action.update(action_params)
          format.html do
            flash[:success] = 'Action updated!'
            redirect_to request.referer
          end
          format.json do
            render json: {
              success: true,
              action: @action,
              message: 'Action updated!',
              update_banners: @action.action_type.in?(%w[alert complaint])
            }
          end
        else
          format.html do
            flash[:error] = ['Error updating action', @action.errors.first&.full_message]
            redirect_to request.referer
          end
          format.json do
            render json: {
              success: false,
              error: @action.errors.full_messages.join(', ')
            }, status: :unprocessable_entity
          end
        end
      end
    end

    def destroy
      @action = Action.find(params[:id])
      @action.destroy
      flash[:success] = 'Action archived'
      redirect_to request.referer
    end

    def timeline
      @action = Action.with_deleted.find(params[:id])
      @events = [
        @action,
        *@action.action_comments,
        *@action.action_comments.map(&:descendants).flatten
      ] # add in audit events herew
      render layout: false
    end

    def set_reminder
      @action = Action.find(params[:id])
      minutes = params[:minutes].to_i

      # Validate minutes parameter
      unless [5, 15, 30, 60, 120, 240, 1440, 10_080].include?(minutes)
        return render json: { error: 'Invalid reminder time' }, status: :unprocessable_entity
      end

      # Calculate the reminder time
      reminder_time = Time.current + minutes.minutes

      # Create a notification for each assigned user with the scheduled time
      begin
        ActiveRecord::Base.transaction do
          return render json: { error: 'No users assigned to this action' }, status: :unprocessable_entity if @action.assigned_to.empty?

          @action.assigned_to.each do |user|
            Notification.create!(
              recipient: user,
              title: "Reminder: #{@action.action_type.capitalize}",
              description: @action.title || @action.description,
              icon: Admin::ActionsController.helpers.action_icon_class(@action.action_type, @action.priority).delete_prefix('fa-light fa-'),
              actions: @action.get_notification_actions_for_type,
              scheduled_for: reminder_time
            )
          end
        end

        render json: { success: true }, status: :ok
      rescue StandardError => e
        Rails.logger.error("Error setting reminder: #{e.message}")
        render json: { error: 'Failed to set reminder' }, status: :internal_server_error
      end
    end

    private

    def counter
      # Get actions assigned to the current user
      assigned_action_ids = Action.where(deleted_at: nil).where('assigned_to_json::text LIKE ?', "%#{current_user.id}%").pluck(:id)

      # Count incomplete actions assigned to the user
      actions_count = Action.where(id: assigned_action_ids).incomplete.count

      render json: { count: actions_count }
    end

    def action_params
      params.require(:action_data).permit(:action_type, :actionable_type, :actionable_id, :patient_id,
                                          :description, :date_due, :completed, :priority, :assignment_type,
                                          :treatment_plan_id, :appointment_id, assigned_to_json: [])
    end
  end
end
