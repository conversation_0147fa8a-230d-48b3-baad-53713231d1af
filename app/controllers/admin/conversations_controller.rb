# frozen_string_literal: true

module Admin
  class ConversationsController < Admin::ApplicationController
    include ConversationsHelper
    include ConversationMessagesHelper
    include ActionsHelper
    include WhatsappTemplatesHelper

    before_action :set_filtered_conversations, only: %i[index show]
    before_action :set_conversation,
                  only: %i[show messages create_message insert_whatsapp_template insert_dynamic_template insert_letter_template update_name
                           mark_as_read mark_as_unread message_content notification_templates]

    def index
      respond_to do |format|
        format.html
        format.json { render json: conversations_json_response }
        # Handle AJAX requests for search
        format.js do
          if request.xhr?
            # Render only the conversation cards for the current filter
            render partial: 'conversation_cards', locals: { conversations: @conversations, filter: @filter }
          end
        end
      end
    end

    def select2_search
      @conversations = Conversation.where.not(id: params[:id]).includes([:patient])
      render json: @conversations.map { |conversation|
                     { id: conversation.id,
                       text: conversation.patient.present? ? conversation.patient.full_name : conversation.name }
                   }
    end

    def merge_conversation
      result = Conversations::MergeService.new(Conversation.find(params[:original_conversation_id]),
                                               Conversation.find(params[:selected_conversation_id])).execute
      render json: result
    end

    def create
      # Log the parameters for debugging
      Rails.logger.info "Creating conversation with params: #{conversation_params.inspect}"

      # Ensure we have a practice
      practice = current_user.practices.first
      Rails.logger.info "Using practice: #{practice.inspect}"

      # Create the conversation
      service = Conversations::CreationService.new(conversation_params, current_user, practice)
      result = service.execute

      # Log the result for debugging
      if result[:success]
        Rails.logger.info "Conversation created successfully: #{result[:conversation].inspect}"
      else
        Rails.logger.error "Failed to create conversation: #{result[:errors].inspect}"
      end

      respond_to do |format|
        if result[:success]
          format.html do
            redirect_to admin_conversation_path(result[:conversation]), notice: 'Conversation started successfully'
          end
          format.json { render json: { id: result[:conversation].id, status: 'success' }, status: :created }
        else
          format.html { redirect_to admin_conversations_path, alert: 'Failed to create conversation' }
          format.json { render json: { errors: result[:errors] }, status: :unprocessable_entity }
        end
      end
    end

    def mark_as_read
      message_service = Conversations::MessageStatusService.new(params[:message_id])
      @message = message_service.mark_as_read(current_user)

      respond_to do |format|
        format.html { redirect_to admin_conversation_path(@message.conversation_id) }
        format.json { render json: { success: true, status: 'read' } }
      end
    end

    def mark_as_unread
      message_service = Conversations::MessageStatusService.new(params[:message_id])
      @message = message_service.mark_as_unread

      respond_to do |format|
        format.html { redirect_to admin_conversation_path(@message.conversation_id) }
        format.json { render json: { success: true, status: 'unread' } }
      end
    end

    def show
      # Use the service to load messages with proper associations
      message_result = Conversations::MessageLoaderService.new(@conversation, params).load_messages
      @messages = message_result[:messages]
      @current_page = message_result[:page]
      @has_more_messages = message_result[:has_more]

      @whatsapp_templates = load_whatsapp_templates
      @letter_templates = load_letter_templates

      respond_to do |format|
        format.html
        format.json { render json: conversations_json_response }
        format.js do
          render partial: 'conversation',
                 locals: {
                   conversation: @conversation,
                   messages: @messages,
                   whatsapp_templates: @whatsapp_templates,
                   letter_templates: @letter_templates
                 },
                 layout: false
        end
      end
    end

    def search_patients
      query = params[:q].to_s.downcase

      @patients = if query.present?
                    Patient.where(
                      'LOWER(first_name) LIKE :q OR LOWER(last_name) LIKE :q OR LOWER(email) LIKE :q',
                      q: "%#{query}%"
                    )
                  else
                    Patient.none
                  end

      @patients = @patients.order(:last_name, :first_name).limit(10)

      respond_to do |format|
        format.json { render partial: 'admin/conversations/patient_results', formats: [:html] }
      end
    end

    def toggle_flag
      @conversation = Conversation.unscoped.find(params[:id])
      @conversation.update(flag: !@conversation.flag)

      respond_to do |format|
        format.html { redirect_to admin_conversations_path }
        format.json { render json: { flagged: @conversation.flag } }
      end
    end

    def toggle_archived
      @conversation = Conversation.unscoped.find(params[:id])
      @conversation.update(archived: !@conversation.archived)

      respond_to do |format|
        format.html { redirect_to admin_conversations_path }
        format.json { render json: { archived: @conversation.archived } }
      end
    end

    def insert_whatsapp_template
      template_service = Conversations::WhatsappTemplateService.new(@conversation, params[:template_id])
      render json: template_service.process
    end

    def insert_letter_template
      template_service = Conversations::LetterTemplateService.new(@conversation, params[:template_id])
      render json: template_service.process
    end

    def insert_dynamic_template
      template_service = Conversations::WhatsappTemplateService.new(@conversation, params[:template_id], params[:data])
      render json: template_service.process_dynamic
    end

    def create_message
      service = Conversations::MessageCreationService.new(
        @conversation,
        current_user.id,
        params[:content],
        {
          sender_id: params[:sender],
          template_id: params[:template_id],
          template_data: params[:template_data],
          session_id: params[:session_id],
          lab_work_id: params[:lab_work_id],
          message_type: params[:message_type]
        }
      )
      result = service.execute

      respond_to do |format|
        if result.present? && !result.is_a?(Hash) || (result.is_a?(Hash) && result[:success] != false)
          format.html do
            if params[:lab_work_id].present?
              redirect_to admin_lab_work_path(params[:lab_work_id]), notice: 'Message sent successfully'
            else
              redirect_to admin_conversation_path(@conversation), notice: 'Message sent successfully'
            end
          end
          format.json { render json: { success: true, message: 'Message sent successfully' } }
        else
          error_message = result.is_a?(Hash) && result[:error] ? result[:error] : 'Failed to send message'
          format.html do
            if params[:lab_work_id].present?
              redirect_to admin_lab_work_path(params[:lab_work_id]), alert: error_message
            else
              redirect_to admin_conversation_path(@conversation), alert: error_message
            end
          end
          format.json { render json: { success: false, error: error_message }, status: :unprocessable_entity }
        end
      end
    end

    def message_content
      @message = @conversation.conversation_messages.find(params[:message_id])
      render partial: 'admin/conversations/message', locals: { message: @message, pusher: true, user_id: current_user.id }
    end

    def notification_templates
      communication_account = CommunicationAccount.find(params[:account_id])
      practice = communication_account.practice
      delivery_method = communication_account.account_type == 'whatsapp' ? 'sms' : communication_account.account_type

      @templates = NotificationTemplate.where(
        practice: practice,
        delivery_method: delivery_method
      ).order(:name)

      render json: @templates.map { |template|
        {
          id: template.id,
          name: template.name,
          subject: template.subject,
          text: template.text,
          preview: template.text.truncate(100)
        }
      }
    end

    def update_label
      service = Conversations::LabelService.new(params[:message_id], params[:label])
      result = service.execute

      flash[result[:flash_type]] = result[:message]
      redirect_back(fallback_location: admin_conversation_path(service.conversation))
    end

    def update_name
      @conversation.update(name: params[:name])
      head :ok
    end

    def messages
      message_result = Conversations::MessageLoaderService.new(@conversation, params).load_messages

      render json: {
        messages: message_result[:messages].map do |message|
          {
            id: message.id,
            html: render_to_string(
              partial: 'admin/conversations/message',
              locals: {
                message: message,
                pusher: false,
                user_id: current_user.id
              },
              formats: [:html]
            )
          }
        end,
        page: message_result[:page],
        has_more: message_result[:has_more]
      }
    end

    def template_content
      conversation = Conversation.find(params[:id])
      template = NotificationTemplate.find(params[:template_id])

      begin
        processed_text = ::Notifications::PlaceholderConverter.convert(conversation.patient, template.text)

        render json: {
          success: true,
          processed_text: processed_text,
          template_id: template.id,
          template_name: template.name
        }
      rescue StandardError => e
        render json: {
          success: false,
          error: "Failed to process template: #{e.message}"
        }, status: :unprocessable_entity
      end
    end

    private

    def set_filtered_conversations
      @page = params[:page] || 1
      @filter = params[:filter]
      @search = params[:search]

      # We've already added includes in the ConversationFinderService methods
      @conversations = case @filter
                       when 'internal'
                         conversation_finder.internal_conversations(@page, @search)
                       when 'lab'
                         conversation_finder.lab_conversations(@page, @search)
                       when 'flag'
                         conversation_finder.flagged_conversations(@page, @search)
                       when 'archived'
                         conversation_finder.archived_conversations(@page, @search)
                       when 'attention'
                         conversation_finder.attention_required_conversations(@page, @search)
                       when 'recent'
                         conversation_finder.recent_conversations(@page, @search)
                       when 'older'
                         conversation_finder.older_conversations(@page, @search)
                       when 'all'
                         conversation_finder.all_conversations(@page, @search)
                       else
                         if @filter.present?
                           conversation_finder.filtered_conversations(@filter, @page, @search)
                         else
                           conversation_finder.accessible_conversations(@page, @search)
                         end
                       end
    end

    def set_conversation
      @conversation = Conversation.unscoped.find(params[:id])
    end

    def conversations_json_response
      {
        conversations: @conversations.map do |conversation|
          {
            id: conversation.id,
            html: render_to_string(
              partial: 'admin/conversations/conversation_card',
              locals: {
                conversation: conversation,
                highlight: conversation_has_unread_inbound?(conversation)
              },
              formats: [:html]
            )
          }
        end,
        count: @conversations.length,
        has_more: @conversations.length == ConversationFinderService::PER_PAGE
      }
    end

    def conversation_finder
      @conversation_finder ||= ConversationFinderService.new(current_user, Current.practice)
    end

    def conversation_params
      params.require(:conversation).permit(:name, internal_recipients_json: [])
    end
  end
end
