# frozen_string_literal: true

module Admin
  module GeneralSettings
    module Practices
      class PaymentProcessingController < Admin::ApplicationController
        before_action :set_practice

        def edit
          return if @practice.stripe_account_id.blank?

          @stripe_account = StripeClient.get_account(@practice)
          @bank_account = StripeClient.get_bank_account(@practice)
          @payouts = Stripe::Payout.list({ limit: 10 }, { stripe_account: @practice.stripe_account_id })
          @balance = Stripe::Balance.retrieve({ expand: ['instant_available.net_available'] },
                                              { stripe_account: @practice.stripe_account_id })
          @readers = Stripe::Terminal::Reader.list(
            { limit: 100 },
            { stripe_account: @practice.stripe_account_id }
          )
        end

        def update
          if @practice.update(practice_params)
            redirect_to edit_admin_general_settings_practice_opening_hours_path(@practice),
                        notice: 'Payment processing is updated!'
          else
            render :edit
          end
        end

        def connect_terminal
          location = Stripe::Terminal::Location.create({
                                                         display_name: params[:name],
                                                         address: {
                                                           line1: params[:line1],
                                                           line2: params[:line2].presence,
                                                           city: params[:city],
                                                           postal_code: params[:postcode],
                                                           country: 'GB'
                                                         }
                                                       }, { stripe_account: stripe_account_id(@practice) })

          terminal = Stripe::Terminal::Reader.create({
                                                       registration_code: params[:registration_code],
                                                       location: location.id
                                                     }, { stripe_account: stripe_account_id(@practice) })

          render json: {
            success: true,
            message: 'Terminal connected successfully!',
            terminal: {
              id: terminal.id,
              label: terminal.label,
              status: terminal.status
            }
          }
        rescue Stripe::StripeError => e
          render json: {
            success: false,
            message: e.message
          }, status: :unprocessable_entity
        rescue StandardError
          render json: {
            success: false,
            message: 'An unexpected error occurred while connecting the terminal.'
          }, status: :internal_server_error
        end

        private

        def practice_params
          {}
        end

        def set_practice
          @practice = Practice.find(params[:practice_id])
        end

        def stripe_account_id(practice)
          practice.stripe_account_id
        end
      end
    end
  end
end
