# frozen_string_literal: true

module Admin
  module GeneralSettings
    module Practices
      class InformationController < Admin::ApplicationController
        before_action :set_practice

        def edit; end

        def update
          if @practice.update(practice_params)
            redirect_to edit_admin_general_settings_practice_information_path(@practice),
                        notice: 'Practice information Updated!'
          else
            render :edit
          end
        end

        private

        def practice_params
          if params[:practice][:two_factor_enabled] == '0'
            params[:practice][:two_factor_enabled] = (@practice.two_factor_enabled ? '1' : '0')
          end

          params.require(:practice).permit(
            :name,
            :website,
            :num_surgeries,
            :address_line_1,
            :address_line_2,
            :city,
            :postcode,
            :email,
            :phone,
            :primary_color,
            :secondary_color,
            :tertiary_color,
            :azure_url,
            :azure_token,
            :image,
            :favicon_image,
            :logo,
            :two_factor_enabled
          )
        end

        def set_practice
          @practice = Practice.find(params[:practice_id])
        end
      end
    end
  end
end
