# frozen_string_literal: true

module Admin
  module GeneralSettings
    class TreatmentsController < Admin::ApplicationController
      before_action :set_treatment, only: %i[edit update destroy]
      before_action :set_treatment_categories, only: %i[new create edit update]

      def index
        @treatments = policy_scope(Treatment)
      end

      def new
        @treatment_categories = policy_scope(TreatmentCategory)
        @cot_payment_plan_treatments = @payment_plans.map do |plan|
          CotPaymentPlansTreatment.new(cot_payment_plan: plan)
        end
        @practices = current_user.practices
        @treatment = Treatment.new
      end

      def create
        @treatment = Treatment.new(treatment_params)

        if @treatment.save
          update_pricing
          redirect_to admin_general_settings_treatments_path, notice: 'Treatment Category added!'
        else
          flash[:alert] = "Error creating treatment: #{@treatment.errors.full_messages.join(', ')}"
          render :new
        end
      end

      def edit
        @treatment_categories = policy_scope(TreatmentCategory)
        @payment_plans = @payment_plans.where(practice: @treatment.practice)
        @cot_payment_plan_treatments = @payment_plans.map do |plan|
          CotPaymentPlansTreatment.find_or_initialize_by(cot_payment_plan_id: plan.id, treatment_id: @treatment.id)
        end
      end

      def update
        @cot_payment_plan_treatments = @payment_plans.map do |plan|
          CotPaymentPlansTreatment.find_or_initialize_by(cot_payment_plan_id: plan.id, treatment_id: @treatment.id)
        end
        if @treatment.update(treatment_params)
          update_pricing
          redirect_to edit_admin_general_settings_treatment_path(@treatment), notice: 'Treatment updated!'
        else
          flash[:alert] = "Error updating treatment: #{@treatment.errors.full_messages.join(', ')}"
          render :edit
        end
      end

      def destroy
        if @treatment.destroy
          redirect_to admin_general_settings_treatments_path,
                      notice: 'Treatment was successfully deleted.'
        else
          redirect_to admin_general_settings_treatments_path,
                      alert: 'Failed to delete treatment.'
        end
      end

      def select2_search
        ransack_params = {
          'patient_friendly_name_or_nomenclature_or_code_cont' => params[:q]
        }

        results = policy_scope(Treatment)
                  .ransack(ransack_params)
                  .result(distinct: true)
                  .limit(20)
                  .pluck(:id, :patient_friendly_name)
                  .map { |id, patient_friendly_name| { id: id, text: patient_friendly_name } }

        render json: { results: }, status: :ok
      end

      private

      def update_pricing
        params[:treatment][:cot_payment_plan_treatments]&.each do |plan_id, plan_data|
          cot_payment_plan_treatment = CotPaymentPlansTreatment.find_or_initialize_by(
            treatment_id: @treatment.id,
            cot_payment_plan_id: plan_id.to_i
          )

          raw = plan_data.to_unsafe_h.deep_symbolize_keys
          durations = raw.delete(:duration_data) || {}

          cot_payment_plan_treatment.data = plan_data.to_unsafe_h.deep_symbolize_keys
          cot_payment_plan_treatment.duration_data = durations
          cot_payment_plan_treatment.save!
        end
      end

      def set_treatment_categories
        @treatment_categories = policy_scope(TreatmentCategory)
        @practices = current_user.practices

        @payment_plans = policy_scope(CotPaymentPlan)
      end

      def treatment_params
        params.require(:treatment).permit(
          :code,
          :description,
          :nomenclature,
          :patient_description,
          :notes,
          :staff_description,
          :patient_friendly_name,
          :region,
          :appear_on_invoice,
          :missing_tooth,
          :base_chart_list,
          :remove_treatment_when_completed,
          :remove_tooth_when_completed,
          :treatment_folder,
          :duration,
          :bridge_treatment,
          :treatment_category_id,
          :practice_id,
          :available_for_recall
        )
      end

      def set_treatment
        @treatment = Treatment.find(params[:id])
      end
    end
  end
end
