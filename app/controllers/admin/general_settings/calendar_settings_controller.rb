# frozen_string_literal: true

module Admin
  module GeneralSettings
    class CalendarSettingsController < Admin::ApplicationController
      skip_before_action :verify_authenticity_token
      before_action :set_selected_practice, only: %i[show update generate_description_ai]

      def show
        @setting = CalendarSetting.where(practice: @selected_practice).first_or_create
        @practitioners = policy_scope(User).without_archived.clinicians
        @selected_date = params[:date].present? ? Date.parse(params[:date]) : Date.current

        @calendar_reserved_block_types = @setting
                                         .calendar_reserved_block_types
                                         .joins(:treatment)
                                         .includes(:practice, :treatment)
                                         .order('treatments.patient_friendly_name')

        @weekly_blocks = CalendarReservedBlock
                         .joins(:calendar_reserved_block_type)
                         .includes(:practitioner, :calendar_reserved_block_type)
                         .between_dates(@selected_date.beginning_of_week(:monday), @selected_date.end_of_week(:monday))
                         .where(calendar_reserved_block_types: { id: @calendar_reserved_block_types.pluck(:id) })
      end

      def update
        @setting = CalendarSetting.where(practice: @selected_practice).first_or_create
        @setting.assign_attributes(setting_params)

        if @setting.save
          respond_to do |format|
            format.html do
              redirect_to admin_general_settings_calendar_setting_path(selected_practice_id: @selected_practice.id),
                          notice: 'Calendar settings updated successfully.'
            end
            format.json do
              flash[:notice] = 'Calendar settings updated successfully.'
              render json: { success: true }
            end
          end
        else
          respond_to do |format|
            format.html do
              redirect_to admin_general_settings_calendar_setting_path(selected_practice_id: @selected_practice.id),
                          alert: @setting.errors.full_messages.to_sentence
              return
            end
            format.json { render json: { success: false, errors: @setting.errors.full_messages } }
          end
        end
      end

      def generate_description_ai
        treatment = Treatment.find_by(id: params[:treatment_id])

        return render json: { error: 'Treatment not found' }, status: :not_found if treatment.nil?

        render json: { description: Ai::TreatmentDescriptionGenerator.generate(treatment, @selected_practice) }
      end

      private

      def set_selected_practice
        @available_practices = current_user.practices.order(:name)

        if @available_practices.empty?
          redirect_to admin_general_settings_calendar_setting_path,
                      alert: 'No practices available.'
          return
        end

        @selected_practice =
          if params[:selected_practice_id].present?
            current_user.practices.find(params[:selected_practice_id].to_i)
          else
            current_user.practices.first
          end
      end

      def setting_params
        params.require(:calendar_setting).permit(
          :late_cancellation_window_hours,
          types: {},
          calendar_reserved_block_types_attributes: [
            :id, :_destroy, :treatment_id, :color, :no_patient_required, :indicates_new, :practice_id,
            :online_bookable, :deposit_required, :only_bookable_within_reserved_slots, :new_patient_deposit_size,
            :old_patient_deposit_size, :treatment_description,
            { calendar_reserved_blocks_attributes: %i[
              id _destroy
            ] },
            { user_ids: [] }
          ]
        )
      end
    end
  end
end
