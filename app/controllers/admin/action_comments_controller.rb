# frozen_string_literal: true

module Admin
  class ActionCommentsController < ApplicationController
    def index
      @action = Action.find(params[:action_id])
      # Only get parent comments (comments without ancestry) where comment field is not null and not empty
      @comments = @action.action_comments.includes(:user)
                         .where.not(comment: [nil, ''])
                         .where(ancestry: nil)
                         .order(created_at: :desc)

      respond_to do |format|
        format.html
        format.json do
          render json: {
            comments: @comments.map do |comment|
              {
                id: comment.id,
                text: comment.comment,
                created_at: comment.created_at,
                parent_id: comment.ancestry,
                user: {
                  id: comment.user.id,
                  name: comment.user.full_name,
                  initials: comment.user.full_name.split.map(&:first).join
                },
                replies: ActionComment.where(ancestry: comment.id.to_s).includes(:user).map do |reply|
                  {
                    id: reply.id,
                    text: reply.comment,
                    created_at: reply.created_at,
                    user: {
                      id: reply.user.id,
                      name: reply.user.full_name,
                      initials: reply.user.full_name.split.map(&:first).join
                    }
                  }
                end
              }
            end
          }
        end
      end
    end

    def create
      @comment = ActionComment.new(action_comment_params)
      @comment.user_id = current_user.id

      respond_to do |format|
        if @comment.save
          format.html do
            flash[:success] = 'Comment added'
            redirect_to request.referer
          end
          format.json do
            render json: {
              success: true,
              comment: {
                id: @comment.id,
                text: @comment.comment,
                created_at: @comment.created_at,
                parent_id: @comment.ancestry,
                user: {
                  id: @comment.user.id,
                  name: current_user.full_name,
                  initials: current_user.full_name.split.map(&:first).join
                }
              }
            }
          end
        else
          format.html do
            flash[:error] = ['Error adding comment', @comment.errors.first&.full_message]
            redirect_to request.referer
          end
          format.json do
            render json: { success: false, errors: @comment.errors.full_messages }, status: :unprocessable_entity
          end
        end
      end
    end

    private

    def action_comment_params
      params.require(:action_comment).permit(:action_id, :parent_id, :comment)
    end
  end
end
