# frozen_string_literal: true

module Admin
  module Crm
    class ActionsController < Admin::ApplicationController
      before_action :set_card

      def show
        @board = @card.crm_list.crm_board

        @cot_categories = CotCategory.where(practice_id: Current.practice_id)
                                     .select(:id, :name)
                                     .order(:name)

        @card_checklist_ids = @card.crm_checklists.pluck(:id)
        @all_checklists = CrmChecklist.joins(crm_board: :practice)
                                      .where(crm_boards: { practice_id: Current.practice_id })
                                      .select(:id, :title)

        user_ids = @card.members.present? ? @card.members.map { |m| m['user_id'] }.compact : []
        @card_team_member_ids = user_ids
        @all_team_members = User.joins(:practices_users)
                                .where(practices_users: { practice_id: Current.practice_id })
                                .select(:id, :first_name, :last_name)
                                .order(:first_name, :last_name)

        @custom_fields = @board.crm_custom_fields.order(position: :asc)

        field_values = @card.crm_custom_field_values.to_a
        @custom_field_values = field_values.each_with_object({}) do |field_value, hash|
          hash[field_value.crm_custom_field_id] = field_value.value
        end

        @card_activities = @card.card_activities.includes(user: { image_attachment: :blob })
        @card_comments = @card.card_comments.includes(user: { image_attachment: :blob }).where(ancestry: nil)

        @patient = @card.patient
        @actions = []

        if @patient.present?
          Rails.logger.debug "Loading actions for patient: #{@patient.id}"
          patient_actions = Action.where(actionable: @patient)
                                  .or(Action.where('actionable_type = ? AND actionable_id IN (?)',
                                                   'CalendarBooking',
                                                   @patient.calendar_bookings.pluck(:id)))
          Rails.logger.debug "Found #{patient_actions.count} patient actions"
          @actions.concat(patient_actions)
        end

        Rails.logger.debug "Loading actions for card: #{@card.id}"
        card_actions = Action.where(actionable: @card)
        Rails.logger.debug "Found #{card_actions.count} card actions"
        @actions.concat(card_actions)

        Rails.logger.debug "Total actions in system: #{Action.count}"
        Rails.logger.debug "Actions by type: #{Action.group(:action_type).count}"

        # Sort actions: overdue first, then future due dates, then no due date
        now = Time.zone.now
        @actions = @actions.sort_by do |a|
          if a.date_due.nil?
            [3, Time.zone.now + 100.years, -a.created_at.to_i] # No due date - last, newest first
          elsif a.date_due < now
            [1, a.date_due, -a.created_at.to_i] # Overdue - first, earliest due date first
          else
            [2, a.date_due, -a.created_at.to_i] # Future - middle, earliest due date first
          end
        end

        @tasks = @actions.select { |a| a.action_type == 'task' }
        @reminders = @actions.select { |a| a.action_type == 'reminder' }
        @alerts = @actions.select { |a| a.action_type == 'alert' }
        @callbacks = @actions.select { |a| a.action_type == 'callback' }
        @comments = @actions.select { |a| a.action_type == 'comment' }
        @complaints = @actions.select { |a| a.action_type == 'complaint' }

        Rails.logger.debug 'Action counts by type:'
        Rails.logger.debug "Tasks: #{@tasks.count}"
        Rails.logger.debug "Reminders: #{@reminders.count}"
        Rails.logger.debug "Alerts: #{@alerts.count}"
        Rails.logger.debug "Callbacks: #{@callbacks.count}"
        Rails.logger.debug "Comments: #{@comments.count}"
        Rails.logger.debug "Complaints: #{@complaints.count}"

        respond_to do |format|
          format.html { render 'admin/crm/cards/actions' }
          format.json do
            render json: {
              success: true,
              card: @card.as_json,
              actions: @actions.as_json
            }
          end
        end
      end

      private

      def set_card
        @card = CrmCard.includes(
          :patient,
          { crm_list: :crm_board },
          :crm_labels,
          :crm_custom_field_values
        ).find(params[:id])
      end
    end
  end
end
