# frozen_string_literal: true

module Admin
  class CalendarDayNotesController < Admin::ApplicationController
    before_action :set_calendar_day_note, only: %i[update]

    def update
      authorize :calendar_day_note, :update?

      @calendar_day_note.assign_attributes(calendar_day_note_params)

      if @calendar_day_note.save
        render json: @calendar_day_note, status: :ok
      else
        render json: @calendar_day_note.errors, status: :unprocessable_entity
      end
    end

    def create
      authorize :calendar_day_note, :create?

      @calendar_day_note = CalendarDayNote.new(calendar_day_note_params.merge(practice: Current.practice))

      if @calendar_day_note.save
        create_recurring_calendar_day_notes(recurrence_params, @calendar_day_note) if recurrence_params[:repeat_event] == '1'

        render json: @calendar_day_note, status: :created
      else
        render json: @calendar_day_note.errors, status: :unprocessable_entity
      end
    rescue Pundit::NotAuthorizedError
      render json: { error: 'You are not authorized to create calendar day notes' }, status: :forbidden
    end

    def destroy
      authorize :calendar_day_note, :destroy?

      @calendar_day_note = policy_scope(CalendarDayNote).find(params[:id])

      if params[:all_future] == '1' && @calendar_day_note.recurrent_identifier.present?
        policy_scope(CalendarDayNote)
          .where(recurrent_identifier: @calendar_day_note.recurrent_identifier)
          .where('date >= ?', @calendar_day_note.date)
          .find_each(&:destroy)
      elsif params[:all_related] == '1' && @calendar_day_note.recurrent_identifier.present?
        policy_scope(CalendarDayNote)
          .where(recurrent_identifier: @calendar_day_note.recurrent_identifier)
          .find_each(&:destroy)
      else
        @calendar_day_note.destroy
      end

      flash[:notice] = 'Note deleted successfully'
      head :no_content
    end

    private

    def calendar_day_note_params
      params.require(:calendar_day_note).permit(:note, :date, :user_id)
    end

    def recurrence_params
      params.require(:recurrence).permit(
        :repeat_event, :repeat_pattern, :end_repeat_date, :repeat_indefinitely,
        repeat_days: []
      )
    end

    def set_calendar_day_note
      @calendar_day_note = policy_scope(CalendarDayNote).find(params[:id])
    end

    # TODO: use Calendar::RecurrenceService here
    def create_recurring_calendar_day_notes(attrs, reference_note)
      start_date = reference_note.date
      recurrent_identifier = SecureRandom.uuid

      reference_note.update!(recurrent_identifier: recurrent_identifier)

      if attrs[:repeat_indefinitely] == '1'
        end_date = start_date + 1.year
      elsif attrs[:end_repeat_date].present?
        end_repeat_date = Date.parse(attrs[:end_repeat_date])
        end_date = end_repeat_date.end_of_day
      else
        end_date = (start_date + 3.months).end_of_day
      end

      repeat_days = (attrs[:repeat_days] || []).map(&:to_i)
      pattern = attrs[:repeat_pattern]

      current_date = start_date.to_date + 1.day
      week_counter = 0

      while current_date.end_of_day <= end_date
        if repeat_days.include?(current_date.wday) && (pattern == 'weekly' || (pattern == 'alternating' && week_counter.even?))
          CalendarDayNote.create!(
            practice_id: reference_note.practice_id,
            user_id: reference_note.user_id,
            note: reference_note.note,
            date: current_date,
            recurrent_identifier: recurrent_identifier
          )
        end

        current_date += 1.day
        week_counter += 1 if current_date.monday?
      end
    end
  end
end
