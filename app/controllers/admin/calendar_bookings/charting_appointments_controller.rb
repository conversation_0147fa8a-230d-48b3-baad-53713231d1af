# frozen_string_literal: true

module Admin
  module CalendarBookings
    class ChartingAppointmentsController < Admin::ApplicationController
      layout 'admin'

      before_action :set_filter_params, only: %i[upcoming]

      def upcoming
        @patient = policy_scope(Patient).find_by(id: params[:patient_id])

        if @patient.present?
          course_of_treatments_ids = @patient.course_of_treatments.accepted.without_archived.pluck(:id)
          @charting_appointments = policy_scope(ChartingAppointment)
                                   .joins(:course_of_treatment)
                                   .includes(
                                     :calendar_booking,
                                     :lab_works,
                                     :signature_requests,
                                     charted_treatments: [:treatment],
                                     dentist: [:image_attachment],
                                     course_of_treatment: [
                                       patient: [
                                         :medical_histories,
                                         :actions,
                                         { payment_plans: [payments: :refunds],
                                           payments: :refunds,
                                           invoices: [:invoice_items, { payments: :refunds }] }
                                       ]
                                     ]
                                   )
                                   .where(course_of_treatment_id: course_of_treatments_ids, calendar_booking_id: nil)
                                   .select('charting_appointments.*, course_of_treatment.created_at AS cot_created_at')
                                   .order('cot_created_at DESC, charting_appointments.position ASC')

          if params[:practitioner_id].present?
            @charting_appointments = @charting_appointments.where(dentist_id: [params[:practitioner_id], nil])
          end

          @charting_appointments = @charting_appointments.reject(&:completed?)
        else
          @charting_appointments = ChartingAppointment.none
        end

        render layout: false
      end

      private

      def set_filter_params
        @mode =
          if params[:mode].in?(%w[day week month])
            params[:mode].to_sym
          else
            :day
          end

        @date = params[:date] ? Date.parse(params[:date]) : Time.zone.today

        # TODO: refactor, a temporary fix for AJAX requests
        return unless params[:hidden_dentists].is_a?(String)

        params[:hidden_dentists] = begin
          JSON.parse(params[:hidden_dentists])
        rescue StandardError
          []
        end

        return unless params[:hidden_dentists].is_a?(String)

        params[:hidden_dentists] =
          begin
            JSON.parse(params[:hidden_dentists])
          rescue StandardError
            []
          end
      end
    end
  end
end
