# frozen_string_literal: true

module Admin
  class WhatsappTemplatesController < Admin::ApplicationController
    def create
      @template = WhatsappTemplate.new(whatsapp_template_params)
      if @template.save
        flash[:success] = 'Template created successfully!'
        redirect_to edit_admin_whatsapp_template_path(@template.id)
      else
        render :new
      end
    end

    def edit
      @template = WhatsappTemplate.find(params[:id])
      @practice = @template.practice
    end

    def update
      @template = WhatsappTemplate.find(params[:id])
      if @template.update(whatsapp_template_params)
        flash[:success] = 'Template update successfully!'
        redirect_to edit_admin_whatsapp_template_path(@template.id)
      else
        render :new
      end
    end

    def destroy
      @template = WhatsappTemplate.find(params[:id])
      @template.destroy
      flash[:success] = 'Template deleted successfully!'
      redirect_to edit_admin_general_settings_practice_path(@template.practice, tab: 'whatsapp_templates')
    end

    def submit
      @template = WhatsappTemplate.find(params[:id])

      if @template.submitted
        @template.resubmit
      else
        @template.submit
      end

      if @template.submitted
        flash[:success] = 'Template submitted successfully!'
      else
        flash[:error] = 'Template submission failed'
      end
      redirect_to edit_admin_whatsapp_template_path(@template.id)
    end

    private

    def whatsapp_template_params
      if params[:whatsapp_template][:whatsapp_components_attributes].present?
        params[:whatsapp_template][:whatsapp_components_attributes].each_value do |value|
          value[:text] = value[:text].gsub(/<[^>]*>/, '') if value[:text].present?
        end
      end

      params.require(:whatsapp_template).permit(
        :name,
        :automations_only,
        :category,
        :practice_id,
        whatsapp_components_attributes: [
          :id,
          :component_type,
          :text,
          :_destroy,
          { whatsapp_buttons_attributes: %i[id button_type text content _destroy] }
        ]
      )
    end
  end
end
