# frozen_string_literal: true

module Admin
  module CalendarBookingsHelper
    TIMELINE_ENTRY_HEIGHT = 28 # px
    NO_NOTES_AVAILABLE = 'No notes available.'

    def start_day_offset(date)
      Date.new(date.year, date.month, 1).wday + 1
    end

    def days_in_month(date)
      Date.new(date.year, date.month, -1).day
    end

    def format_date_label(date, mode)
      case mode
      when :month
        date.strftime('%B %Y') # E.g., September 2024
      when :week
        week_start = date.beginning_of_week(:monday)
        week_end = week_start + 6.days
        "Week of #{week_start.strftime('%-d %b %Y')} - #{week_end.strftime('%-d %b %Y')}"
      when :day
        date.strftime('%a, %B %-d, %Y') # E.g., Tue 9 September 2024
      end
    end

    def format_timeline_entry(date)
      date.utc.strftime('%-l.%M %p') # e.g. "9:00 AM"
    end

    def calendar_item_height_px(duration_in_minutes, _timeline, step = 5)
      duration_in_minutes / step * TIMELINE_ENTRY_HEIGHT
    end

    def calendar_item_top_px(seconds_since_midnight, timeline)
      timeline.count do |e|
        seconds_since_midnight > e[:start_time].in_time_zone.seconds_since_midnight
      end * TIMELINE_ENTRY_HEIGHT
    end

    def reserved_block_top_px(reserved_block, timeline)
      seconds_since_midnight = reserved_block.start_time.seconds_since_midnight
      above_entries = timeline.select { |entry| seconds_since_midnight >= entry[:end_time].seconds_since_midnight }

      # NOTE: ensure right position between 30 min marks
      pct_of_30_min_interval_passed = (seconds_since_midnight % 1800 / 1800.0)

      (above_entries.size.to_f + pct_of_30_min_interval_passed) * TIMELINE_ENTRY_HEIGHT
    end

    def horizontal_time_line_height_px(timeline)
      current_time_of_day = Time.now.in_time_zone('London').seconds_since_midnight
      above_entries = timeline.select { |entry| current_time_of_day > entry[:start_time].seconds_since_midnight }

      # NOTE: ensure right position between 5-min marks
      pct_of_5_min_interval_passed = (current_time_of_day % 300 / 300.0)

      (above_entries.size.to_f + pct_of_5_min_interval_passed) * TIMELINE_ENTRY_HEIGHT
    end

    def hex_to_rgba(hex_color, opacity)
      hex_color = hex_color.delete('#')
      r = hex_color[0..1].to_i(16)
      g = hex_color[2..3].to_i(16)
      b = hex_color[4..5].to_i(16)
      "rgba(#{r}, #{g}, #{b}, #{opacity})"
    end

    def duration_in_minutes_options_for_select(default_value: nil)
      opts = [
        ['5 minutes', 5],
        ['10 minutes', 10],
        ['15 minutes', 15],
        ['20 minutes', 20],
        ['25 minutes', 25],
        ['30 minutes', 30],
        ['35 minutes', 35],
        ['45 minutes', 45],
        ['50 minutes', 50],
        ['55 minutes', 55],
        ['1 hour', 60],
        ['1 hour 5 minutes', 65],
        ['1 hour 10 minutes', 70],
        ['1 hour 15 minutes', 75],
        ['1 hour 20 minutes', 80],
        ['1 hour 25 minutes', 85],
        ['1 hour 30 minutes', 90],
        ['1 hour 35 minutes', 95],
        ['1 hour 40 minutes', 100],
        ['1 hour 45 minutes', 105],
        ['1 hour 50 minutes', 110],
        ['1 hour 55 minutes', 115],
        ['2 hours', 120],
        ['2 hours 15 minutes', 135],
        ['2 hours 30 minutes', 150],
        ['2 hours 45 minutes', 165],
        ['3 hours', 180],
        ['3 hours 15 minutes', 195],
        ['3 hours 30 minutes', 210],
        ['3 hours 45 minutes', 225],
        ['4 hours', 240]
      ]

      options_for_select(opts, default_value)
    end

    def status_icon(calendar_booking, klass = '')
      case calendar_booking.status
      when 'scheduled'
        image_tag('icons/status-scheduled.svg', class: klass)
      when 'confirmation_requested'
        image_tag('icons/status-confirmation-requested.svg', class: klass)
      when 'confirmed'
        image_tag('icons/status-confirmed.svg', class: klass)
      when 'attended'
        image_tag('icons/status-attended.svg', class: klass)
      when 'in_surgery'
        image_tag('icons/status-in-surgery.svg', class: klass)
      when 'completed'
        image_tag('icons/status-completed.svg', class: klass)
      when 'running_late'
        image_tag('icons/status-running-late.svg', class: klass)
      when 'fta'
        image_tag('icons/status-fta.svg', class: klass)
      end
    end

    def formatted_booking_status(calendar_booking)
      mapping = {
        'scheduled' => 'Scheduled',
        'cancelled' => 'Cancelled',
        'confirmation_requested' => 'Confirmation Requested',
        'confirmed' => 'Confirmed',
        'attended' => 'Attended',
        'in_surgery' => 'In Surgery',
        'completed' => 'Completed',
        'running_late' => 'Running Late',
        'fta' => 'Failed to Attend'
      }

      mapping[calendar_booking.status]
    end

    def format_duration(duration_in_minutes)
      hours = (duration_in_minutes / 60).to_i
      minutes = (duration_in_minutes % 60).to_i

      duration_parts = []

      if duration_in_minutes >= 60
        duration_parts << "#{hours}h"
        duration_parts << "#{minutes}m" if minutes.positive?
      else
        duration_parts << "#{minutes}m"
      end

      duration_parts.join(' ')
    end

    # NOTE: round practice hours to nearest 30 minutes
    def closest_time(time_str, time_options)
      return time_options.first if time_str.blank?

      time_in_minutes = time_str.split(':').map(&:to_i).inject { |h, m| h * 60 + m }
      time_options_in_minutes = time_options.map { |t| t.split(':').map(&:to_i).inject { |h, m| h * 60 + m } }
      closest_minutes = time_options_in_minutes.min_by { |t| (t - time_in_minutes).abs }

      format(
        '%<hours>02d:%<minutes>02d',
        hours: closest_minutes / 60,
        minutes: closest_minutes % 60
      )
    end

    # TODO: move to a service
    def calculate_chair_time(mode, bookings, clinicians, date, practice)
      case mode
      when :month
        start_date = date.beginning_of_month
        end_date = date.end_of_month
      when :week
        start_date = date.beginning_of_week(:monday)
        end_date = start_date + 6.days
      when :day
        start_date = date.beginning_of_day
        end_date = date.end_of_day
      end

      shifts = Shift
               .where(user_id: clinicians.pluck(:id), practice_id: practice.id)
               .where('event_start <= ? AND event_end >= ?', end_date, start_date)

      bookings = bookings
                 .where(practice_id: practice.id)
                 .where('start_time <= ? AND end_time >= ?', end_date, start_date)

      total_available_time = 0
      total_booked_time = 0

      # NOTE: this horse shit below is needed because calendar bookings can overlap in dentally data
      clinicians.each do |clinician|
        clinician_shifts = shifts.select { |s| s.user_id == clinician.id }

        clinician_available_mins = clinician_shifts.flat_map do |shift|
          minute_array = []

          current = shift.event_start
          while current < shift.event_end
            minute_array << current
            current += 1.minute
          end

          minute_array
        end.uniq

        total_available_time += clinician_available_mins.size

        clinician_bookings = bookings.select { |b| b.practitioner_id == clinician.id && b.practice_id == practice.id }

        clinician_booked_mins = clinician_bookings.flat_map do |booking|
          minute_array = []

          current = booking.start_time
          while current < booking.end_time
            minute_array << current
            current += 1.minute
          end

          minute_array
        end.uniq

        # NOTE: only track mins which are covered by shifts
        total_booked_time += (clinician_booked_mins & clinician_available_mins).size
      end

      total_chair_time = begin
        (total_booked_time.to_f / total_available_time) * 100
      rescue StandardError
        0.0
      end
      begin
        total_chair_time.round(0)
      rescue StandardError
        0
      end
    end

    def available_event_colors
      [
        ['Light Silver', '#D8D8D8'],
        ['Light Gray', '#c5bbb1'],
        ['Gray Green', '#aab7ac'],
        ['Light Blue', '#c4dffe'],
        ['Light Blue Gray', '#bdcfdb'],
        ['Pale Green', '#d2e1b7'],
        ['Pale Yellow', '#fbe199'],
        ['Peach', '#f7b17c'],
        ['Blush Pink', '#ebcac7'],
        ['Lavender Gray', '#bdb3cc']
      ]
    end

    def current_time_line_left(mode, date)
      now      = Time.zone.now.in_time_zone('London')
      minutes  = now.hour * 60 + now.min
      return unless minutes.between?(8 * 60, 22 * 60)

      shift_pct = (minutes - 8 * 60) / (14.0 * 60) * 100

      case mode
      when :day
        return unless date.to_date == now.to_date

        shift_pct
      when :week
        day_idx = (now.to_date - date.beginning_of_week(:monday)).to_i
        return unless day_idx.between?(0, 6)

        col = 100 / 7.0
        day_idx * col + shift_pct * col / 100
      when :month
        day_idx = (now.to_date - date.beginning_of_month).to_i
        return unless day_idx.between?(0, 30)

        col = 100 / 31.0
        day_idx * col + shift_pct * col / 100
      end
    end
  end
end
