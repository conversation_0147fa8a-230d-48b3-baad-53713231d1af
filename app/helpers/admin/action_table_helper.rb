# frozen_string_literal: true

module Admin
  module ActionTableHelper
    def actions_table_columns
      [
        { label: 'Action', content: ->(action) { action_name_column(action) }, class: 'flex-1' },
        { label: 'Type', content: ->(action) { action_type_column(action) }, class: 'w-40 text-left' },
        { label: 'Assigned By → To', content: ->(action) { action_assignment_column(action) }, class: 'w-48 text-left' },
        { label: 'Priority', content: ->(action) { action_priority_column(action) }, class: 'w-40 text-left' },
        {
          label: 'Due Date',
          content: ->(action) { action.date_due&.strftime('%d/%m/%Y') || 'No due date' },
          class: 'w-40'
        },
        {
          label: 'Progress',
          content: ->(action) { action_progress_bar(action) },
          class: 'w-48 text-left'
        },
        {
          label: 'Status',
          content: ->(action) { action_status_column(action) },
          class: 'w-40 text-left text-sm text-gray-500'
        }
      ]
    end

    def table_column(label, options = {})
      {
        label: label,
        content: options[:content],
        attribute: options[:attribute],
        method: options[:method],
        class: options[:class],
        mobile_hidden: options[:mobile_hidden]
      }
    end

    def action_name_column(action)
      icon_class = action_icon_class_for_table(action.action_type, completed: action.completed?)

      content_tag(:div, class: 'flex items-center') do
        content_tag(:div, class: 'flex-shrink-0 mr-3') do
          content_tag(:i, '', class: "#{icon_class} h-5 w-5")
        end +
          content_tag(:div, class: 'flex flex-col min-w-0') do
            title_text = action.title.presence || action.action_type.capitalize
            title_class = if action.completed?
                            'text-sm font-medium text-gray-500 line-through truncate'
                          else
                            'text-sm font-medium text-gray-900 truncate'
                          end
            content_tag(:span, title_text, class: title_class) +
              content_tag(:span, action.description.present? ? action.description.truncate(50) : 'No description',
                          class: 'text-xs text-gray-500')
          end
      end
    end

    def action_type_column(action)
      color_class = action_type_color_class(action.action_type, completed: action.completed?)
      label = if action.completed?
                %w[complaint alert].include?(action.action_type) ? 'Resolved' : 'Completed'
              else
                action.action_type.capitalize
              end
      content_tag(:span, label, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{color_class}")
    end

    def action_assignment_column(action)
      content_tag(:div, class: 'flex items-center space-x-2') do
        created_by_avatar(action) + assignment_arrow + assigned_to_avatars(action)
      end
    end

    def user_avatar_image(user, width, height, tippy: false)
      image_tag(
        user&.image&.attached? ? user&.image&.url : 'default-avatar.webp',
        style: "width: #{width}px; height: #{height}px; border-radius: 100%;",
        class: 'user-avatar',
        data: tippy ? { "tippy-content": user&.full_name } : {}
      )
    end

    def action_priority_column(action)
      action_priority_badge(action)
    end

    def action_status_column(action)
      if action.completed?
        %w[complaint alert].include?(action.action_type) ? 'Resolved' : 'Completed'
      elsif action.date_due.present?
        days_overdue = (Date.current - action.date_due.to_date).to_i
        days_overdue.positive? ? "#{days_overdue}d overdue" : 'Due soon'
      else
        'Active'
      end
    end

    def action_progress_bar(action)
      return content_tag(:span, 'No due date', class: 'text-xs text-gray-500') unless action.date_due

      created_at = action.created_at
      due_date = action.date_due.end_of_day
      current_time = Time.current

      # Calculate total duration and elapsed time
      total_duration = due_date - created_at
      elapsed_time = current_time - created_at

      # Calculate progress percentage (0-100)
      progress_percentage = if total_duration.positive?
                              [(elapsed_time / total_duration * 100).round, 100].min
                            else
                              100
                            end

      # Determine if overdue
      is_overdue = current_time > due_date

      # Calculate time remaining or overdue
      if is_overdue
        time_diff = current_time - due_date
        time_text = "#{pluralize(time_diff.to_i / 1.day, 'day')} overdue"
        bar_color = 'bg-red-200'
        text_color = 'text-red-500'
      else
        time_diff = due_date - current_time
        days_remaining = (time_diff / 1.day).ceil
        time_text = "#{pluralize(days_remaining, 'day')} left"
        bar_color = 'bg-green-200'
        text_color = 'text-green-500'
      end

      content_tag(:div, class: 'w-full') do
        concat(content_tag(:div, time_text, class: "text-xs font-medium #{text_color} mb-1"))
        concat(
          content_tag(:div, class: 'w-full bg-gray-100 rounded-full h-2') do
            content_tag(:div, '',
                        class: "h-2 rounded-full #{bar_color} transition-all duration-300",
                        style: "width: #{progress_percentage}%")
          end
        )
      end
    end

    def action_action_buttons(action)
      content_tag(:div, class: 'flex items-center gap-2 group') do
        concat(edit_action_button(action))
        concat(comments_action_button(action))
        concat(timeline_action_button(action))
        concat(complete_action_button(action)) unless action.completed?
      end
    end

    private

    def created_by_avatar(action)
      if action.created_by.present?
        content_tag(:div, class: 'flex items-center') do
          user_avatar_image(action.created_by, 24, 24, tippy: true)
        end
      else
        practice_to_display = determine_practice_for_action_notification
        if practice_to_display
          content_tag(:div, class: 'w-6 h-6 rounded-full overflow-hidden border border-gray-200 flex-shrink-0') do
            if practice_to_display.logo.attached?
              image_tag(practice_to_display.logo, alt: practice_to_display.name, class: 'w-full h-full object-cover')
            else
              content_tag(:div, class: 'w-full h-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium text-xs') do
                practice_to_display.name&.first&.upcase || '?'
              end
            end
          end
        else
          content_tag(:div, class: 'w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center') do
            content_tag(:span, '?', class: 'text-xs text-gray-500')
          end
        end
      end
    end

    def assignment_arrow
      content_tag(:i, '', class: 'fa-light fa-arrow-right text-gray-400 text-xs')
    end

    def assigned_to_avatars(action)
      if action.assigned_to.any?
        content_tag(:div, class: 'flex -space-x-1') do
          avatar_elements = action.assigned_to.limit(3).map do |user|
            content_tag(:div, class: 'relative') do
              user_avatar_image(user, 24, 24, tippy: true)
            end
          end

          overflow_element = if action.assigned_to.count > 3
                               overflow_avatar_count(action.assigned_to.count - 3)
                             else
                               ''.html_safe
                             end

          safe_join(avatar_elements) + overflow_element
        end
      else
        content_tag(:div, class: 'w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center') do
          content_tag(:span, '?', class: 'text-xs text-gray-500')
        end
      end
    end

    def determine_practice_for_action_notification
      user_practices = Current.user&.practices || []

      # If user is assigned to only one practice: show that practice's logo
      return user_practices.first if user_practices.count == 1

      # If user is assigned to multiple practices:
      if user_practices.count > 1
        # If viewing a specific practice (practice filter is active): show the logo of the currently filtered practice
        if Current.practice_id.present?
          current_practice = user_practices.find { |p| p.id == Current.practice_id }
          return current_practice if current_practice
        end

        # If viewing "All" practices (no practice filter active): show the logo of the first practice in the user's assigned practice list
        return user_practices.first
      end

      # Fallback: no practices assigned
      nil
    end

    def overflow_avatar_count(count)
      overflow_class = 'w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center ' \
                       'border-2 border-white'
      content_tag(:div, class: overflow_class) do
        content_tag(:span, "+#{count}", class: 'text-xs text-gray-600')
      end
    end

    def edit_action_button(action)
      content_tag(:button,
                  class: 'inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-green-700 ' \
                         'bg-green-100 rounded-lg hover:bg-green-200 transition-colors edit-action-btn w-8 h-8',
                  data: { action_id: action.id, tippy_content: 'Edit Action' }) do
        content_tag(:i, '', class: 'fas fa-edit h-3.5 w-3.5')
      end
    end

    def comments_action_button(action)
      content_tag(:button,
                  class: 'relative inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-blue-700 ' \
                         'bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors comments-toggle w-8 h-8',
                  data: { action_id: action.id, tippy_content: 'View Comments' }) do
        concat(content_tag(:i, '', class: 'fas fa-comments h-3.5 w-3.5'))
        if action.action_comments.count.positive?
          concat(content_tag(:span, action.action_comments.count,
                             class: 'absolute -top-1 -right-1 inline-flex items-center justify-center ' \
                                    'min-w-[16px] h-4 rounded-full bg-red-500 text-white text-xs'))
        end
      end
    end

    def timeline_action_button(action)
      content_tag(:button,
                  class: 'inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-purple-700 ' \
                         'bg-purple-100 rounded-lg hover:bg-purple-200 transition-colors timeline-toggle w-8 h-8',
                  data: { action_id: action.id, tippy_content: 'View Timeline' }) do
        content_tag(:i, '', class: 'fas fa-clock h-3.5 w-3.5')
      end
    end

    def complete_action_button(action)
      tooltip_text = if %w[complaint alert].include?(action.action_type)
                       'Resolve Action'
                     else
                       'Complete Action'
                     end

      content_tag(:button,
                  class: 'inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-emerald-700 ' \
                         'bg-emerald-100 rounded-lg hover:bg-emerald-200 transition-colors complete-action-btn w-8 h-8',
                  data: { action_id: action.id, action_type: action.action_type, tippy_content: tooltip_text }) do
        content_tag(:i, '', class: 'fas fa-check h-3.5 w-3.5')
      end
    end
  end
end
