# frozen_string_literal: true

module Admin
  module Event<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    def event_logs_table_columns
      [
        table_column('Type', { content: ->(event_log) { event_log_type_column(event_log) } }),
        table_column('Action', { content: ->(event_log) { event_log_action_column(event_log) } }),
        table_column('Event Details', { content: ->(event_log) { event_log_details_column(event_log) } }),
        table_column('Caused By', { content: ->(event_log) { event_log_caused_by_column(event_log) } }),
        table_column('Time', { content: ->(event_log) { event_log_time_column(event_log) } })
      ]
    end

    def event_log_object_link(log)
      case log.object_type
      when 'Patient'
        link_to 'Patient Information', admin_patient_path(log.object_id)
      when 'MedicalHistory'
        link_to 'Medical History', admin_patient_medical_histories_path(log.patient_id)
      when 'Payment'
        link_to "Payment ##{log.object_id}", admin_patient_account_path(log.patient_id)
      when 'ChartedTreatment'
        link_to "Charted Treatment ##{log.object_id}", admin_patient_charting_path(log.patient_id)
      when 'ChartingAppointment'
        link_to "Charting Appointment ##{log.object_id}", admin_patient_charting_path(log.patient_id)
      when 'CourseOfTreatment'
        link_to "Course of Treatment ##{log.object_id}", admin_patient_charting_path(log.patient_id, course_of_treatment_id: log.object_id)
      when 'Invoice'
        link_to "Invoice ##{log.object_id}", admin_patient_account_path(log.patient_id)
      when 'LabWork'
        link_to "Lab Work ##{log.object_id}", admin_lab_work_path(log.object_id)
      when 'SignatureRequest'
        link_to "Signature Request ##{log.object_id}", admin_signature_request_path(log.object_id)
      when 'SignatureVerification'
        verification = SignatureVerification.find(log.object_id)
        link_to "Signature Request Verification ##{log.object_id}", admin_signature_request_path(verification.signature_request_id)
      when 'TreatmentPlanOption'
        link_to "Treatment Plan Option ##{log.object_id}", admin_treatment_plans_path
      when 'TreatmentPlan'
        link_to "Treatment Plan ##{log.object_id}", admin_treatment_plans_path
      end
    end

    def event_log_action_label(log)
      label_action, label_css_type =
        case log.event_type
        when 'object_create'
          %w[Create primary]
        when 'object_update'
          %w[Update warning]
        when 'object_delete'
          %w[Delete danger]
        else
          %w[Event success]
        end

      content_tag :span, label_action, class: "label label-#{label_css_type}"
    end

    def changed_by_name(log)
      log.user_name.presence || '<em>System</em>'.html_safe
    end

    def event_log_value_added?(value_before, value_after)
      value_before.to_s.blank? && value_after.to_s.present?
    end

    def event_log_value_updated?(value_before, value_after)
      value_before.to_s.present? && value_after.to_s.present?
    end

    def event_log_value_removed?(value_before, value_after)
      value_before.to_s.present? && value_after.to_s.blank?
    end

    def event_log_attribute_formatted_value(attribute, value)
      if attribute.ends_with?('_at')
        DateTime.strptime(value, '%FT%T.%L%:z').in_time_zone
      else
        value
      end
    end

    private

    def event_log_type_column(event_log)
      content_tag(:div, class: 'flex items-center space-x-3') do
        event_log_file_icon +
          event_log_type_info(event_log)
      end
    end

    def event_log_file_icon
      content_tag(:div, class: 'p-2 bg-gray-100 rounded-lg') do
        svg_attrs = {
          xmlns: 'http://www.w3.org/2000/svg', width: '24', height: '24', viewBox: '0 0 24 24',
          fill: 'none', stroke: 'currentColor', 'stroke-width': '2', 'stroke-linecap': 'round',
          'stroke-linejoin': 'round', class: 'lucide lucide-file-text h-4 w-4 text-gray-600'
        }
        content_tag(:svg, svg_attrs) do
          content_tag(:path, '', d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z') +
            content_tag(:path, '', d: 'M14 2v4a2 2 0 0 0 2 2h4') +
            content_tag(:path, '', d: 'M10 9H8') +
            content_tag(:path, '', d: 'M16 13H8') +
            content_tag(:path, '', d: 'M16 17H8')
        end
      end
    end

    def event_log_type_info(event_log)
      content_tag(:div) do
        badge_color = case event_log.event_type
                      when 'object_delete'
                        'bg-red-100 text-red-800'
                      when 'object_create'
                        'bg-green-100 text-green-800'
                      else
                        'bg-yellow-100 text-yellow-800'
                      end

        badge_classes = 'inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold ' \
                       'transition-colors focus:outline-none focus:ring-2 focus:ring-ring ' \
                       "focus:ring-offset-2 mt-1 text-xs #{badge_color}"

        content_tag(:div, event_log_object_link(event_log), class: 'font-medium text-gray-900 text-sm') +
          content_tag(:div, class: badge_classes, 'data-v0-t': 'badge') do
            if event_log.event_type == 'object_delete'
              'high'
            else
              (event_log.event_type == 'object_create' ? 'low' : 'medium')
            end
          end
      end
    end

    def event_log_action_column(event_log)
      badge_color = case event_log.event_type
                    when 'object_delete'
                      'bg-red-50 text-red-700 border-red-200'
                    when 'object_create'
                      'bg-green-50 text-green-700 border-green-200'
                    else
                      'bg-blue-50 text-blue-700 border-blue-200'
                    end

      badge_classes = 'inline-flex items-center rounded-full border px-2.5 py-0.5 ' \
                     'transition-colors focus:outline-none focus:ring-2 focus:ring-ring ' \
                     "focus:ring-offset-2 #{badge_color} text-xs font-medium"

      content_tag(:div, class: badge_classes, 'data-v0-t': 'badge') do
        event_log_action_icon(event_log) +
          event_log.event_type.gsub('object_', '').capitalize
      end
    end

    def event_log_action_icon(event_log)
      content_tag(:span, class: 'mr-1') do
        case event_log.event_type
        when 'object_delete'
          event_log_delete_icon
        when 'object_create'
          event_log_create_icon
        else
          event_log_update_icon
        end
      end
    end

    def event_log_delete_icon
      svg_attrs = {
        xmlns: 'http://www.w3.org/2000/svg', width: '24', height: '24', viewBox: '0 0 24 24',
        fill: 'none', stroke: 'currentColor', 'stroke-width': '2', 'stroke-linecap': 'round',
        'stroke-linejoin': 'round', class: 'lucide lucide-trash2 h-4 w-4'
      }
      content_tag(:svg, svg_attrs) do
        content_tag(:path, '', d: 'M3 6h18') +
          content_tag(:path, '', d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6') +
          content_tag(:path, '', d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2') +
          content_tag(:line, '', x1: '10', x2: '10', y1: '11', y2: '17') +
          content_tag(:line, '', x1: '14', x2: '14', y1: '11', y2: '17')
      end
    end

    def event_log_create_icon
      svg_attrs = {
        xmlns: 'http://www.w3.org/2000/svg', width: '24', height: '24', viewBox: '0 0 24 24',
        fill: 'none', stroke: 'currentColor', 'stroke-width': '2', 'stroke-linecap': 'round',
        'stroke-linejoin': 'round', class: 'lucide lucide-plus h-4 w-4'
      }
      content_tag(:svg, svg_attrs) do
        content_tag(:path, '', d: 'M5 12h14') +
          content_tag(:path, '', d: 'M12 5v14')
      end
    end

    def event_log_update_icon
      svg_attrs = {
        xmlns: 'http://www.w3.org/2000/svg', width: '24', height: '24', viewBox: '0 0 24 24',
        fill: 'none', stroke: 'currentColor', 'stroke-width': '2', 'stroke-linecap': 'round',
        'stroke-linejoin': 'round', class: 'lucide lucide-pen-line h-4 w-4'
      }
      content_tag(:svg, svg_attrs) do
        content_tag(:path, '', d: 'M12 20h9') +
          content_tag(:path, '', d: 'M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z')
      end
    end

    def event_log_details_column(event_log)
      content_tag(:div, class: 'text-sm text-gray-900 max-w-md') do
        content_tag(:div, class: 'font-mono text-xs bg-white p-2 rounded-lg border border-gray-200 shadow-sm') do
          render partial: event_log.partial_path, locals: { log: event_log, changes: event_log.data }
        end
      end
    end

    def event_log_caused_by_column(event_log)
      content_tag(:div, class: 'flex items-center space-x-2') do
        event_log_user_avatar(event_log) +
          content_tag(:span, changed_by_name(event_log), class: 'text-sm font-medium text-gray-900')
      end
    end

    def event_log_user_avatar(event_log)
      content_tag(:div, class: 'relative flex shrink-0 overflow-hidden rounded-full') do
        if event_log.user.present?
          render 'layouts/shared/user_avatar', user: event_log.user, width: 24, height: 24
        else
          div_classes = 'h-6 w-6 flex items-center justify-center bg-blue-100 text-blue-500 ' \
                        'font-semibold text-xs rounded-full'
          content_tag(:div, 'S', class: div_classes)
        end
      end
    end

    def event_log_time_column(event_log)
      content_tag(:div, class: 'flex items-center justify-between') do
        time_classes = 'ml-2 px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md'
        content_tag(:div, l(event_log.created_at, format: '%b %d, %Y %H:%M'), class: 'text-sm text-gray-600') +
          content_tag(:div, "#{time_ago_in_words(event_log.created_at)} ago", class: time_classes)
      end
    end
  end
end
