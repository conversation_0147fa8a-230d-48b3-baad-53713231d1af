# frozen_string_literal: true

module Admin
  module ChartingAppoint<PERSON><PERSON><PERSON><PERSON>
    def booking_duration_in_words(start_time, end_time)
      seconds = (end_time - start_time).to_i
      hours = seconds / 3600
      minutes = (seconds % 3600) / 60
      result = []
      result << "#{hours} hour#{'s' if hours != 1}" if hours.positive?
      result << "#{minutes} minute#{'s' if minutes != 1}" if minutes.positive?
      result.join(' ')
    end

    def bookable_after(charting_appointment)
      return if charting_appointment.time_until_booking_count.zero? || charting_appointment.time_until_booking_unit.blank?

      "#{charting_appointment.time_until_booking_count} #{charting_appointment.time_until_booking_unit}"
    end

    def status_badge_class(level)
      {
        'positive' => 'bg-green-100 text-green-700',
        'negative' => 'bg-red-100 text-red-700',
        'pending' => 'bg-amber-100 text-amber-700'
      }[level] || 'bg-gray-100 text-gray-500'
    end

    def charting_appointments_table_columns(_patient)
      [
        table_column('', {
                       content: lambda { |row|
                         if row[:type] == :unbooked
                           content_tag(:div, class: 'yellow-bg rounded-full flex items-center justify-center',
                                             style: 'width: 28px; height: 28px;', 'data-tippy-content': 'Unbooked') do
                             content_tag(:i, '', class: 'fa-light fa-calendar-circle-plus', style: 'font-size: 10px;')
                           end
                         else
                           booking = row[:record]
                           case booking.status
                           when 'completed'
                             content_tag(:div, class: 'pale-green-bg rounded-full flex items-center justify-center',
                                               style: 'width: 28px; height: 28px;', 'data-tippy-content': 'Completed') do
                               content_tag(:i, '', class: 'fa-light fa-circle-check')
                             end
                           when 'cancelled'
                             content_tag(:div, class: 'deep-red-bg rounded-full flex items-center justify-center',
                                               style: 'width: 28px; height: 28px;', 'data-tippy-content': 'Cancelled') do
                               content_tag(:i, '', class: 'fa-light fa-xmark', style: 'color: white !important;')
                             end
                           when 'fta'
                             content_tag(:div, class: 'light-purple-bg rounded-full flex items-center justify-center',
                                               style: 'width: 28px; height: 28px;', 'data-tippy-content': 'Missed') do
                               content_tag(:i, '', class: 'fa-light fa-face-sad-sweat', style: 'color: #151515 !important;')
                             end
                           else
                             content_tag(:div, class: 'peach-orange-bg rounded-full flex items-center justify-center',
                                               style: 'width: 28px; height: 28px;', 'data-tippy-content': 'Upcoming') do
                               image_tag('icons/calendar.svg', style: 'width: 16px; height: 16px;')
                             end
                           end
                         end
                       }
                     }),
        table_column('Date', {
                       content: lambda { |row|
                         if row[:type] == :unbooked
                           'Unbooked'
                         else
                           booking = row[:record]
                           link_to booking.start_time.strftime('%d/%m/%Y - %H:%M'),
                                   staff_calendar_admin_calendar_bookings_path(date: booking.start_time.strftime('%Y-%m-%d'),
                                                                               highlight_id: booking.id),
                                   class: 'text-decoration-none'
                         end
                       }
                     }),
        table_column('Appointment Reason', {
                       content: lambda { |row|
                         if row[:type] == :unbooked
                           appt = row[:record]
                           appt.appointment_type || '-'
                         else
                           booking = row[:record]
                           if booking.treatment
                             booking.treatment.patient_friendly_name || '-'
                           elsif booking.charting_appointments.present?
                             booking.charting_appointments.first.appointment_type || '-'
                           else
                             '-'
                           end
                         end
                       }
                     }),
        table_column('Practitioner', {
                       content: lambda { |row|
                         if row[:type] == :unbooked
                           appt = row[:record]
                           if appt.dentist.present?
                             content_tag(:div, class: 'flex gap-2 items-center',
                                               style: 'white-space: nowrap; overflow: hidden; text-overflow: ellipsis;') do
                               render('layouts/shared/user_avatar', user: appt.dentist, width: 32, height: 32, tippy: true) +
                                 content_tag(:span, appt.dentist.full_name,
                                             class: 'px-2 py-1 text-[12px] rounded-full peach-orange-bg text-gray-800')
                             end
                           else
                             '-'
                           end
                         else
                           booking = row[:record]
                           content_tag(:div, class: 'flex gap-2 items-center',
                                             style: 'white-space: nowrap; overflow: hidden; text-overflow: ellipsis;') do
                             render('layouts/shared/user_avatar', user: booking.practitioner, width: 32, height: 32, tippy: true) +
                               content_tag(:span, booking.practitioner.full_name,
                                           class: 'px-2 py-1 text-[12px] rounded-full peach-orange-bg text-gray-800')
                           end
                         end
                       },
                       class: 'whitespace-nowrap min-w-[180px]'
                     }),
        table_column('Booked by', {
                       content: lambda { |row|
                         if row[:type] == :unbooked
                           '-'
                         else
                           booking = row[:record]
                           if booking.booked_by.present?
                             render('layouts/shared/user_avatar', user: booking.booked_by, width: 32, height: 32, tippy: true)
                           else
                             '-'
                           end
                         end
                       },
                       class: 'whitespace-nowrap min-w-[50px]'
                     }),
        table_column('Duration', {
                       content: lambda { |row|
                         if row[:type] == :unbooked
                           appt = row[:record]
                           if appt.charted_treatments.present?
                             booking_duration_in_words(0, appt.charted_treatments.sum do |ct|
                                                            ct.duration || ct.treatment.duration || 0
                                                          end.minutes)
                           else
                             '-'
                           end
                         else
                           booking = row[:record]
                           booking_duration_in_words(booking.start_time, booking.end_time)
                         end
                       }
                     }),
        table_column('', {
                       content: lambda { |row|
                         if row[:type] == :unbooked
                           appt = row[:record]
                           render('admin/calendar_bookings/staff_calendar/icons_group',
                                  record: appt,
                                  icon_klass: 'appointment-row-icon')
                         else
                           booking = row[:record]
                           render('admin/calendar_bookings/staff_calendar/icons_group',
                                  record: booking.charting_appointments.first,
                                  icon_klass: 'appointment-row-icon')
                         end
                       },
                       class: 'whitespace-nowrap min-w-[120px]'
                     }),
        table_column('Late Cancellation', {
                       content: lambda { |row|
                         if row[:type] == :unbooked
                           '-'
                         else
                           booking = row[:record]
                           if booking.cancelled?
                             booking.late_cancellation? ? 'Yes' : 'No'
                           else
                             '-'
                           end
                         end
                       }
                     }),
        table_column('Status', {
                       content: lambda { |row|
                         if row[:type] == :unbooked
                           'Unbooked'
                         else
                           booking = row[:record]
                           formatted_booking_status(booking)
                         end
                       }
                     }),
        table_column('Recall Appointment', {
                       content: lambda { |row|
                         if row[:type] == :unbooked
                           appt = row[:record]
                           appt&.calendar_booking&.recall.present? ? 'Yes' : 'No'
                         else
                           booking = row[:record]
                           booking&.recall.present? ? 'Yes' : 'No'
                         end
                       }
                     })
      ]
    end
  end
end
