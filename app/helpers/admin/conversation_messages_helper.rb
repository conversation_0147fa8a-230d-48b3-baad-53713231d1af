# frozen_string_literal: true

module Admin
  module ConversationMessagesHelper
    def build_message(conversation, sender, user_id, content, options = {})
      lab_work_id = options[:lab_work_id]
      template_id = options[:template_id]
      template_data = options[:template_data]
      stannp_practice = options[:stannp_practice]
      email_subject = options[:email_subject]

      # Determine the source based on the sender's account type or stannp_practice
      source = if stannp_practice.present?
                 'stannp'
               elsif sender.present?
                 sender.account_type
               else
                 'sms' # Default fallback
               end

      if sender&.account_type == 'whatsapp' && template_id.present?
        template, data = *fetch_whatsapp_template(conversation, template_id, template_data)
        message = conversation.conversation_messages.new(
          content: template.to_html(*data),
          message_type: options[:message_type] || 'outbound',
          author_id: user_id,
          from: sender.identifier,
          lab_work_id: lab_work_id,
          source: source,
          delivery_status: 'pending',
          email_subject: email_subject
        )
        [message, template, data]
      else
        message = conversation.conversation_messages.new(
          content: content,
          message_type: options[:message_type] || 'outbound',
          delivery_status: 'pending',
          author_id: user_id,
          from: sender&.identifier,
          lab_work_id: lab_work_id,
          source: source,
          email_subject: email_subject
        )
        [message, nil, nil]
      end
    end

    def push_message(message, session_id)
      pusher = PusherService.new
      pusher.trigger(
        "private-conversation-#{message.conversation.id}",
        'new_message',
        {
          message_id: message.id,
          conversation_id: message.conversation.id,
          exclude_sessions: [session_id]
        }
      )
    end

    def send_message(conversation, message, sender, _identifier, template, data, attachment)
      # For internal notes or internal conversations, just return success
      if conversation.internal? || message.message_type == 'internal'
        {
          success: true,
          html: render_message(message)
        }
      elsif sender&.account_type == 'email'
        # Handle email differently from other message types
        begin
          # Assuming conversation has a method to get recipient's email
          recipient_email = conversation.to('email')

          return { success: false, errors: ['Cannot send to user via email - no email address found'] } if recipient_email.blank?

          mailer_response = PatientMailer.email_message(
            conversation.patient,
            message.email_subject,
            message.email_subject,
            message.content,
            attachment,
            sender
          ).deliver_now

          message.update(external_id: mailer_response.message_id)

          {
            success: true,
            html: render_message(message)
          }
        rescue StandardError => e
          Rails.logger.error("Email sending error: #{e.message}")
          { success: false, errors: ["Failed to send email: #{e.message}"] }
        end
      else
        # Existing Sinch messaging for WhatsApp and SMS
        messaging_service = Sinch::MessagingService.new(conversation.practice)
        source = sinch_source(sender&.account_type)
        to = conversation.to(source)

        if to.blank?
          { success: false, errors: ['Cannot send to user on specified service'] }
        else
          message_content = ActionController::Base.helpers.strip_tags(message.content).gsub('&nbsp;', ' ')

          result =
            if sender&.account_type == 'whatsapp' && template.present?
              messaging_service.send_message(
                sender.identifier,
                'WHATSAPP',
                to,
                template.to_message(*data)
              )
            else
              messaging_service.send_message(
                sender.identifier,
                source,
                to,
                message_content
              )
            end

          if result['error']
            {
              success: false,
              errors: [result['error']]
            }
          else
            message.update(external_id: result['message_id'])
            {
              success: true,
              html: render_message(message)
            }
          end
        end
      end
    end

    def render_message(message)
      ApplicationController.render(partial: 'admin/conversations/message',
                                   locals: { message: message, pusher: false,
                                             user_id: message.author_id })
    end

    def fetch_whatsapp_template(conversation, template_id, template_data_json)
      template = WhatsappTemplate.find(template_id)
      sources = template.whatsapp_components.map { |c| c.tags.map { |t| t.split('.')[0] } }.flatten.uniq
      template_data = if template_data_json.present?
                        JSON.parse(template_data_json)
                      else
                        {}
                      end
      data = sources.map do |s|
        if s == 'patient'
          conversation.patient
        else
          relation = conversation.patient.respond_to?(s.pluralize) ? conversation.patient.send(s.pluralize) : nil
          if template_data[s].present?
            relation&.find(template_data[s])
          else
            relation&.last
          end
        end
      end.reject(&:blank?)
      [template, data]
    end

    def sinch_source(source)
      case source.downcase
      when 'email'
        'email'
      else
        source.upcase
      end
    end
  end
end
