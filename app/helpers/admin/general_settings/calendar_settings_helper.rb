# frozen_string_literal: true

module Admin
  module GeneralSettings
    module CalendarSettings<PERSON><PERSON><PERSON>
      def calculate_date(operator, time)
        selected_datetime.send(operator, time)
      end

      def selected_date
        params[:date].present? ? params[:date].to_date : Date.current
      end

      def selected_datetime
        (params[:date].present? ? params[:date].to_datetime : DateTime.current)
      end

      def week_range_string
        parsed_date = selected_date.is_a?(String) ? Time.zone.parse(selected_date).to_date : selected_date
        start_of_week = parsed_date.beginning_of_week(:monday)
        end_of_week = start_of_week + 6.days
        "#{start_of_week.strftime('%B %-d')} - #{end_of_week.strftime('%B %-d, %Y')}"
      end

      def calendar_reserved_block_types_table_columns
        [
          table_column('Color', {
                         content: lambda { |block_type|
                           content_tag(:div, '', class: 'w-6 h-6 rounded-full', style: "background-color: #{block_type.color};")
                         },
                         class: 'w-12'
                       }),
          table_column('Type', {
                         content: ->(block_type) { block_type.treatment.patient_friendly_name },
                         class: 'min-w-32 max-w-40'
                       }),
          table_column('Practice', {
                         content: ->(block_type) { block_type.practice&.name || 'All Practices' },
                         class: 'min-w-24 max-w-32 hidden lg:table-cell'
                       }),
          table_column(format_day_header(0), {
                         content: ->(block_type) { render_day_of_week_blocks(block_type, 0) },
                         class: 'min-w-22 max-w-48'
                       }),
          table_column(format_day_header(1), {
                         content: ->(block_type) { render_day_of_week_blocks(block_type, 1) },
                         class: 'min-w-22 max-w-48'
                       }),
          table_column(format_day_header(2), {
                         content: ->(block_type) { render_day_of_week_blocks(block_type, 2) },
                         class: 'min-w-22 max-w-48'
                       }),
          table_column(format_day_header(3), {
                         content: ->(block_type) { render_day_of_week_blocks(block_type, 3) },
                         class: 'min-w-22 max-w-48'
                       }),
          table_column(format_day_header(4), {
                         content: ->(block_type) { render_day_of_week_blocks(block_type, 4) },
                         class: 'min-w-22 max-w-48'
                       }),
          table_column(format_day_header(5), {
                         content: ->(block_type) { render_day_of_week_blocks(block_type, 5) },
                         class: 'min-w-22 max-w-48'
                       }),
          table_column(format_day_header(6), {
                         content: ->(block_type) { render_day_of_week_blocks(block_type, 6) },
                         class: 'min-w-22 max-w-48'
                       }),
          table_column('Weekly Slots', {
                         content: lambda { |block_type|
                           weekly_blocks = get_weekly_blocks_for_type(block_type)
                           "#{weekly_blocks.count} #{'slot'.pluralize(weekly_blocks.count)}"
                         },
                         class: 'w-auto text-center whitespace-nowrap'
                       }),
          table_column('Weekly Hours', {
                         content: lambda { |block_type|
                           weekly_blocks = get_weekly_blocks_for_type(block_type)
                           total_minutes = weekly_blocks.sum(&:duration_in_minutes)
                           total_hours = (total_minutes / 60.0).round(1)
                           "#{total_hours} #{'hour'.pluralize(total_hours)}"
                         },
                         class: 'w-auto text-center whitespace-nowrap'
                       })
        ]
      end

      def calendar_reserved_block_type_action_buttons(block_type)
        edit_btn = edit_type_button(block_type)
        calendar_btn = calendar_view_button
        delete_btn = delete_type_button(block_type)
        content_tag(:div, class: 'flex justify-end items-center space-x-1') do
          concat(edit_btn)
          concat(calendar_btn)
          concat(delete_btn)
        end
      end

      private

      def edit_type_button(block_type)
        content_tag(
          :button,
          class:
            'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ' \
            'ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 ' \
            'focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ' \
            '[&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-gray-400 hover:text-blue-600 ' \
            'hover:bg-blue-50 h-8 w-8 edit-type-link',
          title: 'Edit Type',
          data: {
            id: block_type.id,
            color: block_type.color,
            treatment_id: block_type.treatment_id,
            treatment_name: block_type.treatment.patient_friendly_name,
            treatment_description: block_type.treatment_description,
            no_patient_required: block_type.no_patient_required,
            indicates_new: block_type.indicates_new,
            online_bookable: block_type.online_bookable,
            deposit_required: block_type.deposit_required,
            only_bookable_within_reserved_slots: block_type.only_bookable_within_reserved_slots,
            new_patient_deposit_size: block_type.new_patient_deposit_size,
            old_patient_deposit_size: block_type.old_patient_deposit_size,
            user_ids: (block_type.user_ids || []).to_json
          }
        ) do
          content_tag(:svg,
                      xmlns: 'http://www.w3.org/2000/svg',
                      width: '24',
                      height: '24',
                      viewBox: '0 0 24 24',
                      fill: 'none',
                      stroke: 'currentColor',
                      'stroke-width': '2',
                      'stroke-linecap': 'round',
                      'stroke-linejoin': 'round',
                      class: 'lucide lucide-pen-line h-4 w-4') do
            content_tag(:path, '', d: 'M12 20h9') +
              content_tag(:path, '', d: 'M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z')
          end
        end
      end

      def calendar_view_button
        link_to(
          staff_calendar_admin_calendar_bookings_path(mode: :week, date: selected_date),
          class:
            'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ' \
            'ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 ' \
            'focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ' \
            '[&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-gray-400 hover:text-gray-700 ' \
            'hover:bg-gray-100 h-8 w-8',
          title: 'View Calendar Availability'
        ) do
          content_tag(:svg,
                      xmlns: 'http://www.w3.org/2000/svg',
                      width: '24',
                      height: '24',
                      viewBox: '0 0 24 24',
                      fill: 'none',
                      stroke: 'currentColor',
                      'stroke-width': '2',
                      'stroke-linecap': 'round',
                      'stroke-linejoin': 'round',
                      class: 'lucide lucide-calendar h-4 w-4') do
            content_tag(:path, '', d: 'M8 2v4') +
              content_tag(:path, '', d: 'M16 2v4') +
              content_tag(:rect, '', width: '18', height: '18', x: '3', y: '4', rx: '2') +
              content_tag(:path, '', d: 'M3 10h18')
          end
        end
      end

      def delete_type_button(block_type)
        content_tag(
          :button,
          class:
            'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ' \
            'ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 ' \
            'focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ' \
            '[&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-gray-400 hover:text-red-600 ' \
            'hover:bg-red-50 h-8 w-8 delete-calendar-reserved-block-type',
          title: 'Delete Type',
          data: { 'calendar-reserved-block-type-id': block_type.id }
        ) do
          content_tag(:svg,
                      xmlns: 'http://www.w3.org/2000/svg',
                      width: '24',
                      height: '24',
                      viewBox: '0 0 24 24',
                      fill: 'none',
                      stroke: 'currentColor',
                      'stroke-width': '2',
                      'stroke-linecap': 'round',
                      'stroke-linejoin': 'round',
                      class: 'lucide lucide-trash2 h-4 w-4') do
            content_tag(:path, '', d: 'M3 6h18') +
              content_tag(:path, '', d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6') +
              content_tag(:path, '', d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2') +
              content_tag(:line, '', x1: '10', x2: '10', y1: '11', y2: '17') +
              content_tag(:line, '', x1: '14', x2: '14', y1: '11', y2: '17')
          end
        end
      end

      def render_day_of_week_blocks(block_type, day_of_week)
        start_of_week = selected_date.beginning_of_week(:monday)
        target_date = start_of_week + day_of_week.days

        weekly_blocks = get_weekly_blocks_for_type(block_type)
        blocks = weekly_blocks.select { |block| block.start_time.to_date == target_date }

        content_tag(:div, class: 'space-y-1') do
          blocks.each do |block|
            concat(render_block_item(block))
          end
        end
      end

      def render_block_item(block)
        content_tag(:div, class: 'flex items-center gap-2') do
          concat(render_practitioner_avatar(block.practitioner))
          concat(
            content_tag(
              :div,
              block.times_formatted,
              class: 'text-xs text-gray-900 border border-gray-300 rounded px-2 py-1 bg-white text-center whitespace-nowrap'
            )
          )
          concat(
            content_tag(
              :i,
              '',
              class: 'fas fa-regular fa-trash text-gray-600 hover:text-red-600 cursor-pointer ml-1 delete-calendar-reserved-block',
              data: {
                'calendar-reserved-block-id': block.id,
                'calendar-reserved-block-type-id': block.calendar_reserved_block_type_id
              }
            )
          )
        end
      end

      def render_practitioner_avatar(practitioner)
        if practitioner.image.attached?
          image_tag(practitioner.image, class: 'w-6 h-6 rounded-full object-cover', data: { tippy_content: practitioner.full_name })
        else
          image_tag('default-avatar.webp', class: 'w-6 h-6 rounded-full object-cover', data: { tippy_content: practitioner.full_name })
        end
      end

      def format_day_header(day_of_week)
        start_of_week = selected_date.beginning_of_week(:monday)
        target_date = start_of_week + day_of_week.days
        target_date.strftime('%a, %b %d')
      end

      def get_weekly_blocks_for_type(block_type)
        @weekly_blocks_cache ||= {}
        @weekly_blocks_cache[block_type.id] ||= @weekly_blocks.select { |block| block.calendar_reserved_block_type_id == block_type.id }
      end
    end
  end
end
