# frozen_string_literal: true

module Admin
  module ActionsHelper
    include ActionStylingHelper
    include ActionTableHelper
    include ActionTimelineHelper

    def actions_nav
      render 'admin/shared/vertical_nav', links: [
        {
          label: 'All',
          icon: 'fa-light fa-list',
          color_class: 'peach-orange-bg',
          active: true,
          options: { data: { "action-type": 'all' } }
        },
        *%w[Reminders Tasks Alerts Callbacks Complaints].map do |label|
          action_type = label.downcase.delete_suffix('s')
          {
            label: label,
            icon: action_icon_class(action_type),
            color_class: action_color_class(action_type),
            options: { data: { "action-type": action_type } }
          }
        end,
        {
          label: 'Mentions',
          icon: 'fa-light fa-at',
          color_class: 'light-green-bg',
          options: { data: { "action-type": 'mentions' } }
        },
        {
          label: 'Archived',
          icon: 'fa-light fa-archive',
          color_class: 'deep-red-bg',
          options: { data: { "action-type": 'archived' } }
        }
      ]
    end

    def action_action_buttons(action)
      content_tag(:div, class: 'flex items-center gap-2 group') do
        concat(edit_action_button(action))
        concat(comments_action_button(action))
        concat(timeline_action_button(action))
        if action.completed?
          concat(reactivate_action_button(action))
        else
          concat(complete_action_button(action))
        end
      end
    end

    private

    def reactivate_action_button(action)
      content_tag(:button,
                  class: 'inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-blue-700 ' \
                         'bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors reactivate-action-btn w-8 h-8',
                  data: { action_id: action.id, tippy_content: 'Reactivate Action' }) do
        content_tag(:i, '', class: 'fas fa-undo h-3.5 w-3.5')
      end
    end
  end
end
