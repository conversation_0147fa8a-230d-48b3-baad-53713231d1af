# frozen_string_literal: true

module HrManagementServices
  class ShiftCreator
    def initialize(params)
      @params = params
    end

    def call
      @users = User.includes(:shifts, :roles).where(roles: { name: :admin }, archived: false).clinicians
      @shift = Shift.new(params)
      @shift.assign_event_start = (params[:event_start])
      @shift.assign_event_end = (params[:event_end])
      @shift.practice_id = Current.practice_id

      return result unless @shift.valid?

      # NOTE: repeat_shift columns are legacy and should be removed
      if @shift.repeat_shift
        create_recurring_shifts
      else
        @shift.save
      end

      result
    end

    private

    attr_reader :params

    def create_recurring_shifts
      start_date = DateTime.parse(params[:date])

      end_date = if params[:repeat_indefinitely] == '1'
                   start_date + 1.year
                 elsif params[:end_repeat_date].present?
                   DateTime.parse(params[:end_repeat_date])
                 else
                   # NOTE: fallback 3 months
                   start_date >> 3
                 end

      repeat_days = (params[:repeat_days] || []).map(&:to_i)
      repeat_days = repeat_days.map { |d| d == 7 ? 0 : d }

      start_time = Time.zone.parse(params[:event_start]).strftime('%H:%M')
      end_time = Time.zone.parse(params[:event_end]).strftime('%H:%M')
      pattern = params[:repeat_pattern]

      recurrent_identifier = SecureRandom.uuid

      @shift = Shift.create(
        custom_description: params[:custom_description],
        event_type: params[:description],
        description: params[:description],
        event_start: Time.zone.parse("#{start_date.strftime('%Y-%m-%d')}T#{start_time}"),
        event_end: Time.zone.parse("#{start_date.strftime('%Y-%m-%d')}T#{end_time}"),
        date: start_date.to_date,
        user_id: params[:user_id],
        working_location: params[:working_location],
        practice_id: Current.practice_id,
        surgery_id: params[:surgery_id],
        recurrent_identifier: recurrent_identifier
      )

      current_date = start_date + 1.day
      week_counter = 0
      while current_date <= end_date
        wday = current_date.wday

        if repeat_days.include?(wday) && (pattern == 'weekly' || (pattern == 'alternating' && week_counter.even?))
          event_start = Time.zone.parse("#{current_date.strftime('%Y-%m-%d')}T#{start_time}")
          event_end = Time.zone.parse("#{current_date.strftime('%Y-%m-%d')}T#{end_time}")
          Shift.create(
            custom_description: params[:custom_description],
            event_type: params[:description],
            description: params[:description],
            event_start: event_start,
            event_end: event_end,
            date: event_start.to_date,
            user_id: params[:user_id],
            working_location: params[:working_location],
            practice_id: Current.practice_id,
            surgery_id: params[:surgery_id],
            recurrent_identifier: recurrent_identifier
          )
        end

        week_counter = (week_counter + 1) % 2 if current_date.monday?

        current_date += 1.day
      end
    end

    def result
      [@users, @shift]
    end
  end
end
