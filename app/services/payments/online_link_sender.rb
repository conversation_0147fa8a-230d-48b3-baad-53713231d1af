# frozen_string_literal: true

module Payments
  class OnlineLinkSender
    def initialize(payment)
      @payment = payment
    end

    def call
      email_payment_link
    end

    private

    attr_reader :payment

    def email_payment_link
      payment_link = create_dynamic_payment_link

      patient = payment.patient
      sent_by = payment.transactioned_by

      conversation = patient.conversation
      practice = patient.practices.first # TODO: Use active practice
      sender = practice.communication_accounts.where(account_type: 'email').first

      template = NotificationTemplate.find_by(
        practice: conversation.practice,
        default: true,
        delivery_method: 'email',
        name: 'Online Payment Link'
      )

      subject = ::Notifications::PlaceholderConverter.convert(patient, template.subject)
      text    = ::Notifications::PlaceholderConverter.convert(patient, template.text)
      title   = ::Notifications::PlaceholderConverter.convert(patient, template.title)

      text += "<button class='accept'><a href='#{payment_link}'>Pay Now</a></button>"

      service = Conversations::MessageCreationService.new(
        conversation,
        sent_by.id,
        text,
        {
          subject: subject,
          title: title,
          sender_id: sender.id
        }
      )
      service.execute
    end

    def create_dynamic_payment_link
      price = Stripe::Price.create(
        {
          unit_amount: payment.stripe_amount.to_i,
          currency: payment.currency,
          product_data: { name: payment.name }
        },
        {
          stripe_account: payment.practice.stripe_account_id
        }
      )

      payment_link = Stripe::PaymentLink.create(
        {
          line_items: [{ price: price.id, quantity: 1 }],
          metadata: { payment_id: payment.id }
        },
        {
          stripe_account: payment.practice.stripe_account_id
        }
      )

      payment_link.url
    end
  end
end
