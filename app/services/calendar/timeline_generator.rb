# frozen_string_literal: true

module Calendar
  class TimelineGenerator
    STEP = 5.minutes
    CALENDAR_START_HOUR_FALLBACK = 8
    CALENDAR_END_HOUR_FALLBACK = 22

    attr_accessor :step, :practice, :wday, :start_time, :end_time

    def initialize(practice = nil, wday = nil, step = STEP)
      @practice = practice
      @wday = wday.to_s
      @step = step
    end

    def run
      fetch_practice_opening_times

      (@start_time.to_i...@end_time.to_i).step(step).map do |timestamp|
        start_interval = Time.zone.at(timestamp)
        end_interval = [start_interval + step, @end_time].min

        {
          start_time: start_interval,
          end_time: end_interval,
          duration: end_interval - start_interval
        }
      end
    end

    private

    def fetch_practice_opening_times
      opening_times = practice&.opening_hours

      @start_time = time_now
      @end_time = time_now

      if opening_times.present? && wday.present? # NOTE: daily timeline
        return if opening_times.dig(wday, 'closed') != '0'

        start_time_obj = Time.zone.parse(opening_times.dig(wday, 'start'))
        end_time_obj = Time.zone.parse(opening_times.dig(wday, 'end'))

        @start_time = time_now.change(hour: start_time_obj.hour, min: start_time_obj.min, sec: 0)
        @end_time = time_now.change(hour: end_time_obj.hour, min: end_time_obj.min, sec: 0)
      elsif opening_times.present? # NOTE: weekly timeline (get min and max times)
        return if opening_times.values.all? { |day| day['closed'] == '1' }

        start_time_obj = opening_times.values.filter_map do |day|
          day['start'].present? && Time.zone.parse(day['start'])
        end.min
        end_time_obj = opening_times.values.filter_map { |day| day['end'].present? && Time.zone.parse(day['end']) }.max

        @start_time = time_now.change(hour: start_time_obj.hour, min: start_time_obj.min, sec: 0)
        @end_time = time_now.change(hour: end_time_obj.hour, min: end_time_obj.min, sec: 0)
      else
        @start_time = time_now.change(hour: CALENDAR_START_HOUR_FALLBACK, min: 0, sec: 0)
        @end_time = time_now.change(hour: CALENDAR_END_HOUR_FALLBACK, min: 0, sec: 0)
      end
    end

    def time_now
      @time_now ||= DateTime.now.in_time_zone('London').beginning_of_day
    end
  end
end
