# frozen_string_literal: true

module Calendar
  class RecurrenceService
    def initialize(attrs, reference_record)
      @attrs = attrs
      @reference_record = reference_record
    end

    def call(&create_block)
      start_date = @reference_record.start_time.to_date
      recurrent_identifier = SecureRandom.uuid

      @reference_record.update!(recurrent_identifier: recurrent_identifier)

      if @attrs[:repeat_indefinitely] == '1'
        end_date = start_date + 1.year
      elsif @attrs[:end_repeat_date].present?
        end_repeat_date = Date.parse(@attrs[:end_repeat_date])
        end_date = end_repeat_date.end_of_day
      else
        end_date = (start_date + 3.months).end_of_day
      end

      repeat_days = (@attrs[:repeat_days] || []).map(&:to_i)
      pattern = @attrs[:repeat_pattern]

      current_date = start_date + 1.day
      week_counter = 0

      while current_date.end_of_day <= end_date
        if repeat_days.include?(current_date.wday) && (pattern == 'weekly' || (pattern == 'alternating' && week_counter.even?))
          new_start_time = Time.zone.parse("#{current_date} #{@reference_record.start_time.strftime('%H:%M:%S')}")
          new_end_time   = Time.zone.parse("#{current_date} #{@reference_record.end_time.strftime('%H:%M:%S')}")

          create_block.call(new_start_time, new_end_time, recurrent_identifier)
        end

        current_date += 1.day

        week_counter += 1 if current_date.monday?
      end
    end
  end
end
