# frozen_string_literal: true

module Calendar
  class AvailabilityCalculator
    def initialize(calendar_bookings, calendar_reserved_block_types)
      @calendar_bookings = calendar_bookings
      @calendar_reserved_block_types = calendar_reserved_block_types
    end

    # TODO: fix N+1

    def run
      availability_data = {}

      total_shift_minutes = 14 * 60 # 14 hours 8 AM to 10 PM

      @calendar_bookings.each do |booking|
        day = booking.start_time.to_date
        practitioner_id = booking.practitioner_id
        availability_data[day] ||= {}
        availability_data[day][practitioner_id] ||= []

        # TODO: use real shifts
        shift_start_time = Time.zone.parse("#{day} #{Calendar::SlotFinder::SHIFT_START_TIME}")

        busy_start_minutes = ((booking.start_time - shift_start_time) / 60).to_f
        busy_end_minutes = ((booking.end_time - shift_start_time) / 60).to_f - 1 # NOTE: -1 to make a slight gap between appointments

        # Clamp values between 0 and total_shift_minutes
        busy_start_minutes = [[busy_start_minutes, 0].max, total_shift_minutes].min
        busy_end_minutes = [[busy_end_minutes, 0].max, total_shift_minutes].min

        busy_duration_minutes = busy_end_minutes - busy_start_minutes

        next unless busy_duration_minutes.positive?

        busy_start_percentage = (busy_start_minutes / total_shift_minutes) * 100
        busy_duration_percentage = (busy_duration_minutes / total_shift_minutes) * 100

        availability_data[day][practitioner_id] << {
          start_percentage: busy_start_percentage,
          duration_percentage: busy_duration_percentage,
          color: if booking.booking_type_event?
                   '#828282'
                 else
                   @calendar_reserved_block_types.find do
                     _1.treatment_id == booking.treatment_id
                   end&.color || '#c4dffe'
                 end,
          practice_id: booking.practice_id,
          patient_name: booking.patient&.full_name_with_title,
          treatment_name: booking.treatment&.patient_friendly_name.presence&.truncate(20) || 'Other',
          booking_times: "#{booking.start_time.utc.strftime('%-l.%M%p')} - #{booking.end_time.utc.strftime('%-l.%M%p')}",
          booking_times_with_date: "#{booking.start_time.utc.strftime('%-d %b %Y')} "\
            "#{booking.start_time.utc.strftime('%-l.%M%p')} - #{booking.end_time.utc.strftime('%-l.%M%p')}",
          duration_in_minutes: booking.duration_in_minutes,
          id: booking.id,
          notes: booking.notes.presence || 'No notes available.',
          booking_type: booking.booking_type
        }
      end

      availability_data
    end
  end
end
