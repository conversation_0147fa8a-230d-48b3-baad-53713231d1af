# frozen_string_literal: true

require 'English'
module SecureSend
  class PdfProcessingService
    def initialize(signature_request)
      @signature_request = signature_request
      @document = signature_request.signable_document
      @patient = signature_request.patient
    end

    def get_signature_field_content(node, index)
      case node['data-field-type']
      when 'signature'
        if @signature_request.signature_type == 'typed'
          style = 'font-family: serif; font-size: 1.25rem; color: #334155; font-style: italic; min-height: 30px; word-break: break-word;'
          "<p style=#{style}'>#{@signature_request.signed_name}</p>"
        else
          signature_image_data = Base64.strict_encode64(@signature_request.signature_image.download)
          "<img src='data:image/png;base64,#{signature_image_data}' style='max-width: 100%; max-height: 80px; display: inline-block;'>"
        end
      when 'initials'
        initials = ''
        if @patient
          first_initial = @patient.first_name.to_s[0]
          last_initial = @patient.last_name.to_s[0]
          initials = "#{first_initial}#{last_initial}"
        end
        "<span style='font-size: 24px; font-weight: bold; display: inline-block; " \
        "font-family: \"Dancing Script\", sans-serif;'>#{initials}</span>"
      when 'date'
        date_str = @signature_request.date_fields[index]
        "<span style='font-size: 16px; display: inline-block;'>#{date_str}</span>"
      else
        ''
      end
    end

    def generate_template_pdf_with_signature_fields(html_content, signature_fields)
      processed_html = html_content.dup

      Rails.logger.debug "HTML Content first 200 chars: #{html_content[0..200]}"
      Rails.logger.debug "Signature Fields count: #{signature_fields.length}"
      signature_fields.each_with_index do |field, index|
        Rails.logger.debug "Field #{index + 1}: #{field[:id]} - type: #{field[:type]}, merge_tag: #{field[:merge_tag]}"
      end

      signature_pattern = /\{\{\{signature:(.*?)\}\}\}/
      initial_matches = html_content.scan(signature_pattern)
      Rails.logger.debug "Found #{initial_matches.length} signature field matches in original HTML"
      initial_matches.each_with_index do |match, index|
        Rails.logger.debug "  Match #{index + 1}: {{{signature:#{match[0]}}}}"
      end

      sorted_fields = signature_fields.sort_by { |field| -(field[:position] || 0) }
      sorted_fields.each do |field|
        field_id = field[:id]
        field_type = field[:type]
        field_label = field[:label] || field_type.capitalize
        merge_tag = field[:merge_tag]

        count_before = processed_html.scan(merge_tag).length
        Rails.logger.debug "Processing field #{field_id}: found #{count_before} occurrences of merge tag"

        replacement = "<span class='signature-field-inline' " \
                     "data-field-id='#{field_id}' " \
                     "data-field-type='#{field_type}'>" \
                     "#{field_label}</span>"
        processed_html = processed_html.sub(merge_tag, replacement)

        count_after = processed_html.scan(merge_tag).length
        Rails.logger.debug "After replacement: #{count_after} occurrences remain (replaced #{count_before - count_after})"
      end

      remaining_matches = processed_html.scan(signature_pattern)
      Rails.logger.debug "After all replacements: #{remaining_matches.length} signature tags still remain"

      unless processed_html.include?('<html')
        processed_html = <<~HTML
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="UTF-8">
            <title>Document</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .signature-field-inline {
                padding: 4px 8px;
                color: #007bff;
                border: 2px dotted black;
                font-style: italic;
              }
            </style>
          </head>
          <body>
            #{processed_html}
          </body>
          </html>
        HTML
      end

      pdf_content = WickedPdf.new.pdf_from_string(
        processed_html,
        page_size: 'Letter',
        margin: { top: 10, bottom: 10, left: 10, right: 10 },
        encoding: 'UTF-8',
        enable_local_file_access: true
      )

      if pdf_content.present?
        CombinePDF.parse(pdf_content)
      else
        Rails.logger.error("Failed to generate PDF with WickedPDF")
        if @document.original_file.attached?
          temp_file = Tempfile.new(['original', '.pdf'])
          temp_file.binmode
          temp_file.write(@document.original_file.download)
          temp_file.close
          pdf_content = CombinePDF.load(temp_file.path)
          temp_file.unlink if temp_file && File.exist?(temp_file.path)
          pdf_content
        end
      end
    end

    def generate_signed_pdf_with_audit(options = {})
      html_content = options[:html_content]
      treatment_plan_option_pdf = options[:treatment_plan_option_pdf]
      original_pdf = CombinePDF.new

      practice = @patient.current_practice || @patient.practices.first
      logo = practice.logo.attached? ? practice.logo : SiteSetting.first_or_create.logo

      logo_url = Rails.application.routes.url_helpers.rails_blob_url(
        logo,
        host: Rails.application.config.action_mailer.default_url_options[:host]
      )
      cover_html = ApplicationController.render(
        template: 'admin/pdfs/secure_send_cover',
        layout: false,
        assigns: {
          practice: practice,
          signature_request: @signature_request,
          logo_url: logo_url
        }
      )

      cover_pdf_content = WickedPdf.new.pdf_from_string(
        cover_html,
        page_size: 'Letter',
        margin: { top: 10, bottom: 10, left: 10, right: 10 },
        encoding: 'UTF-8',
        enable_local_file_access: true
      )

      if cover_pdf_content.present?
        cover_pdf = CombinePDF.parse(cover_pdf_content)
        original_pdf << cover_pdf
      else
        Rails.logger.error("Failed to generate cover PDF with WickedPDF")
      end

      if html_content.present?
        doc = Nokogiri::HTML(html_content)
        head = doc.at('head') || doc.at('html').add_child('<head></head>').first
        head.add_child('<link href="https://fonts.googleapis.com/css2?family=Dancing+Script&display=swap" rel="stylesheet">')

        signature_index = 0
        date_index = 0
        doc.css('.signature-field-inline').each do |node|
          case node['data-field-type']
          when 'signature'
            node.replace(get_signature_field_content(node, signature_index))
            signature_index += 1
          when 'date'
            node.replace(get_signature_field_content(node, date_index))
            date_index += 1
          else
            node.replace(get_signature_field_content(node, 0))
          end
        end

        modified_html_content = doc.to_html
        content_pdf_content = WickedPdf.new.pdf_from_string(
          modified_html_content,
          page_size: 'Letter',
          margin: { top: 10, bottom: 10, left: 10, right: 10 },
          encoding: 'UTF-8',
          enable_local_file_access: true
        )

        if content_pdf_content.present?
          content_pdf = CombinePDF.parse(content_pdf_content)
          original_pdf << content_pdf
        else
          Rails.logger.error("Failed to generate content PDF with WickedPDF")
          if @document.original_file.attached?
            temp_file = Tempfile.new(['original', '.pdf'])
            temp_file.binmode
            temp_file.write(@document.original_file.download)
            temp_file.close
            fallback_pdf = CombinePDF.load(temp_file.path)
            original_pdf << fallback_pdf
            temp_file.unlink if temp_file && File.exist?(temp_file.path)
          end
        end
      end

      original_pdf << CombinePDF.parse(treatment_plan_option_pdf.download) if treatment_plan_option_pdf.present?

      signature_page = create_signature_page

      combined_pdf = original_pdf
      combined_pdf << signature_page

      combined_pdf.to_pdf
    end

    def create_signature_page
      signature_pdf = CombinePDF.new
      signature_fields = []

      # Safety check for document and template
      if @document&.document_template&.present?
        html_content = @document.document_template.text
        signature_fields = extract_signature_fields_from_html(html_content) if html_content.present?
      end

      merge_tags = {}
      if @document&.document_template&.present?
        html_content ||= @document.document_template.text
        merge_tags = extract_merge_tags_from_document(html_content) if html_content.present?
      end

      host = Rails.application.config.action_mailer.default_url_options[:host] || 'http://localhost:3000'
      background_image_url = "#{host}/images/background.jpg"
      default_avatar_url = "#{host}/images/default-avatar.jpg"

      signature_html = ApplicationController.render(
        template: 'admin/pdfs/secure_send_signature_page',
        layout: false,
        assigns: {
          document: @document,
          signature_request: @signature_request,
          patient: @patient,
          signature_fields: signature_fields,
          merge_tags: merge_tags,
          background_image_url: background_image_url,
          default_avatar_url: default_avatar_url
        }
      )

      pdf_content = WickedPdf.new.pdf_from_string(
        signature_html,
        page_size: 'Letter',
        margin: { top: 0, bottom: 0, left: 0, right: 0 },
        encoding: 'UTF-8',
        enable_local_file_access: true
      )

      signature_pdf << CombinePDF.parse(pdf_content) if pdf_content.present?
      signature_pdf
    end





    def extract_signature_fields_from_html(html_content)
      signature_fields = []
      signature_pattern = /\{\{\{signature:([^}]+)\}\}\}/
      occurrence_counters = {}
      html_content.scan(signature_pattern) do |match|
        field_id = match[0]
        match_position = Regexp.last_match.offset(0)[0]
        match_text = Regexp.last_match[0]
        occurrence_counters[field_id] ||= 0
        occurrence_counters[field_id] += 1
        field_type = if field_id.include?('initial')
                       'initials'
                     elsif field_id.include?('date')
                       'date'
                     else
                       'signature'
                     end
        label = field_id.gsub('_', ' ').titleize
        signature_fields << {
          id: field_id,
          type: field_type,
          label: label,
          merge_tag: match_text,
          position: match_position,
          occurrence: occurrence_counters[field_id]
        }
      end
      signature_fields
    end

    def extract_merge_tags_from_document(html_content)
      merge_tags = {}
      doc = Nokogiri::HTML(html_content)
      doc.css('span[data-merge-tag]').each do |span|
        tag_name = span['data-merge-tag']
        tag_value = span.text.strip
        next if tag_value.blank? || tag_name == 'unknown'

        parent = span.parent
        context = ''
        if parent
          parent_html = parent.inner_html
          highlighted_span = "<strong>#{tag_value}</strong>"
          context_html = parent_html.gsub(span.to_html, highlighted_span)
          context = Nokogiri::HTML(context_html).text.strip
          context = truncate_context(context, tag_value) if context.length > 100
        end
        merge_tags[tag_name] = { value: tag_value, context: context }
      end
      merge_tags
    end

    def truncate_context(context, tag_value)
      highlight_pos = context.index(tag_value)
      if highlight_pos
        start_pos = [highlight_pos - 40, 0].max
        end_pos = [highlight_pos + tag_value.length + 40, context.length].min
        prefix = start_pos.positive? ? '...' : ''
        suffix = end_pos < context.length ? '...' : ''
        prefix + context[start_pos...end_pos] + suffix
      else
        "#{context[0...100]}..."
      end
    end

    def create_audit_trail_page
      audit_pdf = CombinePDF.new

      audit_html = ApplicationController.render(
        template: 'admin/pdfs/secure_send_audit_trail',
        layout: false,
        assigns: {
          document: @document,
          signature_request: @signature_request
        }
      )

      pdf_content = WickedPdf.new.pdf_from_string(
        audit_html,
        page_size: 'Letter',
        margin: { top: 20, bottom: 20, left: 20, right: 20 },
        encoding: 'UTF-8',
        enable_local_file_access: true
      )

      audit_pdf << CombinePDF.parse(pdf_content) if pdf_content.present?
      audit_pdf
    end

    private
  end
end
