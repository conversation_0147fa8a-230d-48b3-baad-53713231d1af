# frozen_string_literal: true

module MedicalHistories
  class AiService
    def self.analyze_medical_history(medical_history, _azure_url = nil, _azure_token = nil)
      questions = format_medical_history_content(medical_history)
      return nil if questions.empty?

      practice = Practice.first
      gemini_service = Ai::GeminiService.new(practice)

      prompt = <<~PROMPT
        You are a medical history analyzer.

        Instructions:
        1. Analyze the provided medical history questions and answers.
        2. Give a SHORT SUMMARY of the entire medical history NOT EXCEEDING 500 CHARACTERS.
        3. Provide a confidence score (0-100) for your analysis.
        4. You must respond with a JSON object containing exactly these fields:
           - "summary": The short summary (max 500 characters)
           - "code": 200 if confidence score > 85%, otherwise 500
           - "confidence_score": Your confidence score as a number (0-100)
           - "character_count": The number of characters in your summary

        Medical History Questions and Answers:
        #{questions.join("\n")}

        Respond with JSON only.
      PROMPT

      begin
        response = gemini_service.generate_text(prompt, temperature: 0.1)
        return nil unless response

        # Clean the response to extract JSON
        cleaned_response = response.gsub(/```json|```|\n/, '').strip
        result = JSON.parse(cleaned_response)

        # Validate required fields
        if result.key?('summary') && result.key?('code') && result.key?('confidence_score') && result.key?('character_count')
          result
        else
          Rails.logger.error "Invalid response format from Gemini: #{result}"
          nil
        end
      rescue JSON::ParserError => e
        Rails.logger.error "JSON parsing error in Gemini response: #{e.message}"
        Rails.logger.error "Response was: #{response}"
        nil
      rescue StandardError => e
        Rails.logger.error "Error in Gemini interaction: #{e.message}"
        nil
      end
    end

    def self.search_medications(medical_history, _azure_url = nil, _azure_token = nil)
      questions = format_medical_history_content(medical_history)
      medications = Medication.all.pluck(:title)

      practice = Practice.first
      gemini_service = Ai::GeminiService.new(practice)

      prompt = <<~PROMPT
        You are a medication analyzer.

        Instructions:
        1. Analyze the provided medical history questions and answers.
        2. Identify ALL medications that the patient is taking.
        3. Only include medications that are named in the questions - do not guess medications from their answers.
        4. Use the provided list of medications to format the names where possible.
        5. Only include alerts that can affect dental treatment.
        6. Respond with a JSON object containing:
           - "code": 200 if medications found, 500 if none found
           - "medications": Array of medication names found

        Available Medications List:
        #{medications.join(', ')}

        Medical History Questions and Answers:
        #{questions.join("\n")}

        Respond with JSON only.
      PROMPT

      begin
        response = gemini_service.generate_text(prompt, temperature: 0.1)
        return nil unless response

        cleaned_response = response.gsub(/```json|```|\n/, '').strip
        result = JSON.parse(cleaned_response)

        if result['code'] == 200
          result.except('code')
        else
          Rails.logger.error 'No medications found by Gemini'
          nil
        end
      rescue JSON::ParserError => e
        Rails.logger.error "JSON parsing error in Gemini medication response: #{e.message}"
        Rails.logger.error "Response was: #{response}"
        nil
      rescue StandardError => e
        Rails.logger.error "Error in Gemini medication interaction: #{e.message}"
        nil
      end
    end

    def self.medical_history_alerts(medical_history, _azure_url = nil, _azure_token = nil)
      questions = format_medical_history_content(medical_history)
      if medical_history.respond_to?(:search_medication) && medical_history.search_medication
        mh_meds = search_medications(medical_history)
        meds = format_medications(mh_meds)
      else
        meds = []
      end

      practice = Practice.first
      gemini_service = Ai::GeminiService.new(practice)

      prompt = <<~PROMPT
        You are a medical alerts analyzer.

        Instructions:
        1. Analyze the provided medical history questions and answers.
        2. Identify medical alerts that represent risks specifically relevant to dental treatment.
        3. Use the patient's medications to identify potential risks.
        4. Provide direct bullet points for each medical alert.
        5. Number each alert as 'alert1', 'alert2', 'alert3', etc.
        6. All alerts must be written in the third person for the dentist's attention.
        7. If alerts can be combined, output them as one alert with combined description.
        8. Respond with a JSON object containing:
            - "code": 200 if alerts found, 500 if none found
            - "confidence_score": Your confidence score (0-100)
            - "alert1", "alert2", etc.: Alert descriptions

        Medical History Questions and Answers:
        #{questions.join("\n")}

        Patient Medications:
        #{meds.join(', ')}

        Respond with JSON only.
      PROMPT

      begin
        response = gemini_service.generate_text(prompt, temperature: 0.1)
        return nil unless response

        cleaned_response = response.gsub(/```json|```|\n/, '').strip
        result = JSON.parse(cleaned_response)

        if result['code'] == 200
          parsed = result.except('code', 'confidence_score')
          parsed.map { |k, v| { k => v } }
        else
          Rails.logger.error 'No alerts found by Gemini'
          nil
        end
      rescue JSON::ParserError => e
        Rails.logger.error "JSON parsing error in Gemini alerts response: #{e.message}"
        Rails.logger.error "Response was: #{response}"
        nil
      rescue StandardError => e
        Rails.logger.error "Error in Gemini alerts interaction: #{e.message}"
        nil
      end
    end

    def self.medical_history_changes(prev_medical_history, current_medical_history, _azure_url = nil, _azure_token = nil)
      set_a = format_medical_history_content(prev_medical_history).join("\n")
      set_b = format_medical_history_content(current_medical_history).join("\n")

      questions = "Previous:\n#{set_a}\n\n\nCurrent:\n#{set_b}\n\n\n"

      practice = Practice.first
      gemini_service = Ai::GeminiService.new(practice)

      prompt = <<~PROMPT
        You are a medical history difference analyzer.

        Instructions:
        1. Compare two medical histories: one from a previous visit and one new.
        2. Identify and output any changes in the answers to the questions as bullet points.
        3. Ignore changes to the questions themselves; focus only on changes to answers.
        4. Each change should be numbered as 'answer_change1', 'answer_change2', etc.
        5. If there are changes and confidence score is above 85%, return code 200.
        6. If there are no changes, return code 500.
        7. Respond with a JSON object containing:
           - "code": 200 if changes found, 500 if none found
           - "confidence_score": Your confidence score (0-100)
           - "answer_change1", "answer_change2", etc.: Change descriptions

        Medical History Comparison:
        #{questions}

        Respond with JSON only.
      PROMPT

      begin
        response = gemini_service.generate_text(prompt, temperature: 0.1)
        return nil unless response

        cleaned_response = response.gsub(/```json|```|\n/, '').strip
        result = JSON.parse(cleaned_response)

        if result['code'] == 200
          result.except('code', 'confidence_score')
        else
          Rails.logger.error 'No changes found by Gemini'
          nil
        end
      rescue JSON::ParserError => e
        Rails.logger.error "JSON parsing error in Gemini changes response: #{e.message}"
        Rails.logger.error "Response was: #{response}"
        nil
      rescue StandardError => e
        Rails.logger.error "Error in Gemini changes interaction: #{e.message}"
        nil
      end
    end

    def self.format_medical_history_content(medical_history)
      results = []
      bridging = medical_history
                 .medical_histories_medical_history_answers
                 .includes(medical_history_answer: :medical_history_question)

      bridging.each do |bridge|
        answer   = bridge.medical_history_answer
        question = answer.medical_history_question
        next unless question

        case question.question_type
        when 'multiple_answers', 'single_answer'
          results << "Question: #{question.question}, Answer: #{answer.answer}"

        when 'text'
          typed_text = bridge.value.presence || 'N/A'
          results << "Question: #{question.question}, Response: #{typed_text}"

        when 'medication_select'
          if bridge.value.present?
            med_ids = bridge.value.split(',')
            meds = Medication.where(id: med_ids).pluck(:name)
            results << "Question: #{question.question}, Medications: #{meds.join(', ')}"
          else
            results << "Question: #{question.question}, Medications: None"
          end
        end
      end

      results << "Patient Comments: #{medical_history.patient_comments}" if medical_history.patient_comments.present?
      results << "Our Comments: #{medical_history.our_comments}" if medical_history.our_comments.present?
      results << "Additional Info: #{medical_history.additional_information}" if medical_history.additional_information.present?

      results
    end

    def self.format_medications(meds_hash)
      return [] unless meds_hash.is_a?(Hash)

      meds_hash['medications'] || []
    end
  end
end
