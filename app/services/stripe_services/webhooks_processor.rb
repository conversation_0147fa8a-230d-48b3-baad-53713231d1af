# frozen_string_literal: true

module StripeServices
  class WebhooksProcessor
    def initialize(payload, sig_header)
      @payload = payload
      @sig_header = sig_header
    end

    def call
      case event['type']
      when 'payment_intent.succeeded'
        process_payment_intent_succeeded
      when 'payment_intent.payment_failed'
        process_payment_intent_failed
      when 'payment_intent.canceled'
        process_payment_intent_canceled
      when 'terminal.reader.action_succeeded'
        process_terminal_reader_action_succeeded
      when 'terminal.reader.action_failed'
        process_terminal_reader_action_failed
      when 'checkout.session.completed'
        process_checkout_session_completed
      else
        raise "Unhandled event type: #{event['type']}"
      end
    end

    private

    def process_payment_intent_succeeded
      payment = Payment.find_by(stripe_payment_intent_id: data.id)

      payment.update(status: 'succeeded')
      Automations::TriggerProcessor.call(model: payment, event_type: 'payment_succeeded')
    end

    def process_payment_intent_failed
      payment = Payment.find_by(stripe_payment_intent_id: data.id)

      payment&.update(status: 'failed')
    end

    def process_payment_intent_canceled
      payment = Payment.find_by(stripe_payment_intent_id: data.id)

      payment&.update(status: 'failed')
    end

    def process_terminal_reader_action_succeeded
      payment = Payment.find_by(stripe_payment_intent_id: data&.action&.process_payment_intent&.payment_intent)

      payment.update(status: 'succeeded')
      Automations::TriggerProcessor.call(model: payment, event_type: 'payment_succeeded')

      trigger_pusher_payment_event(payment, 'payment_succeeded')
    end

    def process_terminal_reader_action_failed
      payment = Payment.find_by(stripe_payment_intent_id: data&.action&.process_payment_intent&.payment_intent)

      payment.update(status: 'failed')

      trigger_pusher_payment_event(payment, 'payment_failed')
    end

    def process_checkout_session_completed
      payment = Payment.find_by(id: data.metadata['payment_id'])

      payment.update(status: 'succeeded')
      Automations::TriggerProcessor.call(model: payment, event_type: 'payment_succeeded')
    end

    def data
      @data ||= event.data.object
    end

    def practice
      @practice ||= Practice.find_by(stripe_account_id: event.account)
    end

    def event
      @event ||= Stripe::Webhook.construct_event(payload, sig_header, ENV['STRIPE_WEBHOOK_SECRET'])
    end

    def trigger_pusher_payment_event(payment, event_name)
      return unless payment

      pusher = PusherService.new
      pusher.trigger("private-payment-#{payment.id}", event_name, {
                       payment_id: payment.id,
                       status: payment.status,
                       stripe_payment_intent_id: payment.stripe_payment_intent_id
                     })
    rescue StandardError => e
      Rails.logger.error "Failed to trigger Pusher event for payment #{payment.id}: #{e.message}"
    end

    attr_reader :payload, :sig_header
  end
end
