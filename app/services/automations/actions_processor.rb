# frozen_string_literal: true

module Automations
  class ActionsProcessor
    class << self
      def call(automation, model)
        automation.automation_actions.each do |action|
          perform_action(action, model)
        end
      end

      def perform_action(action, model)
        case action.action_type
        when 'send_email_to_user'
          send_email_to_user(action, model)
        when 'send_email_to_patient'
          send_email_to_patient(action, model)
        when 'send_sms_to_patient'
          send_sms_to_patient(action, model)
        end
      end

      def send_email_to_patient(action, model)
        patient = model.patient
        conversation = patient.conversation
        practice = patient.current_practice || patient.practices.first
        sender = practice.communication_accounts.where(account_type: 'email').first

        template = NotificationTemplate.find(action.additional_value)

        subject = ::Notifications::PlaceholderConverter.convert(model, template.subject)
        text    = ::Notifications::PlaceholderConverter.convert(model, template.text)
        title   = ::Notifications::PlaceholderConverter.convert(model, template.title)

        service = Conversations::MessageCreationService.new(
          conversation,
          nil,
          text,
          {
            subject: subject,
            title: title,
            sender_id: sender.id
          }
        )
        service.execute
      end

      def send_sms_to_patient(action, model)
        patient = model.patient
        conversation = patient.conversation
        practice = patient.current_practice || patient.practices.first
        sender = practice.communication_accounts.where(account_type: 'sms').first

        template = NotificationTemplate.find(action.additional_value)

        text    = ::Notifications::PlaceholderConverter.convert(model, template.text)

        service = Conversations::MessageCreationService.new(
          conversation,
          nil,
          text,
          {
            sender_id: sender.id
          }
        )
        service.execute
      end

      def send_email_to_user(action, model)
        recipient = User.find(action.value)
        template = NotificationTemplate.find(action.additional_value)
        sender = Notifications::Sender.new(model, template, [recipient])
        sender.send_email
      end
    end
  end
end
