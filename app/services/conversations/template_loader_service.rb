# frozen_string_literal: true

module Conversations
  class TemplateLoaderService
    def initialize(user, practice_id = nil)
      @user = user
      @practice_id = practice_id
    end

    def load_letter_templates(scope)
      if practice_has_stannp_key?
        scope.where(template_type: 'patient_letter')
      else
        DocumentTemplate.none
      end
    end

    def load_whatsapp_templates
      if @user.communication_accounts.where(account_type: 'whatsapp').count.positive?
        WhatsappTemplate.where(
          practice_id: @user.communication_accounts.where(account_type: 'whatsapp').pluck(:practice_id).uniq,
          automations_only: false,
          submitted: true
        )
      else
        WhatsappTemplate.none
      end
    end

    private

    def practice_has_stannp_key?
      Practice.where(
        id: @practice_id || @user.practice_ids
      ).where.not(stannp_key: [nil, '']).count.positive?
    end
  end
end
