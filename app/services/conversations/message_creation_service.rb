# frozen_string_literal: true

module Conversations
  class MessageCreationService
    include Admin::ConversationMessagesHelper

    attr_reader :conversation, :sender, :user_id, :content, :template_id, :template_data, :session_id,
                :lab_work_id, :message_type, :stannp_practice, :options

    def initialize(conversation, user_id, content, options = {})
      @conversation = conversation
      @user_id = user_id
      @content = content
      @sender, @stannp_practice = get_sender(options[:sender_id])
      @session_id = options[:session_id]
      @lab_work_id = options[:lab_work_id]
      @message_type = options[:message_type]
      @template_id = options[:template_id]
      @template_data = options[:template_data]
      @options = options
    end

    def get_sender(sender_id)
      return [] if sender_id.blank?

      if sender_id.to_s.start_with?('stannp-')
        [nil, Practice.find(sender_id.to_s.split('-').last)]
      else
        [CommunicationAccount.find(sender_id), nil]
      end
    end

    def execute
      message_array = build_message(conversation, sender, user_id, content, {
                                      lab_work_id: lab_work_id,
                                      stannp_practice: stannp_practice,
                                      template_id: options[:template_id],
                                      template_data: options[:template_data],
                                      email_subject: options[:subject],
                                      message_type: message_type
                                    })

      message = message_array[0] # Extract the ConversationMessage object from the array
      template = message_array[1] # Extract the template object from the array
      data = message_array[2] # Extract the data object from the array

      if message.save
        identifier = if conversation.internal?
                       User.find(user_id).email
                     else
                       sender&.identifier
                     end

        conversation.patient.crm_cards.each(&:update_last_contacted) if conversation.patient.present? && !conversation.internal?

        result = send_message(conversation, message, sender, identifier, template, data, options[:pdf])

        if result[:errors].present?
          message.update(delivery_status: 'failed')

          { success: false, errors: result[:errors] }
        else
          push_message(message, session_id)

          { success: true, message: message, result: result }
        end
      else
        { success: false, errors: message.errors.full_messages }
      end
    end
  end
end
