// Conversation tabs functionality
document.addEventListener('DOMContentLoaded', function() {
  // Team Member Search Functionality
  const teamMemberSearch = document.querySelector('#team-member-search');
  const teamMemberCards = document.querySelectorAll('.team-member-card');

  if (teamMemberSearch) {
    teamMemberSearch.addEventListener('input', function() {
      const searchTerm = this.value.toLowerCase();

      teamMemberCards.forEach(card => {
        const memberName = card.querySelector('h4').textContent.toLowerCase();
        const memberRole = card.querySelector('p').textContent.toLowerCase();

        if (memberName.includes(searchTerm) || memberRole.includes(searchTerm)) {
          card.style.display = 'flex';
        } else {
          card.style.display = 'none';
        }
      });
    });
  }

  // Team Member Selection
  const selectedMembers = new Set();
  const selectedMembersContainer = document.querySelector('#selected-team-members');
  const selectedCountElement = document.querySelector('#selected-count');

  teamMemberCards.forEach(card => {
    card.addEventListener('click', function() {
      const userId = this.getAttribute('data-user-id');
      const userName = this.querySelector('h4').textContent;
      const userInitials = userName.split(' ').map(n => n[0]).join('');
      const checkbox = this.querySelector('.team-member-checkbox');
      const hiddenInput = document.querySelector(`#user-${userId}`);

      if (selectedMembers.has(userId)) {
        // Remove from selection
        selectedMembers.delete(userId);
        checkbox.classList.remove('bg-amber-500');
        checkbox.classList.add('border-2', 'border-gray-300');

        // Remove from selected list
        const memberElement = document.querySelector(`#selected-member-${userId}`);
        if (memberElement) memberElement.remove();

        // Disable hidden input
        if (hiddenInput) hiddenInput.disabled = true;
      } else {
        // Add to selection
        selectedMembers.add(userId);
        checkbox.classList.remove('border-2', 'border-gray-300');
        checkbox.classList.add('bg-amber-500');

        // Add to selected list
        const memberElement = document.createElement('div');
        memberElement.id = `selected-member-${userId}`;
        memberElement.className = 'group flex items-center h-7 pl-1.5 pr-2 rounded-full bg-white border border-gray-200 shadow-sm hover:border-gray-300 transition-all duration-200';
        memberElement.innerHTML = `
          <div class="w-5 h-5 rounded-full flex items-center justify-center mr-1.5" style="background: linear-gradient(135deg, rgb(254, 243, 199) 0%, rgb(253, 230, 138) 100%);">
            <span class="text-[10px] font-medium" style="color: rgb(180, 83, 9);">${userInitials}</span>
          </div>
          <span class="text-xs font-medium">${userName}</span>
          <button type="button" class="ml-1.5 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600" aria-label="Remove ${userName}" title="Remove ${userName}" data-user-id="${userId}">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6 6 18"></path>
              <path d="m6 6 12 12"></path>
            </svg>
          </button>
        `;

        selectedMembersContainer.appendChild(memberElement);

        // Add remove event listener
        memberElement.querySelector('button').addEventListener('click', function(e) {
          e.stopPropagation();
          const userId = this.getAttribute('data-user-id');
          const teamMemberCard = document.querySelector(`.team-member-card[data-user-id="${userId}"]`);

          // Trigger a click on the team member card to remove it
          if (teamMemberCard) teamMemberCard.click();
        });

        // Enable hidden input
        if (hiddenInput) {
          hiddenInput.disabled = false;
          hiddenInput.value = userId;
        }
      }

      // Update count
      selectedCountElement.textContent = selectedMembers.size;
    });
  });
});
