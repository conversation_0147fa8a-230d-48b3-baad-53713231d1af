// Phone Fields with International Input Styling

.phone-field-container {
  width: 100%;
  
  .phone-edit {
    width: 100%;
    
    .iti {
      width: 100% !important;
      display: block;
      
      .iti__tel-input {
        width: 100% !important;
        border: 1px solid #e4e4e7;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 14px;
        font-weight: 500;
        color: #1f2937;
        background-color: white;
        
        &:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
        
        &::placeholder {
          color: #9ca3af;
        }
      }
      
      .iti__country-list {
        z-index: 9999;
        border: 1px solid #e4e4e7;
        border-radius: 6px;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        
        .iti__country {
          padding: 8px 12px;
          
          &:hover {
            background-color: #f3f4f6;
          }
          
          &.iti__highlight {
            background-color: #eff6ff;
          }
        }
      }
      
      .iti__flag-container {
        .iti__selected-flag {
          border: none;
          background: transparent;
          padding: 0 8px 0 12px;
          
          &:hover {
            background-color: #f9fafb;
          }
          
          .iti__arrow {
            border-top-color: #6b7280;
          }
        }
      }
    }
  }
}

// Ensure the container takes full width in grid layouts
.grid .phone-field-container,
.flex .phone-field-container {
  width: 100%;
  min-width: 0; // Allow flex items to shrink
}
