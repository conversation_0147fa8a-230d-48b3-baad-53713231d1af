button,
input[type='button'],
input[type='submit'],
input[type='reset'],
[role='button'] {
  cursor: pointer;
}

.orangebtn {
  border-radius: 30px;
  border: 1px solid var(--Secondary---Orange, #F7B07C);
  background: linear-gradient(89deg, #FF9E6D 14.11%, #F7B07C 91.49%);
  color: var(--Secondary---Dark-Blue, #2A4558);
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 16.8px */
  display: flex;
  padding: 8px 32px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  text-decoration: 0;
}

.back-reports-button {
  padding: 8px 32px 8px 32px;
  height: 35px;
  border-radius: 30px;
  border: 1px solid #2A4558;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  text-align: center;
  text-decoration: none;
}

.small-btn {
  font-size: 12px;
  padding: 6px 20px;
}

.menubtn {
  background: none;
  border: 1px solid #6D6B6B;
  border-radius: 50px;
  color: #151515;
  font-size: 16px;
  cursor: pointer;
  padding: 5px 4px;
}

.cool-gray-btn {
  border-radius: 30px;
  border: 1px solid #8999B0;
  background: linear-gradient(90deg, #8999B0 0%, #8999B0 100%);
  color: #151515;
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 16.8px */
  display: flex;
  padding: 8px 32px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  text-decoration: 0;
}

.deep-red-btn {
  border-radius: 30px;
  border: 1px solid #B97172;
  background: linear-gradient(90deg, #B97172 0%, #B97172 100%);
  color: #151515;
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 16.8px */
  display: flex;
  padding: 8px 32px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  text-decoration: 0;
}

.lightbluebtn {
  border-radius: 30px;
  border: 1px solid #BDCFDB;
  background: linear-gradient(90deg, #BDCFDB 0%, #BDCFDB 100%);
  color: #151515;
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 16.8px */
  display: flex;
  padding: 8px 32px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  text-decoration: 0;
}

.greenbtn {
  border-radius: 30px;
  border: 1px solid var(--Secondary---Light-Purple, #D1E0B7);
  background: linear-gradient(90deg, #D1E0B7 0%, #D1E0B7 100%);
  color: var(--Secondary---Dark-Blue, #2A4558);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 16.8px */
  display: flex;
  padding: 8px 32px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  text-decoration: 0;
}

.yellowbtn {
  border-radius: 30px;
  border: 1px solid var(--Primary---Yellow, #FBE099);
  background: linear-gradient(90deg, #FBE099 0%, #FFEDBD 100%);
  color: var(--Secondary---Dark-Blue, #2A4558);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 16.8px */
  display: flex;
  padding: 8px 32px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  text-decoration: 0;
}

.purplebtn {
  border-radius: 30px;
  border: 1px solid var(--Secondary---Light-Purple, #BDB3CC);
  background: linear-gradient(90deg, #C0B0D9 0%, #CFB5F5 100%);
  color: var(--Secondary---Dark-Blue, #2A4558);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%; /* 16.8px */
  display: flex;
  padding: 8px 32px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  text-decoration: 0;
}

.back-account-button {
  padding: 9px 32px 8px 32px;
  margin-left: 12px;
  height: 35px;
  border-radius: 30px;
  border: 1px solid #2A4558;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  text-align: center;
  text-decoration: none;
}

.ai-button {
  border: 1px solid #2A4558;
  border-radius: 30px;
  background-color: #FBE099;
}

.quote-button-previous {
  padding: 7.5px 40px;
  border-radius: 100px;
  color: black;
  text-align: center;
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  background: white;
  border: 2px solid black;
  margin-right: 10px;
}

.quote-button-previous:hover {
  background: white;
  color: black;
  border: 2px solid black;
}

.quote-button-next {
  padding: 7.5px 40px;
  border-radius: 100px;
  color: black;
  text-align: center;
  font-family: "Poppins", sans-serif;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  background: #F7B07C;
  border: 2px solid #F7B07C;
  margin-left: 10px;
}

.quote-button-next:hover {
  background: #F7B07C;
  color: black;
  border: 2px solid #F7B07C;
}

// Action button active states
.comments-toggle.active {
  background-color: rgb(59, 130, 246) !important;
  color: white !important;
}

.timeline-toggle.active {
  background-color: rgb(147, 51, 234) !important;
  color: white !important;
}
