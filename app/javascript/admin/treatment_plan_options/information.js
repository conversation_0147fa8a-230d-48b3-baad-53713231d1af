$(document).ready(function() {
    if(!$('.tp-information-step').length) return;

    const $patientSelect = $('#treatment_plan_option_patient_id');
    let practiceId = $patientSelect.data('practiceId');
    const $nextButton = $('#next-button');

    $patientSelect.select2({
        placeholder: 'Search for a patient',
        minimumInputLength: 3,
        width: '100%',
        ajax: {
            url: `/admin/treatment_plan_options/option/select2_search?practice_id=${practiceId}`,
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return { q: params.term };
            },
            processResults: function(data) {
                return data;
            }
        }
    });

    function toggleNextButton() {
        if ($patientSelect.val()) {
            $nextButton.prop('disabled', false);
        } else {
            $nextButton.prop('disabled', true);
        }
    }

    toggleNextButton();

    $patientSelect.on('change', toggleNextButton);

    let patientData = $patientSelect.data('patient');
    if (patientData && patientData.id) {
        let option = new Option(patientData.text, patientData.id, true, true);
        $patientSelect.append(option).trigger('change');
    }

    const toggleReasonBtn = document.querySelector(".toggle-reason-template-dropdown");
    const toggleServiceBtn = document.querySelector(".toggle-service-template-dropdown");
    const toggleConclusionBtn = document.querySelector(".toggle-conclusion-template-dropdown");
    const reasonDropdown = document.getElementById("reason-template-dropdown");
    const serviceDropdown = document.getElementById("service-template-dropdown");
    const conclusionDropdown = document.getElementById("conclusion-template-dropdown");

    document.addEventListener("click", function (event) {
        const isClickInside = toggleReasonBtn.contains(event.target) || reasonDropdown.contains(event.target);
        if (!isClickInside) {
            reasonDropdown.classList.add("hidden");
            toggleReasonBtn.setAttribute("aria-expanded", "false");
        }
    });

    toggleReasonBtn.addEventListener("click", function (event) {
        event.stopPropagation();
        reasonDropdown.classList.toggle("hidden");
        const expanded = toggleReasonBtn.getAttribute("aria-expanded") === "true";
        toggleReasonBtn.setAttribute("aria-expanded", String(!expanded));
    });

    document.addEventListener("click", function (event) {
        const isClickInside = toggleServiceBtn.contains(event.target) || serviceDropdown.contains(event.target);
        if (!isClickInside) {
            serviceDropdown.classList.add("hidden");
            toggleServiceBtn.setAttribute("aria-expanded", "false");
        }
    });

    toggleServiceBtn.addEventListener("click", function (event) {
        event.stopPropagation();
        serviceDropdown.classList.toggle("hidden");
        const expanded = toggleServiceBtn.getAttribute("aria-expanded") === "true";
        toggleServiceBtn.setAttribute("aria-expanded", String(!expanded));
    });

    document.addEventListener("click", function (event) {
        const isClickInside = toggleConclusionBtn.contains(event.target) || conclusionDropdown.contains(event.target);
        if (!isClickInside) {
            conclusionDropdown.classList.add("hidden");
            toggleConclusionBtn.setAttribute("aria-expanded", "false");
        }
    });

    toggleConclusionBtn.addEventListener("click", function (event) {
        event.stopPropagation();
        conclusionDropdown.classList.toggle("hidden");
        const expanded = toggleConclusionBtn.getAttribute("aria-expanded") === "true";
        toggleConclusionBtn.setAttribute("aria-expanded", String(!expanded));
    });

    $(".reason-card").click(function () {
        const text = $(this).data("text");

        if (tinymce.get("treatment_plan_option_reason")) {
            tinymce.get("treatment_plan_option_reason").setContent(text);
        }
        // $("#treatmentReasonModal .btn-close").click();
        reasonDropdown.classList.add("hidden");
        toggleReasonBtn.setAttribute("aria-expanded", "false");
    });

    $(".services-card").click(function () {
        const text = $(this).data("text");

        if (tinymce.get("treatment_plan_option_services_provided")) {
            tinymce.get("treatment_plan_option_services_provided").setContent(text);
        }
        // $("#treatmentServicesModal .btn-close").click();
        serviceDropdown.classList.add("hidden");
        toggleServiceBtn.setAttribute("aria-expanded", "false");
    });

    $(".conclusions-card").click(function () {
        const text = $(this).data("text");

        if (tinymce.get("treatment_plan_option_outcome")) {
            tinymce.get("treatment_plan_option_outcome").setContent(text);
        }
        // $("#treatmentConclusionsModal .btn-close").click();
        conclusionDropdown.classList.add("hidden");
        toggleConclusionBtn.setAttribute("aria-expanded", "false");
    });
});
