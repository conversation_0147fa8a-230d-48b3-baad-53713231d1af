$(document).ready(function() {
  // Track the currently displayed conversation ID
  let currentDisplayedConversationId = null;

  // Initialize active state for current conversation
  function initializeActiveState() {
    const currentConversationId = $('[data-current-conversation-id]').data('current-conversation-id');
    if (currentConversationId) {
      // Set the initially displayed conversation ID
      currentDisplayedConversationId = currentConversationId.toString();

      // Remove active state from all conversation cards
      $('.conversation-card').removeClass('bg-blue-50 border-blue-200/70');
      $('.conversation-card').addClass('border-transparent bg-transparent');

      // Add active state to current conversation card
      $(`.conversation-card[data-conversation-id="${currentConversationId}"]`).removeClass('border-transparent bg-transparent');
      $(`.conversation-card[data-conversation-id="${currentConversationId}"]`).addClass('bg-blue-50 border-blue-200/70');
    }
  }

  // Initialize active state on page load
  initializeActiveState();

  // Handle conversation card clicks for AJAX switching
  $(document).on('click', '.conversation-card', function(e) {
    e.preventDefault();

    const conversationId = $(this).data('conversation-id');
    const currentConversationId = $('[data-current-conversation-id]').data('current-conversation-id');

    // Check if we're on the index page (no conversation selected)
    const conversationContainer = $('#conversation-container');
    const conversationLoading = $('#conversation-loading');

    if (conversationContainer.length === 0 || conversationLoading.length === 0) {
      // We're on the index page - navigate to the conversation show page
      window.location.href = `/admin/conversations/${conversationId}`;
      return;
    }

    // Don't reload if we're already on this conversation (only check for show page)
    if (currentDisplayedConversationId && currentDisplayedConversationId === conversationId.toString()) {
      return;
    }

    // We're on the show page - use AJAX switching
    // Disconnect from current Pusher channel before switching
    if (typeof window.disconnectPusherChannel === 'function') {
      window.disconnectPusherChannel();
    }

    // Remove active state from all conversation cards
    $('.conversation-card').removeClass('bg-blue-50 border-blue-200/70');
    $('.conversation-card').addClass('border-transparent bg-transparent');

    // Add active state to clicked card
    $(this).removeClass('border-transparent bg-transparent');
    $(this).addClass('bg-blue-50 border-blue-200/70');

    // Show loading state
    conversationContainer.addClass('hidden');
    conversationLoading.removeClass('hidden');

    // Make AJAX request to load new conversation
    $.ajax({
      url: `/admin/conversations/${conversationId}.js`,
      method: 'GET',
      dataType: 'html',
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      },
      success: function(response) {
        // Update conversation container with new content
        conversationContainer.html(response);

        // Update browser URL without full page reload
        window.history.pushState({}, '', `/admin/conversations/${conversationId}`);

        // Update current conversation ID
        $('[data-current-conversation-id]').attr('data-current-conversation-id', conversationId);

        // Update the currently displayed conversation ID
        currentDisplayedConversationId = conversationId.toString();

        // Hide loading state and show conversation
        conversationLoading.addClass('hidden');
        conversationContainer.removeClass('hidden');

        // Reinitialize any JavaScript components that may be needed
        initializeConversationComponents();

        tippy("[data-tippy-content]", {
          allowHTML: true,
          theme: "light"
        });
      },
      error: function(xhr, status, error) {
        console.error('Error loading conversation:', error);

        // Hide loading state and show error or fallback
        conversationLoading.addClass('hidden');
        conversationContainer.removeClass('hidden');

        // Show error message
        if (typeof toastr !== 'undefined') {
          toastr.error('Failed to load conversation. Please try again.');
        }

        // Restore original active state
        initializeActiveState();
      }
    });
  });

  // Function to reinitialize conversation components after AJAX load
  function initializeConversationComponents() {
    // Reinitialize TinyMCE if it exists
    if (typeof tinymce !== 'undefined') {
      tinymce.remove('#message-editor');

      // Wait a moment before reinitializing to ensure DOM is ready
      setTimeout(function() {
        // The TinyMCE initialization code will be in conversation_form.js
        // We need to trigger it manually here
        if (typeof window.initializeTinyMCE === 'function') {
          window.initializeTinyMCE();
        }
      }, 100);
    }

    // Reinitialize Pusher connection for new conversation
    if (typeof window.initializePusher === 'function') {
      window.initializePusher();
    }

    // Reinitialize conversation form components
    if (typeof window.initializeConversationForm === 'function') {
      window.initializeConversationForm();
    }

    // Reinitialize chat container components
    if (typeof window.initializeChatContainer === 'function') {
      window.initializeChatContainer();
    }

    // Reinitialize messages list components
    if (typeof window.initializeMessagesList === 'function') {
      window.initializeMessagesList();
    }

    // Reinitialize message list components
    if (typeof window.initializeMessageList === 'function') {
      window.initializeMessageList();
    }

    // Manually reinitialize key conversation form components
    setTimeout(function() {
      // Reinitialize message type buttons
      const messageTypeButtons = document.querySelectorAll('.message-type-button');
      messageTypeButtons.forEach(function(button) {
        // Remove existing event listeners by cloning and replacing
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        // Add new event listeners based on button type
        if (newButton.id === 'internal-note-btn') {
          newButton.addEventListener('click', function(e) {
            e.preventDefault();
            if (typeof window.setMessageType === 'function') {
              window.setMessageType('internal', this, e);
            }
            return false;
          });
        } else if (newButton.id.startsWith('messaging-btn-')) {
          newButton.addEventListener('click', function(e) {
            e.preventDefault();
            if (typeof window.setMessageType === 'function') {
              window.setMessageType('messaging', this, e);
            }
            return false;
          });
        } else if (newButton.id.startsWith('email-account-')) {
          newButton.addEventListener('click', function(e) {
            e.preventDefault();
            if (typeof window.setMessageType === 'function') {
              window.setMessageType('email', this, e);
            }
            return false;
          });
        }
      });

      // Reinitialize send button
      const sendButton = document.getElementById('send-message-btn');
      if (sendButton) {
        const newSendButton = sendButton.cloneNode(true);
        sendButton.parentNode.replaceChild(newSendButton, sendButton);

        // Add send button event listener
        newSendButton.addEventListener('click', function() {
          // Make sure TinyMCE content is updated to the textarea
          if (tinymce.get('message-editor')) {
            tinymce.get('message-editor').save();
          }

          // Trigger form submission
          const messageForm = document.getElementById('message-form');
          if (messageForm) {
            const formData = new FormData(messageForm);
            const submitButton = newSendButton;
            const originalButtonText = submitButton.textContent;

            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';

            fetch(messageForm.action, {
              method: 'POST',
              body: formData,
              headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
              }
            })
            .then(response => {
              if (response.ok) {
                messageForm.reset();
                if (tinymce.get('message-editor')) {
                  tinymce.get('message-editor').setContent('');
                }
                if (typeof toastr !== 'undefined') {
                  toastr.success('Message sent successfully');
                }
              } else {
                if (typeof toastr !== 'undefined') {
                  toastr.error('Failed to send message. Please try again.');
                }
              }
            })
            .catch(error => {
              console.error('Error sending message:', error);
              if (typeof toastr !== 'undefined') {
                toastr.error('Failed to send message. Please try again.');
              }
            })
            .finally(() => {
              submitButton.disabled = false;
              submitButton.textContent = originalButtonText;
            });
          }
        });
      }

      // Reinitialize template selector if present
      const templateSelectorBtn = document.getElementById('template-selector-btn');
      if (templateSelectorBtn) {
        const newTemplateSelectorBtn = templateSelectorBtn.cloneNode(true);
        templateSelectorBtn.parentNode.replaceChild(newTemplateSelectorBtn, templateSelectorBtn);

        newTemplateSelectorBtn.addEventListener('click', function() {
          const templateModal = document.getElementById('template-modal');
          if (templateModal) {
            templateModal.classList.remove('hidden');
            // Load templates functionality would need to be called here
          }
        });
      }

      // Reinitialize modal close buttons
      const closeModalButtons = document.querySelectorAll('[id$="-modal"] button[id^="close-"]');
      closeModalButtons.forEach(function(button) {
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        newButton.addEventListener('click', function() {
          const modal = this.closest('[id$="-modal"]');
          if (modal) {
            modal.classList.add('hidden');
          }
        });
      });

      // Initialize conversation form if the function exists
      if (typeof window.initializeConversationForm === 'function') {
        window.initializeConversationForm();
      }

      // Initialize message type to default state
      if (typeof window.setMessageType === 'function') {
        const internalBtn = document.getElementById('internal-note-btn');
        if (internalBtn) {
          window.setMessageType('internal', internalBtn);
        }
      }
    }, 200);
  }

  // Handle browser back/forward buttons
  window.addEventListener('popstate', function(event) {
    // Reload the page when user uses browser navigation
    // This ensures proper state restoration
    window.location.reload();
  });
});
