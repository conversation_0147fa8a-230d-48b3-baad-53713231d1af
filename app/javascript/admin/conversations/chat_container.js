// Global variables to track current Pusher connection
let currentPusherConnection = null;
let currentPusherChannel = null;

// Function to disconnect from current Pusher channel
function disconnectPusherChannel() {
  if (currentPusherChannel && currentPusherConnection) {
    // Unsubscribe from the channel
    currentPusherConnection.unsubscribe(currentPusherChannel.name);
    console.log('Disconnected from Pusher channel:', currentPusherChannel.name);

    // Clear the references
    currentPusherChannel = null;
    currentPusherConnection = null;
  }
}

// Function to initialize chat container (available globally for AJAX reloads)
function initializeChatContainer() {
  if ($('#chat-container').length === 0) {
    return;
  }

  // Disconnect from previous channel if exists
  disconnectPusherChannel();

  // Get conversation ID and subscribe only to this conversation's channel
  const conversationId = $('#chat-messages').data('conversation-id');
  const channelName = `private-conversation-${conversationId}`;

  const [pusher, channel] = connectPusherActions(channelName);

  // Store the connection references globally for disconnection
  currentPusherConnection = pusher;
  currentPusherChannel = channel;

  console.log('Connected to Pusher channel:', channelName);

  // Listen for new messages
  channel.bind("new_message", (data) => {
    // Check if this message should be excluded for this session
    if (data.exclude_sessions && data.exclude_sessions.includes(pusher.sessionID.toString())) {
      return;
    }

    // Fetch the message content via AJAX
    fetch(`/admin/conversations/${data.conversation_id}/message_content?message_id=${data.message_id}`)
      .then(response => response.text())
      .then(html => {
        const $chatMessages = $('#chat-messages');
        const $newMessage = $(html);

        // If current user is author, change the classes
        if ($newMessage.hasClass("practice-message") && $newMessage.data("author-id") == window._currentUserId) {
          $newMessage.addClass("outbound-message").removeClass("practice-message");
        }

        $chatMessages.prepend($newMessage);

        // Auto-scroll to bottom (with flex-col-reverse, newest messages are visually at bottom)
        const chatContainer = document.getElementById('chat-container');
        if (chatContainer) {
          chatContainer.scrollTop = chatContainer.scrollHeight;
        }
      })
      .catch(error => {
        console.error('Error fetching message content:', error);
      });
  });

  channel.bind("delivery_status_update", (data) => {
    updateDeliveryStatus(data.message_id, data.delivery_status, data.failure_reason);
  });

  // Initialize read status toggle handlers
  initializeReadStatusToggles();
}

function initializeReadStatusToggles() {
  // Remove existing handlers to prevent duplicates
  $(document).off('click', '.read-status-toggle');

  // Handle read status toggle clicks
  $(document).on('click', '.read-status-toggle', function(e) {
    e.preventDefault();

    const $button = $(this);
    const $container = $button.closest('.read-status-container');
    const $icon = $button.find('svg');
    const $label = $container.find('.read-status-label');
    const url = $button.data('url');
    const currentStatus = $container.data('read-status');

    // Disable button during request
    $button.addClass('opacity-50 pointer-events-none');

    // Make AJAX request
    fetch(url, {
      method: 'POST',
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content'),
        'Accept': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Use the response status to determine the new state
        const newStatus = data.status;

        if (newStatus === 'read') {
          // Change to read state
          $container.attr('data-read-status', 'read');
          $container.data('read-status', 'read'); // Also update jQuery data cache
          $icon.removeClass('single-tick text-gray-400').addClass('double-tick text-blue-500');
          $icon.html(`
            <path d="M18 6 L7 17 L2 12"></path>
            <path d="M22 10 L13 19 L11 17"></path>
          `);
          $label.removeClass('text-gray-500').addClass('text-blue-500').text('Read');

          // Update button URL for next toggle
          const messageId = $container.data('message-id');
          const conversationId = window.location.pathname.split('/').pop();
          const newUrl = `/admin/conversations/${conversationId}/mark_as_unread?message_id=${messageId}`;
          $button.attr('data-url', newUrl);
          $button.data('url', newUrl); // Also update jQuery data cache
          console.log('Updated to read state, new URL:', newUrl);
        } else if (newStatus === 'unread') {
          // Change to unread state
          $container.attr('data-read-status', 'unread');
          $container.data('read-status', 'unread'); // Also update jQuery data cache
          $icon.removeClass('double-tick text-blue-500').addClass('single-tick text-gray-400');
          $icon.html(`
            <polyline points="20 6 9 17 4 12"></polyline>
          `);
          $label.removeClass('text-blue-500').addClass('text-gray-500').text('Unread');

          // Update button URL for next toggle
          const messageId = $container.data('message-id');
          const conversationId = window.location.pathname.split('/').pop();
          const newUrl = `/admin/conversations/${conversationId}/mark_as_read?message_id=${messageId}`;
          $button.attr('data-url', newUrl);
          $button.data('url', newUrl); // Also update jQuery data cache
        }

      }
    })
    .catch(error => {
      console.error('Error toggling read status:', error);
    })
    .finally(() => {
      // Re-enable button
      $button.removeClass('opacity-50 pointer-events-none');
    });
  });
}

function updateDeliveryStatus(messageId, deliveryStatus, failureReason) {
  // Get conversation ID from the current page
  const conversationId = window.location.pathname.split('/').pop();

  // Fetch updated message partial from server
  fetch(`/admin/conversations/${conversationId}/message_content?message_id=${messageId}`)
    .then(response => response.text())
    .then(html => {
      const $existingMessage = $(`[data-message-id="${messageId}"]`).closest('.mb-6');

      if ($existingMessage.length > 0) {
        const $newMessage = $(html);

        // If current user is author, change the classes
        if ($newMessage.hasClass("practice-message") && $newMessage.data("author-id") == window._currentUserId) {
          $newMessage.addClass("outbound-message").removeClass("practice-message");
        }

        // Replace the existing message with the updated one
        $existingMessage.replaceWith($newMessage);

        tippy("[data-tippy-content]", {
          allowHTML: true,
          theme: "light"
        });
      }
    })
    .catch(error => {
      console.error('Error fetching updated message partial:', error);
    });
}

// Make chat container initialization available globally
window.initializeChatContainer = initializeChatContainer;
window.initializeReadStatusToggles = initializeReadStatusToggles;

// Make disconnect function available globally
window.disconnectPusherChannel = disconnectPusherChannel;

$(document).ready(function() {
  // Initialize chat container on page load
  initializeChatContainer();
});
