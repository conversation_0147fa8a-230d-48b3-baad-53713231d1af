document.addEventListener('DOMContentLoaded', function() {
  // Message type selection functionality
  function setMessageType(type, clickedElement, event) {
    // Prevent default behavior
    if (event) event.preventDefault();
    if (event) event.stopPropagation();
    // Get message form and send button
    const messageForm = document.getElementById('message-form');
    const sendButton = document.getElementById('send-message-btn');
    const messageTypeInput = messageForm.querySelector('input[name="message_type"]');
    const whatsappTemplatesBlock = document.getElementById('whatsapp-templates-block');
    const messageEditor = document.getElementById('message-editor');

    // Hide WhatsApp templates block by default
    if (whatsappTemplatesBlock) {
      whatsappTemplatesBlock.classList.add('hidden');
    }

    // Show TinyMCE editor by default
    if (messageEditor && messageEditor.style) {
      messageEditor.style.display = '';
    }

    // Remove active class from all buttons and divs
    document.querySelectorAll('.message-type-button').forEach(element => {
      element.classList.remove('active-type');
      element.classList.add('bg-white', 'text-gray-600');
      element.classList.remove('bg-gradient-to-r');

      // Reset the text span to collapsed state
      const textSpan = element.querySelector('.button-text');
      if (textSpan && !element.isSameNode(clickedElement)) {
        textSpan.classList.add('opacity-0', 'w-0');
        textSpan.classList.remove('ml-2');
      }
    });

    // Add active class to clicked element
    clickedElement.classList.add('active-type');
    clickedElement.classList.remove('bg-white', 'text-gray-600');

    // Apply specific styling based on message type
    if (type === 'internal') {
      clickedElement.classList.add('bg-gradient-to-r', 'from-amber-200', 'to-amber-300', 'text-amber-800');
      sendButton.textContent = 'Save Internal Note';

      sendButton.classList.remove('bg-purple-300', 'text-purple-800')
      sendButton.classList.remove('bg-blue-300', 'text-blue-800');
      sendButton.classList.remove('bg-green-300', 'text-green-800');
      sendButton.classList.add('bg-amber-400', 'text-white');

      messageTypeInput.value = 'internal';
      updateSenderNotification('internal');
    } else if (type === 'messaging') {
      // Get account type for specific styling
      const accountType = clickedElement.dataset.accountType;
      const accountName = clickedElement.dataset.accountName;

      if (accountType === 'whatsapp') {
        // Green for WhatsApp
        clickedElement.classList.add('bg-gradient-to-r', 'from-green-200', 'to-green-300', 'text-green-800');
        sendButton.textContent = 'Send Whatsapp';

        sendButton.classList.remove('bg-purple-300', 'text-purple-800')
        sendButton.classList.remove('bg-blue-300', 'text-blue-800');
        sendButton.classList.remove('bg-amber-400', 'text-white');
        sendButton.classList.add('bg-green-300', 'text-green-800');

        // Show WhatsApp templates block
        if (whatsappTemplatesBlock) {
          whatsappTemplatesBlock.classList.remove('hidden');
        }
      } else {
        sendButton.textContent = 'Send SMS';

        sendButton.classList.remove('bg-purple-300', 'text-purple-800')
        sendButton.classList.remove('bg-amber-400', 'text-white');
        sendButton.classList.remove('bg-green-300', 'text-green-800');
        sendButton.classList.add('bg-blue-300', 'text-blue-800');

        // Blue for SMS
        clickedElement.classList.add('bg-gradient-to-r', 'from-blue-200', 'to-blue-300', 'text-blue-800');
      }
      messageTypeInput.value = 'messaging';
      updateSenderNotification('messaging', accountName, accountType);
    } else if (type === 'email') {
      clickedElement.classList.add('bg-gradient-to-r', 'from-purple-200', 'to-purple-300', 'text-purple-800');
      sendButton.textContent = 'Send Email';

      sendButton.classList.remove('bg-blue-300', 'text-blue-800');
      sendButton.classList.remove('bg-amber-400', 'text-white');
      sendButton.classList.remove('bg-green-300', 'text-green-800');
      sendButton.classList.add('bg-purple-300', 'text-purple-800');

      messageTypeInput.value = 'email';
      updateSenderNotification('email', clickedElement.dataset.accountName);
    }

    // Expand the text for the active element
    const textSpan = clickedElement.querySelector('.button-text');
    if (textSpan) {
      textSpan.classList.remove('opacity-0', 'w-0');
      textSpan.classList.add('ml-2');
    }

    const senderInput = messageForm.querySelector('input[name="sender"]');
    if (senderInput) {
      senderInput.value = clickedElement.dataset.accountId || '';
    } else {
      // Create the sender input if it doesn't exist
      const newSenderInput = document.createElement('input');
      newSenderInput.type = 'hidden';
      newSenderInput.name = 'sender';
      newSenderInput.value = clickedElement.dataset.accountId || '';
      messageForm.appendChild(newSenderInput);
    }

    document.dispatchEvent(new CustomEvent('messageTypeChanged', {
      detail: { type: type, accountId: clickedElement.dataset.accountId }
    }));
  }

  // Make setMessageType available globally
  window.setMessageType = setMessageType;

  const sendButton = document.getElementById('send-message-btn');
  const messageForm = document.getElementById('message-form');
  const internalNoteBtn = document.getElementById('internal-note-btn');
  const senderNotification = document.getElementById('sender-notification');
  const senderIndicator = document.getElementById('sender-indicator');
  const messageTypeText = document.getElementById('message-type-text');

  // Update sender notification based on message type
  function updateSenderNotification(type, accountName, accountType) {
    const messageTypeText = document.getElementById('message-type-text');
    const senderIndicator = document.getElementById('sender-indicator');

    if (type === 'internal') {
      // Amber for internal notes with file-text icon
      senderNotification.className = 'bg-amber-100 rounded-lg p-3 text-sm transition-colors duration-300';
      senderIndicator.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-4 w-4 text-amber-700">
          <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
          <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
          <path d="M10 9H8"></path>
          <path d="M16 13H8"></path>
          <path d="M16 17H8"></path>
        </svg>
      `;
      messageTypeText.textContent = 'You are sending an internal note';
    } else if (type === 'messaging') {
      // Check account type to determine platform
      if (accountType === 'whatsapp') {
        // Green for WhatsApp with message-circle icon
        senderNotification.className = 'bg-green-100 rounded-lg p-3 text-sm transition-colors duration-300';
        senderIndicator.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle h-4 w-4 text-green-700">
            <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"></path>
          </svg>
        `;
      } else if (accountType === 'facebook') {
        // Blue for Facebook with message-square icon
        senderNotification.className = 'bg-blue-100 rounded-lg p-3 text-sm transition-colors duration-300';
        senderIndicator.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4 text-blue-700">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
          </svg>
        `;
      } else if (accountType === 'instagram') {
        // Purple for Instagram with message-square icon
        senderNotification.className = 'bg-purple-100 rounded-lg p-3 text-sm transition-colors duration-300';
        senderIndicator.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4 text-purple-700">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
          </svg>
        `;
      } else {
        // Default blue for SMS with message-square icon
        senderNotification.className = 'bg-blue-100 rounded-lg p-3 text-sm transition-colors duration-300';
        senderIndicator.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4 text-blue-700">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
          </svg>
        `;
      }
      messageTypeText.textContent = 'You are sending a message via ' + (accountName || 'SMS/WhatsApp');
    } else if (type === 'email') {
      // Gray for email with mail icon
      senderNotification.className = 'bg-gray-100 rounded-lg p-3 text-sm transition-colors duration-300';
      senderIndicator.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-4 w-4 text-gray-700">
          <rect width="20" height="16" x="2" y="4" rx="2"></rect>
          <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
        </svg>
      `;
      messageTypeText.textContent = 'You are sending an email via ' + (accountName || 'Email');
    }
  }

  // Initialize the UI with the default message type
  function initializeMessageType() {
    // Check if there's already an active button from server-side auto-selection
    const alreadyActiveButton = document.querySelector('.message-type-button.active-type');

    if (alreadyActiveButton) {
      // Use the server-side selected button
      const buttonType = alreadyActiveButton.dataset.type;
      setMessageType(buttonType, alreadyActiveButton);
    } else {
      // Fallback to internal note as default if no server-side selection
      if (internalNoteBtn) {
        setMessageType('internal', internalNoteBtn);
      }
    }

    // Update template button visibility after initialization
    updateTemplateButtonVisibility();
  }

  // Handle send button click
  function sendMessage() {
    // Get the active message type and account ID
    let activeType = 'internal'; // Default
    let accountId = null;

    // Make sure TinyMCE content is updated to the textarea
    if (typeof tinymce !== 'undefined' && tinymce.get('message-editor')) {
      tinymce.get('message-editor').save();
    }

    // Find the active element (button or div)
    const activeElement = document.querySelector('.message-type-button.active-type');
    if (activeElement) {
      activeType = activeElement.dataset.type;
    }

    // Get account ID from the sender input
    const senderInput = messageForm.querySelector('input[name="sender"]');
    if (senderInput && senderInput.value) {
      accountId = senderInput.value;
    }

    // Update the hidden input for message type
    const messageTypeInput = messageForm.querySelector('input[name="message_type"]');
    if (messageTypeInput) {
      messageTypeInput.value = activeType;
    }

    // Add template ID if WhatsApp template is selected
    const whatsappTemplateSelect = document.getElementById('whatsapp-template-select');
    if (whatsappTemplateSelect && whatsappTemplateSelect.value) {
      let templateIdInput = messageForm.querySelector('input[name="template_id"]');
      if (!templateIdInput) {
        templateIdInput = document.createElement('input');
        templateIdInput.type = 'hidden';
        templateIdInput.name = 'template_id';
        messageForm.appendChild(templateIdInput);
      }
      templateIdInput.value = whatsappTemplateSelect.value;
    }

    const formData = new FormData(messageForm);

    const submitButton = sendButton;
    const originalButtonText = submitButton.textContent;

    submitButton.disabled = true;
    submitButton.textContent = 'Sending...';

    fetch(messageForm.action, {
      method: 'POST',
      body: formData,
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      }
    })
    .then(response => {
      if (response.ok) {
        messageForm.reset();

        if (tinymce.get('message-editor')) {
          tinymce.get('message-editor').setContent('');
        }

        // Reset WhatsApp template selection
        const whatsappTemplateSelect = document.getElementById('whatsapp-template-select');
        if (whatsappTemplateSelect) {
          whatsappTemplateSelect.value = '';
          whatsappTemplateSelect.selectedIndex = 0;

          // Reset template-related UI states
          const templatePreview = document.getElementById('template-preview');
          if (templatePreview) {
            templatePreview.classList.add('hidden');
          }

          // Remove template-selected class from message form
          messageForm.classList.remove('template-selected');

          // Remove any template_id hidden input
          const templateIdInput = messageForm.querySelector('input[name="template_id"]');
          if (templateIdInput) {
            templateIdInput.remove();
          }
        }

        if (typeof toastr !== 'undefined') {
          toastr.success('Message sent successfully');
        }
      } else {
        throw new Error('Failed to send message');
      }
    })
    .catch(error => {
      console.error('Error sending message:', error);
      if (typeof toastr !== 'undefined') {
        toastr.error('Failed to send message. Please try again.');
      }
    })
    .finally(() => {
      submitButton.disabled = false;
      submitButton.textContent = originalButtonText;
    });
  }

  sendButton.addEventListener('click', sendMessage);

  // Add event listeners to all message type buttons
  // Internal note button
  document.getElementById('internal-note-btn').addEventListener('click', function(e) {
    e.preventDefault();
    setMessageType('internal', this, e);
    return false;
  });

  // Messaging buttons
  document.querySelectorAll('[id^="messaging-btn-"]').forEach(button => {
    button.addEventListener('click', function(e) {
      e.preventDefault();
      setMessageType('messaging', this, e);
      return false;
    });
  });

  // Email buttons
  document.querySelectorAll('[id^="email-account-"]').forEach(button => {
    button.addEventListener('click', function(e) {
      e.preventDefault();
      setMessageType('email', this, e);
      return false;
    });
  });

  // Template selection functionality
  const templateSelectorBtn = document.getElementById('template-selector-btn');
  const templateModal = document.getElementById('template-modal');
  const closeTemplateModal = document.getElementById('close-template-modal');
  const templateLoading = document.getElementById('template-loading');
  const templateList = document.getElementById('template-list');
  const templateEmpty = document.getElementById('template-empty');
  const templateNoResults = document.getElementById('template-no-results');
  const templateSearch = document.getElementById('template-search');

  let allTemplates = []; // Store all templates for search functionality

  // Show/hide template button based on selected message type
  function updateTemplateButtonVisibility() {
    const activeButton = document.querySelector('.message-type-button.active-type');
    if (activeButton && (activeButton.dataset.type === 'messaging' || activeButton.dataset.type === 'email')) {
      templateSelectorBtn.classList.remove('hidden');
    } else {
      templateSelectorBtn.classList.add('hidden');
    }
  }

  // Filter templates based on search input
  function filterTemplates(searchTerm) {
    const filteredTemplates = allTemplates.filter(template => {
      const searchText = searchTerm.toLowerCase();
      return template.name.toLowerCase().includes(searchText) ||
             (template.subject && template.subject.toLowerCase().includes(searchText)) ||
             template.text.toLowerCase().includes(searchText);
    });

    displayTemplates(filteredTemplates);
  }

  // Display templates in the list
  function displayTemplates(templates) {
    templateList.innerHTML = '';
    templateEmpty.classList.add('hidden');
    templateNoResults.classList.add('hidden');

    if (templates.length === 0) {
      if (templateSearch.value.trim() !== '') {
        templateNoResults.classList.remove('hidden');
      } else {
        templateEmpty.classList.remove('hidden');
      }
      templateList.classList.add('hidden');
    } else {
      templates.forEach(template => {
        const templateItem = createTemplateItem(template);
        templateList.appendChild(templateItem);
      });
      templateList.classList.remove('hidden');
    }
  }

  // Load templates for selected communication account
  function loadTemplates(accountId) {
    templateLoading.classList.remove('hidden');
    templateList.classList.add('hidden');
    templateEmpty.classList.add('hidden');
    templateNoResults.classList.add('hidden');
    templateSearch.value = ''; // Clear search input

    const conversationId = document.getElementById('chat-messages').dataset.conversationId;

    fetch(`/admin/conversations/${conversationId}/notification_templates?account_id=${accountId}`)
      .then(response => response.json())
      .then(templates => {
        allTemplates = templates; // Store all templates for search
        templateLoading.classList.add('hidden');
        displayTemplates(templates);
      })
      .catch(error => {
        console.error('Error loading templates:', error);
        templateLoading.classList.add('hidden');
        templateEmpty.classList.remove('hidden');
      });
  }

  // Create template item HTML
  function createTemplateItem(template) {
    const div = document.createElement('div');
    div.className = 'border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:bg-blue-50 cursor-pointer transition-colors';
    div.dataset.templateId = template.id;

    div.innerHTML = `
      <div class="flex justify-between items-start mb-2">
        <h4 class="font-medium text-gray-900">${template.name}</h4>
        <button type="button" class="text-blue-600 hover:text-blue-800 text-sm font-medium">Select</button>
      </div>
      ${template.subject ? `<p class="text-sm text-gray-700 mb-2"><strong>Subject:</strong> ${template.subject}</p>` : ''}
      <p class="text-sm text-gray-600">${template.preview}</p>
    `;

    div.addEventListener('click', () => selectTemplate(template));

    return div;
  }

  function selectTemplate(template) {
    const editor = tinymce.get('message-editor');
    if (!editor) {
      console.error('TinyMCE editor not found');
      return;
    }

    // Get current conversation ID from the chat messages element
    const chatMessages = document.getElementById('chat-messages');
    const conversationId = chatMessages ? chatMessages.dataset.conversationId : null;

    if (!conversationId) {
      console.error('Missing conversation ID for template processing');
      if (typeof toastr !== 'undefined') {
        toastr.error('Unable to process template - missing conversation data');
      }
      return;
    }

    // Show loading state
    if (typeof toastr !== 'undefined') {
      toastr.info('Processing template...');
    }

    // Make AJAX call to get processed template content
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    
    fetch(`/admin/conversations/${conversationId}/template_content`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        template_id: template.id
      })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      if (data.error) {
        throw new Error(data.error);
      }

      // Insert processed template text into TinyMCE
      editor.setContent(data.processed_text || template.text);

      // Update word count
      const content = editor.getContent({ format: 'text' });
      const wordCount = content.trim() ? content.split(/\s+/).length : 0;
      const wordCountElement = document.querySelector('.text-xs.text-gray-400');
      if (wordCountElement) {
        wordCountElement.textContent = wordCount + ' words';
      }

      // If template has a subject, add it to the form for email messages
      const activeButton = document.querySelector('.message-type-button.active-type');
      if (activeButton && activeButton.dataset.type === 'email' && template.subject) {
        const messageForm = document.getElementById('message-form');
        let subjectInput = messageForm.querySelector('input[name="subject"]');
        if (!subjectInput) {
          subjectInput = document.createElement('input');
          subjectInput.type = 'hidden';
          subjectInput.name = 'subject';
          messageForm.appendChild(subjectInput);
        }
        subjectInput.value = template.subject;
      }

      // Close modal
      templateModal.classList.add('hidden');

      // Show success message
      if (typeof toastr !== 'undefined') {
        toastr.success('Template inserted successfully');
      }
    })
    .catch(error => {
      console.error('Error processing template:', error);
      
      // Fallback to original template text if AJAX fails
      editor.setContent(template.text);

      // Update word count
      const content = editor.getContent({ format: 'text' });
      const wordCount = content.trim() ? content.split(/\s+/).length : 0;
      const wordCountElement = document.querySelector('.text-xs.text-gray-400');
      if (wordCountElement) {
        wordCountElement.textContent = wordCount + ' words';
      }

      // Close modal
      templateModal.classList.add('hidden');

      // Show error message
      if (typeof toastr !== 'undefined') {
        toastr.warning('Template inserted with original text - processing failed');
      }
    });
  }

  // Template selector button click
  if (templateSelectorBtn) {
    templateSelectorBtn.addEventListener('click', function() {
      const activeButton = document.querySelector('.message-type-button.active-type');
      if (activeButton) {
        const accountId = activeButton.dataset.accountId;
        if (accountId) {
          templateModal.classList.remove('hidden');
          loadTemplates(accountId);
        }
      }
    });
  }

  // Close template modal
  if (closeTemplateModal) {
    closeTemplateModal.addEventListener('click', function() {
      templateModal.classList.add('hidden');
    });
  }

  // Close modal when clicking outside
  if (templateModal) {
    templateModal.addEventListener('click', function(e) {
      if (e.target === templateModal) {
        templateModal.classList.add('hidden');
      }
    });
  }

  // Search input event listener
  if (templateSearch) {
    templateSearch.addEventListener('input', debounce(function(e) {
      const searchValue = e.target.value || '';
      console.log('Search input changed:', searchValue);
      filterTemplates(searchValue);
    }, 300));
  }

  // Initialize the UI
  initializeMessageType();

  // Update template button visibility when message type changes
  document.addEventListener('messageTypeChanged', updateTemplateButtonVisibility);

  // Message type filter functionality
  const filterButtons = document.querySelectorAll('.filter-button');
  if (filterButtons.length > 0) {
    filterButtons.forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        const filterType = this.dataset.filterType;
        setActiveFilter(filterType, this);
        filterMessagesByType(filterType);
        return false;
      });
    });
  }

  // Function to set active filter button
  function setActiveFilter(type, clickedButton) {
    // Remove active class from all filter buttons
    document.querySelectorAll('.filter-button').forEach(btn => {
      btn.classList.remove('active-filter');
      btn.classList.add('bg-white', 'text-gray-600');
      btn.classList.remove('bg-gradient-to-r');

      // Reset the text span to collapsed state
      const textSpan = btn.querySelector('.filter-text');
      if (textSpan && !btn.isSameNode(clickedButton)) {
        textSpan.classList.add('opacity-0', 'w-0');
        textSpan.classList.remove('ml-2');
      }
    });

    // Add active class to clicked button
    clickedButton.classList.add('active-filter');
    clickedButton.classList.remove('bg-white', 'text-gray-600');

    // Apply specific styling based on filter type
    if (type === 'all') {
      clickedButton.classList.add('bg-gradient-to-r', 'from-gray-200', 'to-gray-300', 'text-gray-800');
    } else if (type === 'internal') {
      clickedButton.classList.add('bg-gradient-to-r', 'from-amber-200', 'to-amber-300', 'text-amber-800');
    } else if (type === 'messaging') {
      clickedButton.classList.add('bg-gradient-to-r', 'from-blue-200', 'to-blue-300', 'text-blue-800');
    } else if (type === 'email') {
      clickedButton.classList.add('bg-gradient-to-r', 'from-purple-200', 'to-purple-300', 'text-purple-800');
    }

    // Expand the text for the active button
    const textSpan = clickedButton.querySelector('.filter-text');
    if (textSpan) {
      textSpan.classList.remove('opacity-0', 'w-0');
      textSpan.classList.add('ml-2');
    }
  }

  // Function to filter messages by type
  function filterMessagesByType(type) {
    // Get all message containers
    const messageContainers = document.querySelectorAll('#chat-messages > div');

    if (type === 'all') {
      // Show all messages
      messageContainers.forEach(container => {
        container.style.display = '';
      });
    } else {
      // Filter messages by type
      messageContainers.forEach(container => {
        const messageBubble = container.querySelector('.message-bubble');
        if (messageBubble && messageBubble.dataset.messageType === type) {
          container.style.display = '';
        } else {
          container.style.display = 'none';
        }
      });
    }
    scrollToBottom()
  }

  function scrollToBottom() {
    const chatContainer = document.getElementById('chat-container');
    if (chatContainer) {
      // With flex-col-reverse, newest messages are visually at bottom
      // To see newest messages, scroll to maximum position
      chatContainer.scrollTop = chatContainer.scrollHeight;
    }
  }

  tinymce.on('AddEditor', function(e) {
    const editor = e.editor;
    const wordCountElement = document.querySelector('.text-xs.text-gray-400');

    editor.on('postRender', function() { scrollToBottom() });

    editor.on('KeyUp', function() {
      const content = editor.getContent({ format: 'text' });
      const wordCount = content.trim() ? content.split(/\s+/).length : 0;
      wordCountElement.textContent = wordCount + ' words';
    });
  });

  // Function to initialize TinyMCE (available globally for AJAX reloads)
  function initializeTinyMCE() {
    var plugins = [
      'ai', 'anchor', 'autolink', 'charmap', 'codesample', 'emoticons', 'image', 'link', 'lists', 'media', 'searchreplace', 'table', 'visualblocks', 'wordcount',
      'checklist', 'mediaembed', 'casechange', 'export', 'formatpainter', 'pageembed', 'a11ychecker', 'tinymcespellchecker', 'permanentpen', 'powerpaste',
      'advtable', 'advcode', 'editimage', 'advtemplate', 'mentions', 'tinycomments', 'tableofcontents', 'footnotes', 'mergetags', 'autocorrect',
      'typography', 'inlinecss', 'markdown'
    ];

    var toolbar = 'mergetags aidialog aishortcuts | undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat';


    tinymce.init({
      selector: '#message-editor',
      height: 250,
      plugins: plugins,
      toolbar: toolbar,
      tinycomments_mode: 'embedded',
      tinycomments_author: 'Author name',
      license_key: 'h8ksxv4rsqlbi0orn42moa9x8kh5ds8kbykaucvfdjnypo4u',
      branding: false,
      promotion: false,
      ai_request: (request, respondWith) => {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        respondWith.string((signal) =>
          fetch('/admin/ai_tools/improve_text', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': csrfToken
            },
            body: JSON.stringify({text: request.prompt}),
            signal
          })
            .then(response => {
              if (!response.ok) {
                throw new Error(`Request failed with status ${response.status}`);
              }
              return response.json();
            })
            .then(data => data.text)
        );
      },
      setup: function(editor) {
        editor.on('change', function() {
          editor.save(); // Update textarea with content
        });
      }
    });
  }

  // Make TinyMCE initialization available globally
  window.initializeTinyMCE = initializeTinyMCE;

  // Initialize TinyMCE on page load
  initializeTinyMCE();

  // WhatsApp Templates functionality
  const whatsappTemplateSelect = document.getElementById('whatsapp-template-select');
  const templatePreview = document.getElementById('template-preview');
  const templatePreviewContent = document.getElementById('template-preview-content');
  const deselectTemplateBtn = document.getElementById('deselect-template-btn');
  const messageEditor = document.getElementById('message-editor');

  // Handle template selection
  if (whatsappTemplateSelect) {
    // Remove existing event listeners by cloning and replacing
    const newWhatsappTemplateSelect = whatsappTemplateSelect.cloneNode(true);
    whatsappTemplateSelect.parentNode.replaceChild(newWhatsappTemplateSelect, whatsappTemplateSelect);

    newWhatsappTemplateSelect.addEventListener('change', function() {
      const selectedOption = this.options[this.selectedIndex];

      if (selectedOption.value) {
        // Show template preview
        if (templatePreview) {
          templatePreview.classList.remove('hidden');
        }

        // Populate preview with template content
        if (templatePreviewContent) {
          templatePreviewContent.innerHTML = selectedOption.dataset.preview || selectedOption.text;
        }

        // Hide TinyMCE editor
        const messageForm = document.getElementById('message-form');
        if (messageForm) {
          messageForm.classList.add('template-selected');
        }

        // Add template_id hidden input
        let templateIdInput = messageForm.querySelector('input[name="template_id"]');
        if (!templateIdInput) {
          templateIdInput = document.createElement('input');
          templateIdInput.type = 'hidden';
          templateIdInput.name = 'template_id';
          messageForm.appendChild(templateIdInput);
        }
        templateIdInput.value = selectedOption.value;

        // Clear content field when template is selected (we'll send template_id instead)
        if (messageEditor) {
          messageEditor.value = '';
        }

        // Set TinyMCE content to empty when template is selected
        if (typeof tinymce !== 'undefined') {
          const editor = tinymce.get('message-editor');
          if (editor) {
            editor.setContent('');
          }
        }
      } else {
        // Show TinyMCE editor
        const messageForm = document.getElementById('message-form');
        if (messageForm) {
          messageForm.classList.remove('template-selected');
        }

        // Remove template_id parameter
        const templateIdInput = messageForm.querySelector('input[name="template_id"]');
        if (templateIdInput) {
          templateIdInput.remove();
        }

        // Clear TinyMCE content
        if (typeof tinymce !== 'undefined') {
          const editor = tinymce.get('message-editor');
          if (editor) {
            editor.setContent('');
          }
        }

        // Clear content field
        if (messageEditor) {
          messageEditor.value = '';
        }

        // Hide template preview
        if (templatePreview) {
          templatePreview.classList.add('hidden');
        }
      }
    });
  }

  // Handle deselect template button
  if (deselectTemplateBtn) {
    // Remove existing event listeners by cloning and replacing
    const newDeselectTemplateBtn = deselectTemplateBtn.cloneNode(true);
    deselectTemplateBtn.parentNode.replaceChild(newDeselectTemplateBtn, deselectTemplateBtn);

    newDeselectTemplateBtn.addEventListener('click', function(e) {
      e.preventDefault();

      // Reset template selection
      const currentWhatsappTemplateSelect = document.getElementById('whatsapp-template-select');
      if (currentWhatsappTemplateSelect) {
        currentWhatsappTemplateSelect.selectedIndex = 0;
      }

      // Remove template_id parameter
      const messageForm = document.getElementById('message-form');
      const templateIdInput = messageForm.querySelector('input[name="template_id"]');
      if (templateIdInput) {
        templateIdInput.remove();
      }

      // Show TinyMCE editor
      if (messageForm) {
        messageForm.classList.remove('template-selected');
      }

      // Clear TinyMCE content
      if (typeof tinymce !== 'undefined') {
        const editor = tinymce.get('message-editor');
        if (editor) {
          editor.setContent('');
        }
      }

      // Clear content field
      if (messageEditor) {
        messageEditor.value = '';
      }

      // Hide template preview
      if (templatePreview) {
        templatePreview.classList.add('hidden');
      }
    });
  }

  // Make conversation form initialization available globally
  window.initializeConversationForm = function() {
    // The main initialization is already in the DOMContentLoaded event listener
    // This function can be used to reinitialize specific components after AJAX loads

    // Reinitialize template functionality variables
    const templateSelectorBtn = document.getElementById('template-selector-btn');
    const templateModal = document.getElementById('template-modal');
    const closeTemplateModal = document.getElementById('close-template-modal');
    const templateLoading = document.getElementById('template-loading');
    const templateList = document.getElementById('template-list');
    const templateEmpty = document.getElementById('template-empty');
    const templateNoResults = document.getElementById('template-no-results');
    const templateSearch = document.getElementById('template-search');

    let allTemplates = []; // Store all templates for search functionality

    // Redefine template functions for reinitialization
    function displayTemplates(templates) {
      if (!templateList) return;

      templateList.innerHTML = '';
      templateEmpty.classList.add('hidden');
      templateNoResults.classList.add('hidden');

      if (templates.length === 0) {
        if (templateSearch.value.trim() !== '') {
          templateNoResults.classList.remove('hidden');
        } else {
          templateEmpty.classList.remove('hidden');
        }
        templateList.classList.add('hidden');
      } else {
        templates.forEach(template => {
          const templateItem = createTemplateItem(template);
          templateList.appendChild(templateItem);
        });
        templateList.classList.remove('hidden');
      }
    }

    function createTemplateItem(template) {
      const div = document.createElement('div');
      div.className = 'border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:bg-blue-50 cursor-pointer transition-colors';
      div.dataset.templateId = template.id;

      div.innerHTML = `
        <div class="flex justify-between items-start mb-2">
          <h4 class="font-medium text-gray-900">${template.name}</h4>
          <button type="button" class="text-blue-600 hover:text-blue-800 text-sm font-medium">Select</button>
        </div>
        ${template.subject ? `<p class="text-sm text-gray-700 mb-2"><strong>Subject:</strong> ${template.subject}</p>` : ''}
        <p class="text-sm text-gray-600">${template.preview}</p>
      `;

      div.addEventListener('click', () => selectTemplate(template));

      return div;
    }

    function selectTemplate(template) {
      const editor = tinymce.get('message-editor');
      if (!editor) {
        console.error('TinyMCE editor not found');
        return;
      }

      // Get current conversation ID from the chat messages element
      const chatMessages = document.getElementById('chat-messages');
      const conversationId = chatMessages ? chatMessages.dataset.conversationId : null;

      if (!conversationId) {
        console.error('Missing conversation ID for template processing');
        if (typeof toastr !== 'undefined') {
          toastr.error('Unable to process template - missing conversation data');
        }
        return;
      }

      // Show loading state
      if (typeof toastr !== 'undefined') {
        toastr.info('Processing template...');
      }

      // Make AJAX call to get processed template content
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
      
      fetch(`/admin/conversations/${conversationId}/template_content`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          template_id: template.id
        })
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.error) {
          throw new Error(data.error);
        }

        // Insert processed template text into TinyMCE
        editor.setContent(data.processed_text || template.text);

        // Update word count
        const content = editor.getContent({ format: 'text' });
        const wordCount = content.trim() ? content.split(/\s+/).length : 0;
        const wordCountElement = document.querySelector('.text-xs.text-gray-400');
        if (wordCountElement) {
          wordCountElement.textContent = wordCount + ' words';
        }

        // If template has a subject, add it to the form for email messages
        const activeButton = document.querySelector('.message-type-button.active-type');
        if (activeButton && activeButton.dataset.type === 'email' && template.subject) {
          const messageForm = document.getElementById('message-form');
          let subjectInput = messageForm.querySelector('input[name="subject"]');
          if (!subjectInput) {
            subjectInput = document.createElement('input');
            subjectInput.type = 'hidden';
            subjectInput.name = 'subject';
            messageForm.appendChild(subjectInput);
          }
          subjectInput.value = template.subject;
        }

        // Close modal
        templateModal.classList.add('hidden');

        // Show success message
        if (typeof toastr !== 'undefined') {
          toastr.success('Template inserted successfully');
        }
      })
      .catch(error => {
        console.error('Error processing template:', error);
        
        // Fallback to original template text if AJAX fails
        editor.setContent(template.text);

        // Update word count
        const content = editor.getContent({ format: 'text' });
        const wordCount = content.trim() ? content.split(/\s+/).length : 0;
        const wordCountElement = document.querySelector('.text-xs.text-gray-400');
        if (wordCountElement) {
          wordCountElement.textContent = wordCount + ' words';
        }

        // Close modal
        templateModal.classList.add('hidden');

        // Show error message
        if (typeof toastr !== 'undefined') {
          toastr.warning('Template inserted with original text - processing failed');
        }
      });
    }

    function loadTemplates(accountId) {
      if (!templateLoading || !templateList || !templateEmpty || !templateNoResults) return;

      templateLoading.classList.remove('hidden');
      templateList.classList.add('hidden');
      templateEmpty.classList.add('hidden');
      templateNoResults.classList.add('hidden');
      if (templateSearch) {
        templateSearch.value = ''; // Clear search input
      }

      const conversationId = document.getElementById('chat-messages').dataset.conversationId;

      fetch(`/admin/conversations/${conversationId}/notification_templates?account_id=${accountId}`)
        .then(response => response.json())
        .then(templates => {
          allTemplates = templates; // Store all templates for search
          templateLoading.classList.add('hidden');
          displayTemplates(templates);
        })
        .catch(error => {
          console.error('Error loading templates:', error);
          templateLoading.classList.add('hidden');
          templateEmpty.classList.remove('hidden');
        });
    }

    function filterTemplates(searchTerm) {
      const filteredTemplates = allTemplates.filter(template => {
        const searchText = searchTerm.toLowerCase();
        return template.name.toLowerCase().includes(searchText) ||
               (template.subject && template.subject.toLowerCase().includes(searchText)) ||
               template.text.toLowerCase().includes(searchText);
      });
      displayTemplates(filteredTemplates);
    }

    // Define updateTemplateButtonVisibility function for reinitialization
    function updateTemplateButtonVisibility() {
      const templateSelectorBtn = document.getElementById('template-selector-btn');
      const activeButton = document.querySelector('.message-type-button.active-type');
      if (templateSelectorBtn) {
        if (activeButton && (activeButton.dataset.type === 'messaging' || activeButton.dataset.type === 'email')) {
          templateSelectorBtn.classList.remove('hidden');
        } else {
          templateSelectorBtn.classList.add('hidden');
        }
      }
    }

    // Re-bind messageTypeChanged event listener
    document.addEventListener('messageTypeChanged', updateTemplateButtonVisibility);

    // Update template button visibility on initialization
    updateTemplateButtonVisibility();

    // Reinitialize WhatsApp template functionality
    const whatsappTemplateSelect = document.getElementById('whatsapp-template-select');
    const templatePreview = document.getElementById('template-preview');
    const templatePreviewContent = document.getElementById('template-preview-content');
    const deselectTemplateBtn = document.getElementById('deselect-template-btn');
    const messageEditor = document.getElementById('message-editor');

    // Handle template selection
    if (whatsappTemplateSelect) {
      // Remove existing event listeners by cloning and replacing
      const newWhatsappTemplateSelect = whatsappTemplateSelect.cloneNode(true);
      whatsappTemplateSelect.parentNode.replaceChild(newWhatsappTemplateSelect, whatsappTemplateSelect);

      newWhatsappTemplateSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];

        if (selectedOption.value) {
          // Show template preview
          if (templatePreview) {
            templatePreview.classList.remove('hidden');
          }

          // Populate preview with template content
          if (templatePreviewContent) {
            templatePreviewContent.innerHTML = selectedOption.dataset.preview || selectedOption.text;
          }

          // Hide TinyMCE editor
          const messageForm = document.getElementById('message-form');
          if (messageForm) {
            messageForm.classList.add('template-selected');
          }

          // Add template_id hidden input
          let templateIdInput = messageForm.querySelector('input[name="template_id"]');
          if (!templateIdInput) {
            templateIdInput = document.createElement('input');
            templateIdInput.type = 'hidden';
            templateIdInput.name = 'template_id';
            messageForm.appendChild(templateIdInput);
          }
          templateIdInput.value = selectedOption.value;

          // Clear content field when template is selected (we'll send template_id instead)
          if (messageEditor) {
            messageEditor.value = '';
          }

          // Set TinyMCE content to empty when template is selected
          if (typeof tinymce !== 'undefined') {
            const editor = tinymce.get('message-editor');
            if (editor) {
              editor.setContent('');
            }
          }
        } else {
          // Show TinyMCE editor
          const messageForm = document.getElementById('message-form');
          if (messageForm) {
            messageForm.classList.remove('template-selected');
          }

          // Remove template_id parameter
          const templateIdInput = messageForm.querySelector('input[name="template_id"]');
          if (templateIdInput) {
            templateIdInput.remove();
          }

          // Clear TinyMCE content
          if (typeof tinymce !== 'undefined') {
            const editor = tinymce.get('message-editor');
            if (editor) {
              editor.setContent('');
            }
          }

          // Clear content field
          if (messageEditor) {
            messageEditor.value = '';
          }

          // Hide template preview
          if (templatePreview) {
            templatePreview.classList.add('hidden');
          }
        }
      });
    }

    // Handle deselect template button
    if (deselectTemplateBtn) {
      // Remove existing event listeners by cloning and replacing
      const newDeselectTemplateBtn = deselectTemplateBtn.cloneNode(true);
      deselectTemplateBtn.parentNode.replaceChild(newDeselectTemplateBtn, deselectTemplateBtn);

      newDeselectTemplateBtn.addEventListener('click', function(e) {
        e.preventDefault();

        // Reset template selection
        const currentWhatsappTemplateSelect = document.getElementById('whatsapp-template-select');
        if (currentWhatsappTemplateSelect) {
          currentWhatsappTemplateSelect.selectedIndex = 0;
        }

        // Remove template_id parameter
        const messageForm = document.getElementById('message-form');
        const templateIdInput = messageForm.querySelector('input[name="template_id"]');
        if (templateIdInput) {
          templateIdInput.remove();
        }

        // Show TinyMCE editor
        if (messageForm) {
          messageForm.classList.remove('template-selected');
        }

        // Clear TinyMCE content
        if (typeof tinymce !== 'undefined') {
          const editor = tinymce.get('message-editor');
          if (editor) {
            editor.setContent('');
          }
        }

        // Clear content field
        if (messageEditor) {
          messageEditor.value = '';
        }

        // Hide template preview
        if (templatePreview) {
          templatePreview.classList.add('hidden');
        }
      });
    }

    // Reinitialize template selector button functionality
    if (templateSelectorBtn && templateModal && closeTemplateModal) {
      // Re-bind event listeners for template functionality
      templateSelectorBtn.replaceWith(templateSelectorBtn.cloneNode(true));
      closeTemplateModal.replaceWith(closeTemplateModal.cloneNode(true));

      const newTemplateSelectorBtn = document.getElementById('template-selector-btn');
      const newCloseTemplateModal = document.getElementById('close-template-modal');

      // Re-bind template selector button click
      newTemplateSelectorBtn.addEventListener('click', function() {
        const activeButton = document.querySelector('.message-type-button.active-type');
        if (activeButton) {
          const accountId = activeButton.dataset.accountId;
          if (accountId) {
            templateModal.classList.remove('hidden');
            loadTemplates(accountId);
          }
        }
      });

      // Re-bind close template modal
      newCloseTemplateModal.addEventListener('click', function() {
        templateModal.classList.add('hidden');
      });

      // Re-bind modal outside click
      templateModal.addEventListener('click', function(e) {
        if (e.target === templateModal) {
          templateModal.classList.add('hidden');
        }
      });

      // Re-bind search functionality
      if (templateSearch) {
        templateSearch.addEventListener('input', function(e) {
          const searchValue = e.target.value || '';
          console.log('Search input changed:', searchValue);
          filterTemplates(searchValue);
        });
      }
    }

    // Reinitialize message filters functionality
    initializeMessageFilters();

    // Reinitialize search functionality
    initializeMessageSearch();

    // Initialize read status toggles
    if (typeof window.initializeReadStatusToggles === 'function') {
      window.initializeReadStatusToggles();
    }
  };

  // Initialize message filters
  function initializeMessageFilters() {
    const filterButtons = document.querySelectorAll('.filter-button');

    if (filterButtons.length > 0) {
      filterButtons.forEach(button => {
        // Remove existing event listeners by cloning (to prevent duplicates)
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        newButton.addEventListener('click', function(e) {
          e.preventDefault();
          const filterType = this.dataset.filterType;
          setActiveFilter(filterType, this);
          filterMessagesByType(filterType);
          return false;
        });
      });
    }
  }

  // Function to set active filter button
  function setActiveFilter(type, clickedButton) {
    // Remove active class from all filter buttons
    document.querySelectorAll('.filter-button').forEach(btn => {
      btn.classList.remove('active-filter');
      btn.classList.add('bg-white', 'text-gray-600');
      btn.classList.remove('bg-gradient-to-r');

      // Reset the text span to collapsed state
      const textSpan = btn.querySelector('.filter-text');
      if (textSpan && !btn.isSameNode(clickedButton)) {
        textSpan.classList.add('opacity-0', 'w-0');
        textSpan.classList.remove('ml-2');
      }
    });

    // Add active class to clicked button
    clickedButton.classList.add('active-filter');
    clickedButton.classList.remove('bg-white', 'text-gray-600');

    // Apply specific styling based on filter type
    if (type === 'all') {
      clickedButton.classList.add('bg-gradient-to-r', 'from-gray-200', 'to-gray-300', 'text-gray-800');
    } else if (type === 'internal') {
      clickedButton.classList.add('bg-gradient-to-r', 'from-amber-200', 'to-amber-300', 'text-amber-800');
    } else if (type === 'messaging') {
      clickedButton.classList.add('bg-gradient-to-r', 'from-blue-200', 'to-blue-300', 'text-blue-800');
    } else if (type === 'email') {
      clickedButton.classList.add('bg-gradient-to-r', 'from-purple-200', 'to-purple-300', 'text-purple-800');
    }

    // Expand the text for the active button
    const textSpan = clickedButton.querySelector('.filter-text');
    if (textSpan) {
      textSpan.classList.remove('opacity-0', 'w-0');
      textSpan.classList.add('ml-2');
    }
  }

  // Function to filter messages by type
  function filterMessagesByType(type) {
    // Get all message containers
    const messageContainers = document.querySelectorAll('#chat-messages > div');

    if (type === 'all') {
      // Show all messages
      messageContainers.forEach(container => {
        container.style.display = '';
      });
    } else {
      // Filter messages by type
      messageContainers.forEach(container => {
        const messageBubble = container.querySelector('.message-bubble');
        if (messageBubble && messageBubble.dataset.messageType === type) {
          container.style.display = '';
        } else {
          container.style.display = 'none';
        }
      });
    }
    scrollToBottom()
  }

  // Initialize message search functionality
  function initializeMessageSearch() {
    const searchInput = document.querySelector('#conversation-message-search');
    if (!searchInput) {
      console.log('Search input not found');
      return;
    }

    // Get the messages container
    const messagesContainer = document.getElementById('chat-messages');
    if (!messagesContainer) {
      console.log('Messages container not found');
      return;
    }

    console.log('Initializing message search functionality');

    // Remove the default clear button's functionality
    searchInput.classList.add('search-no-clear');

    // Track the current highlighted message index
    let currentHighlightIndex = -1;
    let highlightedMessages = [];
    let searchMarkers = [];

    // Remove existing event listeners by cloning (to prevent duplicates)
    const newSearchInput = searchInput.cloneNode(true);
    searchInput.parentNode.replaceChild(newSearchInput, searchInput);

    console.log('Search input cloned and replaced');

    // Add event listener for input changes
    newSearchInput.addEventListener('input', debounce(function(e) {
      const searchValue = e.target.value || '';
      console.log('Search input changed:', searchValue);
      searchMessages(searchValue);
    }, 300));

    // Add event listener for Enter key to navigate to next result
    newSearchInput.addEventListener('keydown', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        console.log('Enter key pressed, navigating to next result');
        if (highlightedMessages.length > 0) {
          // Move to next result
          currentHighlightIndex = (currentHighlightIndex + 1) % highlightedMessages.length;
          navigateToMessage(highlightedMessages[currentHighlightIndex]);
        }
      }
    });

    // Function to search messages
    function searchMessages(searchTerm) {
      console.log('Searching for:', searchTerm);

      // Reset previous highlights
      resetHighlights();

      // If search term is empty or undefined, return
      if (!searchTerm || !searchTerm.trim()) {
        updateSearchCount(0, 0);
        return;
      }

      // Find all message bubbles - try different selectors
      let messageBubbles = messagesContainer.querySelectorAll('.message-bubble');
      if (messageBubbles.length === 0) {
        messageBubbles = messagesContainer.querySelectorAll('[class*="rounded-2xl"]');
      }
      if (messageBubbles.length === 0) {
        messageBubbles = messagesContainer.querySelectorAll('div[class*="bg-"]');
      }

      console.log('Found', messageBubbles.length, 'message bubbles');

      highlightedMessages = [];
      currentHighlightIndex = -1;

      messageBubbles.forEach((bubble, index) => {
        const textContent = bubble.textContent.toLowerCase();
        const searchLower = searchTerm.toLowerCase();

        console.log(`Message ${index}:`, textContent.substring(0, 50) + '...');

        if (textContent.includes(searchLower)) {
          console.log('Match found in message', index);
          highlightedMessages.push(bubble);
          highlightInElement(bubble, searchTerm);
        }
      });

      console.log('Total matches found:', highlightedMessages.length);

      updateSearchCount(highlightedMessages.length, currentHighlightIndex + 1);

      // If we have results, navigate to the first one
      if (highlightedMessages.length > 0) {
        currentHighlightIndex = 0;
        navigateToMessage(highlightedMessages[0]);
      }
    }

    // Function to highlight text in an element
    function highlightInElement(element, searchTerm) {
      const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );

      const textNodes = [];
      let node;

      while (node = walker.nextNode()) {
        textNodes.push(node);
      }

      textNodes.forEach(textNode => {
        const text = textNode.textContent;
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        const highlightedText = text.replace(regex, '<mark class="bg-yellow-300 px-1 rounded">$1</mark>');

        if (highlightedText !== text) {
          const span = document.createElement('span');
          span.innerHTML = highlightedText;
          textNode.parentNode.replaceChild(span, textNode);
          searchMarkers.push(span);
        }
      });
    }

    // Function to reset highlights
    function resetHighlights() {
      searchMarkers.forEach(marker => {
        const parent = marker.parentNode;
        if (parent) {
          parent.innerHTML = parent.textContent;
        }
      });
      searchMarkers = [];
    }

    // Function to navigate to a specific message
    function navigateToMessage(messageElement) {
      if (messageElement) {
        console.log('Navigating to message');
        messageElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // Highlight the current message temporarily
        messageElement.classList.add('ring-2', 'ring-yellow-400');
        setTimeout(() => {
          messageElement.classList.remove('ring-2', 'ring-yellow-400');
        }, 2000);
      }
    }

    // Function to update search count display
    function updateSearchCount(total, current) {
      // This would update a search counter if it exists in the UI
      console.log(`Search results: ${current} of ${total}`);
    }
  }

  // Debounce function for search input
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
});
