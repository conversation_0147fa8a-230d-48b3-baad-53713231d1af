/**
 * Actions Sidebar
 *
 * This file handles the functionality of the actions sidebar, including:
 * - Today/All Actions toggle
 * - Action type filtering
 */

// Namespace for sidebar-specific functionality to avoid conflicts
window.ActionsSidebar = window.ActionsSidebar || {};

document.addEventListener('DOMContentLoaded', function() {
  // Only initialize if we're in the sidebar context (not on actions pages)
  if (document.querySelector('#actions-sidebar')) {
    initActionsSidebarTabs();
    initActionTypeFilters();
    updateCompletedCountBadge();
    initUserFilterFunctionality();

    // Initialize default active filter button state
    setTimeout(() => {
      const sidebar = document.querySelector('#actions-sidebar');
      if (sidebar && window.appleFilterButtons) {
        // Set the action type filter "All" as active
        const allFilterButton = sidebar.querySelector('[data-filter="all"]');
        if (allFilterButton) {
          window.appleFilterButtons.setActiveButton(allFilterButton);
        }

        // Set the date tab "All Actions" as active
        const allActionsTab = sidebar.querySelector('[data-tab="all"]');
        if (allActionsTab) {
          window.appleFilterButtons.setActiveButton(allActionsTab);
        }
      }
    }, 200);

    // Don't update counter on page load - let server-side count take precedence
    // updateActionsCounter();
  }
});

/**
 * Update the completed count badge on the completed tab
 */
function updateCompletedCountBadge() {
  const completedTab = document.getElementById('completed-tab');
  if (!completedTab) return;

  const completedCount = document.querySelectorAll('.action-card.completed').length;

  if (completedCount > 0) {
    let countBadge = completedTab.querySelector('.completed-count');

    if (!countBadge) {
      countBadge = document.createElement('span');
      countBadge.className = 'ml-1.5 inline-flex h-5 w-5 items-center justify-center rounded-full bg-emerald-100 text-xs font-medium text-emerald-800 completed-count';
      completedTab.appendChild(countBadge);
    }

    countBadge.textContent = completedCount;
  }
}

/**
 * Update the actions counter in the navbar based on currently visible actions
 * Only operates on sidebar action cards to avoid conflicts with other pages
 * This should only be called when actively using the sidebar (filtering, tab switching)
 */
function updateActionsCounter() {
  const actionsCounter = document.getElementById('actions_count');
  const sidebar = document.querySelector('#actions-sidebar');

  // Only update if we're in sidebar context and counter exists
  if (!actionsCounter || !sidebar) return;

  // Count visible incomplete action cards ONLY within the sidebar
  const allIncompleteActions = sidebar.querySelectorAll('.action-card:not(.completed)');
  let count = 0;

  allIncompleteActions.forEach(card => {
    const style = card.style.display;
    const isHidden = card.classList.contains('hidden');

    // Count if the card is visible (display: block or no explicit display style and not hidden)
    if ((style === 'block' || (style === '' && !isHidden)) && style !== 'none') {
      count++;
    }
  });

  // Update the counter text
  actionsCounter.textContent = count > 99 ? '99+' : count;

  // Show/hide the counter based on count
  if (count === 0) {
    actionsCounter.style.display = 'none';
  } else {
    actionsCounter.style.display = 'flex';
  }
}

/**
 * Restore the server-side counter when sidebar is closed
 * This fetches the current server-side count to ensure accuracy
 */
function restoreServerSideCounter() {
  // Make an AJAX request to get the current server-side count
  fetch('/admin/actions/counter', {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest',
      'Accept': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    const actionsCounter = document.getElementById('actions_count');
    if (actionsCounter && data.count !== undefined) {
      actionsCounter.textContent = data.count > 99 ? '99+' : data.count;

      // Show/hide the counter based on count
      if (data.count === 0) {
        actionsCounter.style.display = 'none';
      } else {
        actionsCounter.style.display = 'flex';
      }
    }
  })
  .catch(error => {
    console.error('Failed to restore server-side counter:', error);
  });
}

/**
 * Expose functions to ActionsSidebarManager
 */
window.ActionsSidebar.updateActionsCounter = updateActionsCounter;
window.ActionsSidebar.restoreServerSideCounter = restoreServerSideCounter;

/**
 * Initialize the Today/All Actions tabs - SIDEBAR ONLY
 */
function initActionsSidebarTabs() {
  const sidebar = document.querySelector('#actions-sidebar');
  if (!sidebar) return; // Only operate within sidebar

  // Find tabs within sidebar only
  const todayTab = sidebar.querySelector('#today-tab');
  const allTab = sidebar.querySelector('#all-tab');
  const completedTab = sidebar.querySelector('#completed-tab');

  // Also try finding by data attributes within sidebar
  const todayTabByData = sidebar.querySelector('[data-tab="today"]');
  const allTabByData = sidebar.querySelector('[data-tab="all"]');
  const completedTabByData = sidebar.querySelector('[data-tab="completed"]');

  // List all elements with 'tab' class within sidebar
  const allTabElements = sidebar.querySelectorAll('.tab');

  // Use the Apple filter buttons (the ones with .tab class) instead of the old ones
  const actualTodayTab = Array.from(allTabElements).find(el => el.getAttribute('data-tab') === 'today');
  const actualAllTab = Array.from(allTabElements).find(el => el.getAttribute('data-tab') === 'all');
  const actualCompletedTab = Array.from(allTabElements).find(el => el.getAttribute('data-tab') === 'completed');

  if (!actualTodayTab) {
    return;
  }

  if (!actualAllTab) {
    return;
  }

  // Set initial state - All Actions tab is active by default
  filterActionsByDate('all');

  // Set the visual active state for the All Actions tab
  if (actualAllTab && window.appleFilterButtons) {
    setTimeout(() => {
      window.appleFilterButtons.setActiveButton(actualAllTab);
    }, 100);
  }
  
  // Add click event listeners
  actualTodayTab.addEventListener('click', function() {
    const completedActionsContainer = sidebar.querySelector('.completed-actions-container');
    const actionsListContainer = sidebar.querySelector('.actions-list');

    // Show regular actions list and hide completed actions container
    if (actionsListContainer) {
      actionsListContainer.style.display = 'block';
    }

    if (completedActionsContainer) {
      completedActionsContainer.style.display = 'none';
    }

    // Reset action type filter to 'all' and make sure all regular actions are visible
    const allFilterButton = sidebar.querySelector('[data-filter="all"]');
    if (allFilterButton && window.appleFilterButtons) {
      window.appleFilterButtons.setActiveButton(allFilterButton);
    }
    filterActions('all');
    filterActionsByDate('today');
    updateActionsCounter();
  });

  actualAllTab.addEventListener('click', function() {
    const completedActionsContainer = sidebar.querySelector('.completed-actions-container');
    const actionsListContainer = sidebar.querySelector('.actions-list');

    // Show regular actions list and hide completed actions container
    if (actionsListContainer) {
      actionsListContainer.style.display = 'block';
    }

    if (completedActionsContainer) {
      completedActionsContainer.style.display = 'none';
    }

    // Reset action type filter to 'all' and make sure all regular actions are visible
    const allFilterButton = sidebar.querySelector('[data-filter="all"]');
    if (allFilterButton && window.appleFilterButtons) {
      window.appleFilterButtons.setActiveButton(allFilterButton);
    }

    filterActions('all');
    filterActionsByDate('all');
    updateActionsCounter();
  });

  // Use the completed tab element we found earlier

  if (actualCompletedTab) {
    actualCompletedTab.addEventListener('click', function() {
      const completedActionsContainer = sidebar.querySelector('.completed-actions-container');
      const actionsListContainer = sidebar.querySelector('.actions-list');

      // Show completed actions container and hide regular actions list
      if (completedActionsContainer) {
        completedActionsContainer.style.display = 'block';

        const completedActions = completedActionsContainer.querySelectorAll('.action-card.completed');

        // Make all completed actions visible by removing hidden class and display none
        completedActions.forEach(action => {
          action.classList.remove('hidden');
          action.style.display = 'block';
        });
      }

      if (actionsListContainer) {
        actionsListContainer.style.display = 'none';
      }

      // Update the filter pills to show 'All' as active within sidebar
      sidebar.querySelectorAll('.filter-pill').forEach(pill => {
        pill.classList.remove('active');
        pill.classList.remove('bg-white');
      });

      const allFilterPill = sidebar.querySelector('.filter-pill[data-filter="all"]');
      if (allFilterPill) {
        allFilterPill.classList.add('active');
        animateFilterPill(allFilterPill);
      }
    });
  }
}

/**
 * Set the active tab
 * @param {HTMLElement} activeTab - The tab to set as active
 */
function setActiveTab(activeTab) {
  // Remove active class from all tabs (including the completed tab)
  document.querySelectorAll('.tab').forEach(tab => {
    tab.classList.remove('active');
  });
  
  // Handle the tabs in the tab-container
  document.querySelectorAll('.tab-container .tab').forEach(tab => {
    tab.classList.remove('bg-white');
    tab.classList.remove('shadow-sm');
    tab.classList.remove('border');
    tab.classList.remove('border-gray-200');
    tab.classList.remove('text-gray-700');
    tab.classList.remove('font-medium');
    
    tab.classList.add('text-gray-500');
  });
  
  // Add active class to the clicked tab
  activeTab.classList.add('active');
  
  // If it's a tab in the tab-container, add the specific styles
  if (activeTab.closest('.tab-container')) {
    activeTab.classList.add('bg-white');
    activeTab.classList.add('shadow-sm');
    activeTab.classList.add('border');
    activeTab.classList.add('border-gray-200');
    activeTab.classList.add('text-gray-700');
    activeTab.classList.add('font-medium');
    
    activeTab.classList.remove('text-gray-500');
  }
}

/**
 * Filter actions by date - SIDEBAR ONLY
 * @param {string} filter - The filter to apply ('today' or 'all')
 */
function filterActionsByDate(filter) {
  const sidebar = document.querySelector('#actions-sidebar');
  if (!sidebar) return; // Only operate within sidebar

  const actionCards = sidebar.querySelectorAll('.action-card');
  const completedTab = sidebar.querySelector('#completed-tab');
  const isCompletedTabActive = completedTab && completedTab.classList.contains('active');

  // If completed tab is active, we handle it separately
  if (isCompletedTabActive) return;

  actionCards.forEach(card => {
    // Don't show completed actions in the regular views
    if (card.classList.contains('completed')) {
      card.style.display = 'none';
      return;
    }

    if (filter === 'today') {
      // Show only today's actions
      if (card.classList.contains('today-action')) {
        card.style.display = 'block';
      } else {
        card.style.display = 'none';
      }
    } else {
      // Show all actions
      card.style.display = 'block';
    }
  });

  // Check if there are any visible actions within sidebar
  const visibleActions = sidebar.querySelectorAll('.action-card[style="display: block;"]');
  const emptyState = sidebar.querySelector('.empty-state');

  if (visibleActions.length === 0 && emptyState) {
    emptyState.style.display = 'flex';
  } else if (emptyState) {
    emptyState.style.display = 'none';
  }

  // Update the actions counter after filtering
  updateActionsCounter();
}



/**
 * Initialize the action type filters - SIDEBAR ONLY
 */
function initActionTypeFilters() {
  const sidebar = document.querySelector('#actions-sidebar');
  if (!sidebar) return; // Only operate within sidebar

  const filterButtons = sidebar.querySelectorAll('.action-type-filter');

  if (!filterButtons.length) {
    return;
  }

  // Set initial state - 'All' filter is active by default
  filterActions('all');

  // Add click event listeners
  filterButtons.forEach(button => {
    button.addEventListener('click', function() {
      // Filter actions
      const filterType = button.getAttribute('data-filter');
      filterActions(filterType);
    });
  });

  // Initialize the first pill animation within sidebar
  const activeButton = sidebar.querySelector('.actions-filter-tabs button.active');
  if (activeButton) {
    setTimeout(() => animateFilterPill(activeButton), 100);
  }
}

/**
 * Animate the active filter pill
 * @param {HTMLElement} activePill - The active pill element
 */
function animateFilterPill(activePill) {
  // Add transition class for smooth animation
  activePill.classList.add('bg-white');
}

/**
 * Filter actions by type - SIDEBAR ONLY
 * @param {string} filterType - The type to filter by
 */
function filterActions(filterType) {
  const sidebar = document.querySelector('#actions-sidebar');
  if (!sidebar) return; // Only operate within sidebar

  // Check if we're currently showing the completed actions container within sidebar
  const completedActionsContainer = sidebar.querySelector('.completed-actions-container');
  const isCompletedContainerVisible = completedActionsContainer && completedActionsContainer.style.display !== 'none';

  if (isCompletedContainerVisible) {
    // Filter completed actions within sidebar
    const completedActions = sidebar.querySelectorAll('.action-card.completed');

    completedActions.forEach(action => {
      const actionType = action.getAttribute('data-action-type');

      if (filterType === 'all' || actionType === filterType) {
        action.classList.remove('hidden');
        action.style.display = 'block';
      } else {
        action.classList.add('hidden');
        action.style.display = 'none';
      }
    });
  } else {
    // Filter regular (non-completed) actions within sidebar
    const regularActions = sidebar.querySelectorAll('.action-card:not(.completed)');

    regularActions.forEach(action => {
      const actionType = action.getAttribute('data-action-type');

      if (filterType === 'all' || actionType === filterType) {
        action.classList.remove('hidden');
        action.style.display = 'block';
      } else {
        action.classList.add('hidden');
        action.style.display = 'none';
      }
    });
  }

  // Check if there are any visible actions within sidebar
  const visibleActions = sidebar.querySelectorAll('.action-card[style="display: block;"]');
  const emptyState = sidebar.querySelector('.empty-state');

  if (visibleActions.length === 0 && emptyState) {
    emptyState.style.display = 'flex';
  } else if (emptyState) {
    emptyState.style.display = 'none';
  }

  // Don't update the global counter when filtering - it should show total assigned actions
  // updateActionsCounter();
}

/**
 * Initialize user filter functionality
 */
function initUserFilterFunctionality() {
  // Toggle dropdown visibility when button is clicked
  $(document).on('click', '#user-filter-toggle', function(e) {
    e.preventDefault();
    $('#user-filter-menu').toggleClass('hidden');

    // Update aria-expanded attribute
    const isExpanded = $(this).attr('aria-expanded') === 'true';
    $(this).attr('aria-expanded', !isExpanded);
    $(this).attr('data-state', isExpanded ? 'closed' : 'open');

    // Load users if dropdown is opened and not already loaded
    if (!isExpanded && $('#user-list .user-option').length <= 1) {
      loadUsers();
    }
  });

  // Close dropdown when clicking outside
  $(document).on('click', function(e) {
    if (!$(e.target).closest('#user-filter-toggle, #user-filter-menu').length) {
      $('#user-filter-menu').addClass('hidden');
      $('#user-filter-toggle').attr('aria-expanded', 'false');
      $('#user-filter-toggle').attr('data-state', 'closed');
    }
  });

  // Prevent dropdown from closing when clicking inside it
  $(document).on('click', '#user-filter-menu', function(e) {
    e.stopPropagation();
  });

  // Handle user search
  $(document).on('input', '#user-search', function() {
    const searchTerm = $(this).val().toLowerCase();
    $('.user-option').each(function() {
      const userName = $(this).find('span.flex-1').text().toLowerCase();
      if (userName.includes(searchTerm)) {
        $(this).show();
      } else {
        $(this).hide();
      }
    });
  });

  // Handle user selection - using event delegation for dynamically loaded content
  $(document).on('click', '.user-option', function(e) {
    e.preventDefault();
    const userId = $(this).data('user-id');
    const userName = $(this).find('span.flex-1').text();

    // Clone the avatar element from the clicked option
    const avatarClone = $(this).find('.relative.flex.shrink-0').clone();

    // Update the selected user in the button
    $('#selected-user-name').text(userName);
    $('#selected-user-avatar-container').html(avatarClone.html());

    // Update active state - remove active class and ALL checkmarks from all options
    $('.user-option').removeClass('active-user');

    // Remove all checkmarks - target both class-based and all SVG elements in user options
    $('.user-option .checkmark-icon').remove();
    $('.user-option svg').remove();

    // Add active class to the clicked option
    $(this).addClass('active-user');

    // Always add a fresh checkmark to the selected option
    $(this).append(`
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 checkmark-icon" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
      </svg>
    `);

    // Close the dropdown
    $('#user-filter-menu').addClass('hidden');
    $('#user-filter-toggle').attr('aria-expanded', 'false');
    $('#user-filter-toggle').attr('data-state', 'closed');

    // Load actions for the selected user
    loadActionsForUser(userId);
  });
}

/**
 * Load users via AJAX
 */
function loadUsers() {
  $.ajax({
    url: '/admin/general_settings/users',
    method: 'GET',
    dataType: 'json',
    success: function(data) {
      $('#user-loading').hide();

      if (data && data.length > 0) {
        // Get current user ID from the existing user option
        const currentUserId = $('.user-option.active-user').data('user-id');

        // Add each user to the dropdown
        data.forEach(function(user) {
          // Skip if it's the current user (already added)
          if (user.id == currentUserId) return;

          // Create a container for the user option
          const userOption = document.createElement('a');
          userOption.className = 'user-option text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100';
          userOption.setAttribute('role', 'menuitem');
          userOption.setAttribute('data-user-id', user.id);
          userOption.setAttribute('href', '#');

          // Create avatar container
          const avatarContainer = document.createElement('span');
          avatarContainer.className = 'relative flex shrink-0 overflow-hidden h-7 w-7 rounded-full mr-3';

          // Create avatar image
          const avatarImg = document.createElement('img');
          avatarImg.className = 'aspect-square h-full w-full';
          avatarImg.style.objectFit = 'cover';

          // Get the default avatar URL from the data attribute
          const defaultAvatarUrl = document.getElementById('actions-sidebar')?.getAttribute('data-default-avatar-url') || '/assets/default-avatar.webp';

          avatarImg.src = user.image_url || defaultAvatarUrl;
          avatarImg.alt = user.first_name + ' ' + user.last_name;

          // Add error handling for broken images
          avatarImg.onerror = function() {
            this.src = defaultAvatarUrl;
          };

          avatarContainer.appendChild(avatarImg);

          // Create name span
          const nameSpan = document.createElement('span');
          nameSpan.className = 'flex-1';
          nameSpan.textContent = user.first_name + ' ' + user.last_name;

          // Append elements to user option
          userOption.appendChild(avatarContainer);
          userOption.appendChild(nameSpan);

          // Add to the user list
          document.getElementById('user-list').appendChild(userOption);
        });
      } else {
        $('#user-list').append('<div class="px-4 py-2 text-sm text-gray-500 text-center">No other users found</div>');
      }
    },
    error: function() {
      $('#user-loading').text('Failed to load users');
    }
  });
}

/**
 * Load actions for a specific user
 * @param {number} userId - The ID of the user to load actions for
 */
function loadActionsForUser(userId) {
  // Store current filter states
  const currentDateFilter = getCurrentDateFilter();
  const currentActionTypeFilter = getCurrentActionTypeFilter();

  // Hide all current actions
  $('.action-card').hide();

  // Make AJAX request to get actions for the selected user
  $.ajax({
    url: '/admin/actions',
    method: 'GET',
    data: { user_id: userId },
    success: function(data) {
      // Clear all existing action cards from both containers
      $('.action-card').remove();

      const actionsContainer = $('.actions-list').first();

      if (actionsContainer.length === 0) {
        console.error('No .actions-list container found!');
      } else {
        actionsContainer.html(data);
      }

      // Now load completed actions for this user
      loadCompletedActions(userId);

      // Reapply the current filters using jQuery
      reapplyFilters(currentDateFilter, currentActionTypeFilter);

      // Update completed count badge
      updateCompletedCountBadge();
    },
    error: function(xhr, status, error) {
      console.error('AJAX error:', status, error);
      $('.actions-list').html('<div class="text-center py-8"><p class="text-red-500">Failed to load actions</p></div>');
    }
  });
}

/**
 * Load completed actions for a specific user
 * @param {number} userId - The user ID to load completed actions for
 */
function loadCompletedActions(userId) {
  $.ajax({
    url: '/admin/actions',
    method: 'GET',
    data: { user_id: userId, completed_only: true },
    success: function(data) {
      const completedContainer = $('.completed-actions-container').first();
      if (completedContainer.length > 0) {
        completedContainer.html(data);
      } else {
        console.error('No .completed-actions-container found!');
      }

      // Reapply filters to completed actions
      reapplyFilters(getCurrentDateFilter(), getCurrentActionTypeFilter());
    },
    error: function(xhr, status, error) {
      console.error('Completed actions AJAX error:', status, error);
      $('.completed-actions-container').html('<div class="px-4 py-8 text-center text-red-500">Failed to load completed actions</div>');
    }
  });
}

/**
 * Reapply filters after loading new content
 * @param {string} dateFilter - The date filter to apply
 * @param {string} actionTypeFilter - The action type filter to apply
 */
function reapplyFilters(dateFilter, actionTypeFilter) {
  // Apply date filter
  if (dateFilter === 'today') {
    $('.action-card').hide();
    $('.action-card.today-action:not(.completed)').show();
    $('.completed-actions-container').hide();
    $('.actions-list').show();
  } else if (dateFilter === 'completed') {
    $('.action-card:not(.completed)').hide();
    $('.action-card.completed').show();
    $('.completed-actions-container').show();
    $('.actions-list').hide();
  } else {
    // 'all' - show all non-completed actions
    $('.action-card.completed').hide();
    $('.action-card:not(.completed)').show();
    $('.completed-actions-container').hide();
    $('.actions-list').show();
  }

  // Apply action type filter
  if (actionTypeFilter !== 'all') {
    $(`.action-card:not([data-action-type="${actionTypeFilter}"])`).hide();
  }
}

/**
 * Get the current date filter state
 * @returns {string} The current date filter ('today', 'all', or 'completed')
 */
function getCurrentDateFilter() {
  // Check for active tab with different possible selectors
  let activeTab = document.querySelector('.tab.active');
  if (!activeTab) {
    // Try alternative selectors
    activeTab = document.querySelector('.tab[aria-pressed="true"]');
  }
  if (!activeTab) {
    // Check which container is visible to determine the filter
    const completedContainer = document.querySelector('.completed-actions-container');
    if (completedContainer && completedContainer.style.display !== 'none') {
      return 'completed';
    }
    // Default logic - check if we're showing today's actions
    const todayActions = Array.from(document.querySelectorAll('.action-card.today-action')).filter(el =>
      el.style.display !== 'none' && el.offsetParent !== null
    );
    const allActions = Array.from(document.querySelectorAll('.action-card:not(.completed)')).filter(el =>
      el.style.display !== 'none' && el.offsetParent !== null
    );
    if (todayActions.length > 0 && todayActions.length === allActions.length) {
      return 'today';
    }
  }

  if (activeTab) {
    const filter = activeTab.getAttribute('data-tab');
    return filter || 'all';
  }

  return 'all';
}

/**
 * Get the current action type filter state
 * @returns {string} The current action type filter
 */
function getCurrentActionTypeFilter() {
  // Check for active action type filter - the correct class is 'active-filter'
  let activeFilter = document.querySelector('.action-type-filter.active-filter');
  if (!activeFilter) {
    activeFilter = document.querySelector('.action-type-filter.active');
  }
  if (!activeFilter) {
    activeFilter = document.querySelector('.action-type-filter[aria-pressed="true"]');
  }

  if (activeFilter) {
    const filter = activeFilter.getAttribute('data-filter');
    return filter || 'all';
  }

  return 'all';
}

/**
 * Re-initialize event handlers for action cards
 */
function initializeActionHandlers() {
  // Add any event handlers needed for the action cards
  // This will be called after loading new actions
}