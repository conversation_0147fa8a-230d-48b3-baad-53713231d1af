/**
 * Unified Action Modal Manager
 * 
 * This module provides a unified interface for managing action modals across different contexts:
 * - Sidebar context (sidebarNewActionModal)
 * - Patient actions screen context (patientNewActionModal)
 * 
 * Both contexts use the same modal template but have different JavaScript initialization needs.
 */

class UnifiedActionModal {
  constructor() {
    this.activeModal = null;
    this.modalConfigs = new Map();
    this.initializeGlobalEventListeners();
  }

  /**
   * Register a modal with its configuration
   * @param {string} modalId - The ID of the modal
   * @param {Object} config - Configuration options for the modal
   */
  registerModal(modalId, config = {}) {
    const defaultConfig = {
      context: 'general', // 'sidebar', 'patient-actions'
      enableAI: true,
      enableTeamSelector: true,
      defaultActionType: 'task',
      openTriggerSelector: null, // CSS selector for elements that open this modal
      closeTriggerSelectors: ['.modal-close', '.modal-cancel'] // CSS selectors for elements that close this modal
    };

    this.modalConfigs.set(modalId, { ...defaultConfig, ...config });
    
    // Set up event listeners for this modal
    this.setupModalEventListeners(modalId);
  }

  /**
   * Set up event listeners for a specific modal
   * @param {string} modalId - The ID of the modal
   */
  setupModalEventListeners(modalId) {
    const config = this.modalConfigs.get(modalId);
    if (!config) return;

    // Set up open trigger
    if (config.openTriggerSelector) {
      document.addEventListener('click', (e) => {
        if (e.target.matches(config.openTriggerSelector) || e.target.closest(config.openTriggerSelector)) {
          e.preventDefault();
          this.openModal(modalId);
        }
      });
    }

    // Set up form submission handler for combining callback date/time
    const modal = document.getElementById(modalId);
    if (modal) {
      const form = modal.querySelector(`#${modalId}-form`);
      if (form) {
        form.addEventListener('submit', (e) => {
          this.handleFormSubmission(modal, e);
        });
      }
    }

    // Set up close triggers
    config.closeTriggerSelectors.forEach(selector => {
      document.addEventListener('click', (e) => {
        const modal = document.getElementById(modalId);
        if (modal && (e.target.matches(`#${modalId} ${selector}`) || e.target.closest(`#${modalId} ${selector}`))) {
          e.preventDefault();
          this.closeModal(modalId);
        }
      });
    });

    // Set up overlay click to close
    document.addEventListener('click', (e) => {
      if (e.target.id === `${modalId}-overlay`) {
        this.closeModal(modalId);
      }
    });
  }

  /**
   * Initialize global event listeners
   */
  initializeGlobalEventListeners() {
    // ESC key to close active modal
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.activeModal) {
        this.closeModal(this.activeModal);
      }
    });
  }

  /**
   * Open a modal by ID
   * @param {string} modalId - The ID of the modal to open
   * @param {Object} options - Additional options for opening
   */
  openModal(modalId, options = {}) {
    const modal = document.getElementById(modalId);
    const overlay = document.getElementById(`${modalId}-overlay`);

    if (!modal || !overlay) {
      console.error(`Modal ${modalId} or its overlay not found`);
      return;
    }

    // Close any currently active modal
    if (this.activeModal && this.activeModal !== modalId) {
      this.closeModal(this.activeModal);
    }

    // Show modal and overlay
    modal.classList.remove('hidden');
    overlay.classList.remove('hidden');
    modal.setAttribute('data-state', 'open');
    
    // Set active modal
    this.activeModal = modalId;

    // Handle edit mode if action data is provided
    if (options.editAction) {
      this.populateModalForEdit(modalId, options.editAction);
    } else {
      this.resetModalForCreate(modalId);
    }

    // Trigger modalOpen event for other scripts
    const modalOpenEvent = new CustomEvent('modalOpen');
    modal.dispatchEvent(modalOpenEvent);

    // jQuery event for compatibility with existing code
    if (typeof $ !== 'undefined') {
      $(modal).trigger('modalOpen');
    }

    // Initialize modal functionality
    setTimeout(() => {
      this.initializeModalFunctionality(modalId);
    }, 100);

    // Set focus for accessibility
    modal.focus();
  }

  /**
   * Close a modal by ID
   * @param {string} modalId - The ID of the modal to close
   */
  closeModal(modalId) {
    const modal = document.getElementById(modalId);
    const overlay = document.getElementById(`${modalId}-overlay`);

    if (modal && overlay) {
      modal.classList.add('hidden');
      overlay.classList.add('hidden');
      modal.setAttribute('data-state', 'closed');
    }

    // Clear active modal if this was the active one
    if (this.activeModal === modalId) {
      this.activeModal = null;
    }

    // Reset form if present
    const form = modal?.querySelector('form');
    if (form) {
      form.reset();
      
      // Reset action type to default
      const actionTypeField = form.querySelector('input[name="action_data[action_type]"]');
      if (actionTypeField) {
        actionTypeField.value = 'task';
      }
    }
  }

  /**
   * Populate modal for editing an existing action
   * @param {string} modalId - The ID of the modal
   * @param {Object} actionData - The action data to populate
   */
  populateModalForEdit(modalId, actionData) {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    // Update modal title
    const titleElement = modal.querySelector(`#${modalId}-title`);
    if (titleElement) {
      titleElement.textContent = 'Edit action';
    }

    // Update submit button
    const submitButton = modal.querySelector(`#${modalId}-submit-button`);
    if (submitButton) {
      const updateText = submitButton.getAttribute('data-update-text') || 'Update action';
      submitButton.textContent = updateText;
    }

    // Update form action for PATCH request
    const form = modal.querySelector(`#${modalId}-form`);
    if (form) {
      form.action = `/admin/actions/${actionData.id}`;
      form.method = 'POST';

      // Add method override for PATCH
      let methodInput = form.querySelector('input[name="_method"]');
      if (!methodInput) {
        methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        form.appendChild(methodInput);
      }
      methodInput.value = 'PATCH';
    }

    // Populate form fields
    const actionIdField = modal.querySelector(`#${modalId}-action-id`);
    if (actionIdField) {
      actionIdField.value = actionData.id;
    }

    const descriptionField = modal.querySelector('#action_data_description');
    if (descriptionField) {
      descriptionField.value = actionData.description || '';
    }

    const dueDateField = modal.querySelector('#action_data_date_due');
    const actionDateField = modal.querySelector('#action_data_action_date');
    const actionTimeField = modal.querySelector('#action_data_action_time');

    if (actionData.date_due) {
      try {
        const date = new Date(actionData.date_due);
        if (!isNaN(date.getTime())) {
          // Adjust for timezone offset to get local time
          const localDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));

          // Split the datetime into separate date and time fields for all action types
          if (actionDateField && actionTimeField) {
            // Format date as dd/mm/yyyy
            const day = String(localDate.getDate()).padStart(2, '0');
            const month = String(localDate.getMonth() + 1).padStart(2, '0');
            const year = localDate.getFullYear();
            const formattedDate = `${day}/${month}/${year}`;

            // Format time as HH:MM
            const hours = String(localDate.getHours()).padStart(2, '0');
            const minutes = String(localDate.getMinutes()).padStart(2, '0');
            const formattedTime = `${hours}:${minutes}`;

            actionDateField.value = formattedDate;
            actionTimeField.value = formattedTime;

            console.log('Set action date/time fields:', formattedDate, formattedTime);
          }

          // Keep the hidden due date field for form submission
          if (dueDateField) {
            dueDateField.value = actionData.date_due;
          }
        } else {
          console.warn('Invalid date:', actionData.date_due);
          if (actionDateField) actionDateField.value = '';
          if (actionTimeField) actionTimeField.value = '';
          if (dueDateField) dueDateField.value = '';
        }
      } catch (error) {
        console.error('Error parsing date:', error);
        if (actionDateField) actionDateField.value = '';
        if (actionTimeField) actionTimeField.value = '';
        if (dueDateField) dueDateField.value = '';
      }
    } else {
      if (actionDateField) actionDateField.value = '';
      if (actionTimeField) actionTimeField.value = '';
      if (dueDateField) dueDateField.value = '';
    }

    // Set action type in hidden field
    const actionTypeField = modal.querySelector('input[name="action_data[action_type]"]');
    if (actionTypeField) {
      actionTypeField.value = actionData.action_type || 'task';
    }

    // Set priority in hidden field
    const priorityField = modal.querySelector('#action_data_priority');
    if (priorityField) {
      priorityField.value = actionData.priority || 'low';
    }

    // Store the priority to set after modal initialization
    const priority = actionData.priority || 'low';
    modal.setAttribute('data-edit-priority', priority);
    console.log('Stored priority for edit mode:', priority);

    // Store the action type to set after modal initialization
    const actionType = actionData.action_type || 'task';
    modal.setAttribute('data-edit-action-type', actionType);
    console.log('Stored action type for edit mode:', actionType);

    // Set assigned users for team selector
    if (actionData.assigned_to_json && Array.isArray(actionData.assigned_to_json)) {
      // Check the appropriate checkboxes
      actionData.assigned_to_json.forEach(userId => {
        const checkbox = modal.querySelector(`input[name="action_data[assigned_to_json][]"][value="${userId}"]`);
        if (checkbox) {
          checkbox.checked = true;
          const userItem = checkbox.closest('[data-user-id]');
          if (userItem) {
            userItem.dataset.selected = 'true';
          }
        }
      });
    }
  }

  /**
   * Reset modal for creating a new action
   * @param {string} modalId - The ID of the modal
   */
  resetModalForCreate(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    // Update modal title
    const titleElement = modal.querySelector(`#${modalId}-title`);
    if (titleElement) {
      titleElement.textContent = 'New action';
    }

    // Update submit button
    const submitButton = modal.querySelector(`#${modalId}-submit-button`);
    if (submitButton) {
      const createText = submitButton.getAttribute('data-create-text') || 'Create task';
      submitButton.textContent = createText;
    }

    // Reset form action for POST request
    const form = modal.querySelector(`#${modalId}-form`);
    if (form) {
      form.action = '/admin/actions';
      form.method = 'POST';

      // Remove method override
      const methodInput = form.querySelector('input[name="_method"]');
      if (methodInput) {
        methodInput.remove();
      }
    }

    // Clear form fields
    const actionIdField = modal.querySelector(`#${modalId}-action-id`);
    if (actionIdField) {
      actionIdField.value = '';
    }

    const descriptionField = modal.querySelector('#action_data_description');
    if (descriptionField) {
      descriptionField.value = '';
    }

    const dueDateField = modal.querySelector('#action_data_date_due');
    if (dueDateField) {
      dueDateField.value = '';
    }

    // Clear callback date and time fields
    const callbackDateField = modal.querySelector('#action_data_callback_date');
    const callbackTimeField = modal.querySelector('#action_data_callback_time');
    if (callbackDateField) callbackDateField.value = '';
    if (callbackTimeField) callbackTimeField.value = '';

    // Reset to default action type (task)
    const actionTypeField = modal.querySelector('input[name="action_data[action_type]"]');
    if (actionTypeField) {
      actionTypeField.value = 'task';
    }

    // Reset to default priority
    const priorityField = modal.querySelector('#action_data_priority');
    if (priorityField) {
      priorityField.value = 'low';
    }

    // Reset to default assignment type
    const assignmentTypeField = modal.querySelector('#action_data_assignment_type');
    if (assignmentTypeField) {
      assignmentTypeField.value = 'date_time';
    }

    // Reset appointment assignment fields
    const treatmentPlanField = modal.querySelector('#action_data_treatment_plan_id');
    const appointmentField = modal.querySelector('#action_data_appointment_id');
    if (treatmentPlanField) {
      treatmentPlanField.value = '';
    }
    if (appointmentField) {
      appointmentField.value = '';
      appointmentField.disabled = true;
    }

    // Clear any stored edit action type and priority
    modal.removeAttribute('data-edit-action-type');
    modal.removeAttribute('data-edit-priority');

    // Clear assigned users in team selector
    const checkboxes = modal.querySelectorAll('input[name="action_data[assigned_to_json][]"]');
    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
      const userItem = checkbox.closest('[data-user-id]');
      if (userItem) {
        userItem.dataset.selected = 'false';
        userItem.style.display = 'flex';
      }
    });

    // Clear selected team section
    const currentModalId = modal.id;
    const selectedSection = modal.querySelector(`#${currentModalId}-selected-team-section`);
    const selectedContainer = modal.querySelector(`#${currentModalId}-selected-team-container`);
    if (selectedSection && selectedContainer) {
      selectedContainer.innerHTML = '';
      selectedSection.classList.add('hidden');
    }
  }

  /**
   * Initialize all functionality for a modal
   * @param {string} modalId - The ID of the modal to initialize
   */
  initializeModalFunctionality(modalId) {
    const modal = document.getElementById(modalId);
    const config = this.modalConfigs.get(modalId) || {};

    if (!modal) return;

    // Initialize action tabs
    this.initializeActionTabs(modal, config);

    // Initialize priority toggles
    this.initializePriorityToggles(modal, config);

    // Initialize assignment type toggles
    this.initializeAssignmentTypeToggles(modal, config);

    // Initialize appointment selectors
    this.initializeAppointmentSelectors(modal, config);

    // Initialize team selector if enabled
    if (config.enableTeamSelector) {
      this.initializeTeamSelectorForModal(modal, config);
    }

    // Initialize AI functionality if enabled
    if (config.enableAI) {
      this.initializeAIInputForModal(modal, config);
    }

    // Initialize practice/patient toggle (only if present in DOM)
    this.initializePracticePatientToggle(modal, config);

    // Initialize datetime picker functionality
    this.initializeDateTimePicker(modal, config);

    // Set action type and priority - check if we're in edit mode first
    const editActionType = modal.getAttribute('data-edit-action-type');
    const editPriority = modal.getAttribute('data-edit-priority');

    if (editActionType) {
      // We're in edit mode, select the correct tab and priority
      console.log('Edit mode detected, setting action type to:', editActionType);
      console.log('Edit mode detected, setting priority to:', editPriority);

      setTimeout(() => {
        const editTab = modal.querySelector(`.actiontab[data-action-type="${editActionType}"]`);
        if (editTab) {
          console.log('Found edit tab, clicking it');
          editTab.click();
        } else {
          console.warn('Edit tab not found for action type:', editActionType);
        }

        // Set priority toggle if specified
        if (editPriority) {
          const editPriorityToggle = modal.querySelector(`.priority-toggle[data-priority-level="${editPriority}"]`);
          if (editPriorityToggle) {
            console.log('Found edit priority toggle, clicking it');
            editPriorityToggle.click();
          } else {
            console.warn('Edit priority toggle not found for priority:', editPriority);
          }
        }

        // Clean up the attributes
        modal.removeAttribute('data-edit-action-type');
        modal.removeAttribute('data-edit-priority');
      }, 150); // Increased timeout to ensure tabs are ready
    } else {
      // We're in create mode, set default action type and priority
      console.log('Create mode detected, setting default action type:', config.defaultActionType);
      const defaultTab = modal.querySelector(`.actiontab[data-action-type="${config.defaultActionType}"]`);
      if (defaultTab) {
        setTimeout(() => {
          defaultTab.click();
        }, 50);
      }

      // Set default priority (low)
      const defaultPriorityToggle = modal.querySelector('.priority-toggle[data-priority-level="low"]');
      if (defaultPriorityToggle) {
        setTimeout(() => {
          defaultPriorityToggle.click();
        }, 100);
      }

      // Set default assignment type (date_time)
      const defaultAssignmentToggle = modal.querySelector('.assignment-toggle[data-assignment-type="date_time"]');
      if (defaultAssignmentToggle) {
        setTimeout(() => {
          defaultAssignmentToggle.click();
        }, 120);
      }
    }
  }

  /**
   * Initialize action tabs functionality
   * @param {Element} modal - The modal element
   * @param {Object} config - Modal configuration
   */
  initializeActionTabs(modal, config) {
    const actionTabs = modal.querySelectorAll('.actiontab');
    const submitBtn = modal.querySelector('[id$="-submit-button"]');
    const actionTypeField = modal.querySelector('input[name="action_data[action_type]"]');

    const buttonConfigs = {
      'task': {
        text: 'Create Task',
        bgGradient: 'linear-gradient(to right, #fef3c7, #fcd34d)',
        textColor: 'text-amber-800',
        iconColor: 'text-amber-700'
      },
      'comment': {
        text: 'Create Comment',
        bgGradient: 'linear-gradient(to right, #dbeafe, #93c5fd)',
        textColor: 'text-blue-800',
        iconColor: 'text-blue-700'
      },
      'reminder': {
        text: 'Create Reminder',
        bgGradient: 'linear-gradient(to right, #f3e8ff, #d8b4fe)',
        textColor: 'text-purple-800',
        iconColor: 'text-purple-700'
      },
      'alert': {
        text: 'Create Alert',
        bgGradient: 'linear-gradient(to right, #fee2e2, #fca5a5)',
        textColor: 'text-red-800',
        iconColor: 'text-red-700'
      },
      'callback': {
        text: 'Create Callback',
        bgGradient: 'linear-gradient(to right, #dcfce7, #86efac)',
        textColor: 'text-green-800',
        iconColor: 'text-green-700'
      },
      'complaint': {
        text: 'Create Complaint',
        bgGradient: 'linear-gradient(to right, #ffedd5, #fdba74)',
        textColor: 'text-orange-800',
        iconColor: 'text-orange-700'
      }
    };

    // Remove any existing click handlers and add new ones
    actionTabs.forEach(tab => {
      // Clone node to remove all event listeners
      const newTab = tab.cloneNode(true);
      tab.parentNode.replaceChild(newTab, tab);
    });

    // Re-query after cloning
    const newActionTabs = modal.querySelectorAll('.actiontab');
    
    newActionTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const actionType = tab.dataset.actionType;
        const buttonConfig = buttonConfigs[actionType] || buttonConfigs['task'];

        // Update action type field
        if (actionTypeField) {
          actionTypeField.value = actionType;
        }

        // Update submit button with gradient background
        if (submitBtn) {
          // Check if we're in edit mode by looking for the action ID
          const actionIdField = modal.querySelector(`input[name="action_data[id]"]`);
          const isEditMode = actionIdField && actionIdField.value;

          if (isEditMode) {
            // In edit mode, use update text
            const updateText = submitBtn.getAttribute('data-update-text') || 'Update action';
            submitBtn.textContent = updateText;
          } else {
            // In create mode, use the action-specific text
            submitBtn.textContent = buttonConfig.text;
          }

          submitBtn.className = 'px-5 py-2.5 rounded-lg font-medium shadow-sm hover:shadow-md transition-all duration-300 modal-button-font ' + buttonConfig.textColor;
          submitBtn.style.background = buttonConfig.bgGradient;
          submitBtn.style.border = 'none';
        }

        // Update tab visual states
        this.updateTabVisualStates(modal, actionType, buttonConfigs);

        // Update date input type and label based on action type
        this.updateDateInputForActionType(modal, actionType);
      });
    });
  }

  /**
   * Initialize priority toggles functionality
   * @param {Element} modal - The modal element
   * @param {Object} config - Modal configuration
   */
  initializePriorityToggles(modal, config) {
    const priorityToggles = modal.querySelectorAll('.priority-toggle');
    const priorityField = modal.querySelector('input[name="action_data[priority]"]');

    if (!priorityToggles.length || !priorityField) {
      console.log('Priority toggles or field not found');
      return;
    }

    const priorityConfigs = {
      'low': {
        bgGradient: 'linear-gradient(to right, #f0f9ff, #bae6fd)',
        textColor: 'text-sky-800',
        iconColor: 'text-sky-700'
      },
      'medium': {
        bgGradient: 'linear-gradient(to right, #fef3c7, #fcd34d)',
        textColor: 'text-amber-800',
        iconColor: 'text-amber-700'
      },
      'high': {
        bgGradient: 'linear-gradient(to right, #ffedd5, #fdba74)',
        textColor: 'text-orange-800',
        iconColor: 'text-orange-700'
      },
      'urgent': {
        bgGradient: 'linear-gradient(to right, #fee2e2, #fca5a5)',
        textColor: 'text-red-800',
        iconColor: 'text-red-700'
      }
    };

    // Remove any existing click handlers and add new ones
    priorityToggles.forEach(toggle => {
      // Clone node to remove all event listeners
      const newToggle = toggle.cloneNode(true);
      toggle.parentNode.replaceChild(newToggle, toggle);
    });

    // Re-query after cloning
    const newPriorityToggles = modal.querySelectorAll('.priority-toggle');

    newPriorityToggles.forEach(toggle => {
      toggle.addEventListener('click', () => {
        const priorityLevel = toggle.dataset.priorityLevel;
        const priorityConfig = priorityConfigs[priorityLevel] || priorityConfigs['low'];

        // Update priority field
        if (priorityField) {
          priorityField.value = priorityLevel;
        }

        // Update toggle visual states
        this.updatePriorityToggleVisualStates(modal, priorityLevel, priorityConfigs);
      });
    });
  }

  /**
   * Initialize assignment type toggles functionality
   * @param {Element} modal - The modal element
   * @param {Object} config - Modal configuration
   */
  initializeAssignmentTypeToggles(modal, config) {
    const assignmentToggles = modal.querySelectorAll('.assignment-toggle');
    const assignmentTypeField = modal.querySelector('input[name="action_data[assignment_type]"]');
    const assignmentContainer = modal.querySelector('#assignment-type-container');

    if (!assignmentToggles.length || !assignmentTypeField) {
      console.log('Assignment toggles or field not found');
      return;
    }

    const assignmentConfigs = {
      'date_time': {
        bgGradient: 'linear-gradient(to right, #f0f9ff, #bae6fd)',
        textColor: 'text-sky-800',
        iconColor: 'text-sky-700'
      },
      'appointment': {
        bgGradient: 'linear-gradient(to right, #f3e8ff, #d8b4fe)',
        textColor: 'text-purple-800',
        iconColor: 'text-purple-700'
      }
    };

    // Remove any existing click handlers and add new ones
    assignmentToggles.forEach(toggle => {
      // Clone node to remove all event listeners
      const newToggle = toggle.cloneNode(true);
      toggle.parentNode.replaceChild(newToggle, toggle);
    });

    // Re-query after cloning
    const newAssignmentToggles = modal.querySelectorAll('.assignment-toggle');

    newAssignmentToggles.forEach(toggle => {
      toggle.addEventListener('click', () => {
        const assignmentType = toggle.dataset.assignmentType;

        // Update assignment type field
        if (assignmentTypeField) {
          assignmentTypeField.value = assignmentType;
        }

        // Update toggle visual states
        this.updateAssignmentToggleVisualStates(modal, assignmentType, assignmentConfigs);

        // Show/hide appropriate assignment sections
        this.updateAssignmentSections(modal, assignmentType);
      });
    });

    // Initialize the default active state (date_time should be active by default)
    const defaultAssignmentType = assignmentTypeField.value || 'date_time';
    this.updateAssignmentToggleVisualStates(modal, defaultAssignmentType, assignmentConfigs);
    this.updateAssignmentSections(modal, defaultAssignmentType);
  }

  /**
   * Update visual states of assignment type toggles
   * @param {Element} modal - The modal element
   * @param {string} activeAssignmentType - The currently active assignment type
   * @param {Object} assignmentConfigs - Configuration for assignment styles
   */
  updateAssignmentToggleVisualStates(modal, activeAssignmentType, assignmentConfigs) {
    const assignmentToggles = modal.querySelectorAll('.assignment-toggle');

    assignmentToggles.forEach(toggle => {
      const toggleType = toggle.dataset.assignmentType;
      const toggleConfig = assignmentConfigs[toggleType] || assignmentConfigs['date_time'];

      if (toggleType === activeAssignmentType) {
        // Active toggle styling
        toggle.classList.remove('bg-white', 'text-gray-600', 'hover:bg-gray-50');
        toggle.classList.add(toggleConfig.textColor);
        toggle.style.background = toggleConfig.bgGradient;

        const toggleText = toggle.querySelector('.tab-text');
        if (toggleText) {
          toggleText.classList.remove('opacity-0', 'w-0');
          toggleText.classList.add('ml-1.5');
        }

        const icon = toggle.querySelector('i');
        if (icon) {
          icon.className = toggle.dataset.icon + ' h-4 w-4 ' + toggleConfig.iconColor;
        }

        // Adjust padding for active toggle
        if (toggle.classList.contains('px-3')) {
          toggle.classList.remove('px-3');
          toggle.classList.add('pl-3', 'pr-4');
        }
      } else {
        // Inactive toggle styling
        toggle.classList.remove(toggleConfig.textColor);
        toggle.classList.add('bg-white', 'text-gray-600', 'hover:bg-gray-50');
        toggle.style.background = '';

        const toggleText = toggle.querySelector('.tab-text');
        if (toggleText) {
          toggleText.classList.add('opacity-0', 'w-0');
          toggleText.classList.remove('ml-1.5');
        }

        const icon = toggle.querySelector('i');
        if (icon) {
          icon.className = toggle.dataset.icon + ' h-4 w-4';
        }

        // Reset padding for inactive toggle
        if (toggle.classList.contains('pl-3') && toggle.classList.contains('pr-4')) {
          toggle.classList.remove('pl-3', 'pr-4');
          toggle.classList.add('px-3');
        }
      }
    });
  }

  /**
   * Initialize appointment selectors functionality
   * @param {Element} modal - The modal element
   * @param {Object} config - Modal configuration
   */
  initializeAppointmentSelectors(modal, config) {
    const treatmentPlanSelect = modal.querySelector('#action_data_treatment_plan_id');
    const appointmentSelect = modal.querySelector('#action_data_appointment_id');

    if (!treatmentPlanSelect || !appointmentSelect) {
      console.log('Treatment plan or appointment selectors not found');
      return;
    }

    // Populate treatment plans from charting data
    this.populateTreatmentPlans(modal, treatmentPlanSelect);

    // Handle treatment plan change to update appointments
    treatmentPlanSelect.addEventListener('change', (e) => {
      const selectedOption = e.target.selectedOptions[0];
      this.updateAppointmentOptions(appointmentSelect, selectedOption);
    });

    // Handle appointment selection to auto-fill due date
    appointmentSelect.addEventListener('change', (e) => {
      this.handleAppointmentSelection(modal, e.target);
    });
  }

  /**
   * Populate treatment plans from charting data or via AJAX
   * @param {Element} modal - The modal element
   * @param {Element} treatmentPlanSelect - The treatment plan select element
   */
  populateTreatmentPlans(modal, treatmentPlanSelect) {
    // Clear existing options except the first one
    treatmentPlanSelect.innerHTML = '<option value="">Select treatment plan</option>';

    // First try to get treatment plans from charting data (if on charting page)
    const courseOfTreatmentItems = document.querySelectorAll('.course-of-treatment-item');

    if (courseOfTreatmentItems.length > 0) {
      // We're on a charting page, use DOM data
      courseOfTreatmentItems.forEach(item => {
        const id = item.dataset.courseOfTreatmentId;
        const appointmentsInfo = item.dataset.treatmentPlanAppointments;
        const text = item.textContent.trim().replace(/\n/g, ' – ');

        if (id && text) {
          const option = document.createElement('option');
          option.value = id;
          option.textContent = text;
          option.setAttribute('data-appointments-info', appointmentsInfo || '[]');
          treatmentPlanSelect.appendChild(option);
        }
      });
      console.log(`Populated ${courseOfTreatmentItems.length} treatment plans from DOM`);
      return;
    }

    // Not on charting page, fetch treatment plans via AJAX
    const patientId = this.getPatientIdFromModal(modal);
    if (!patientId) {
      console.log('No patient ID found, cannot fetch treatment plans');
      const option = document.createElement('option');
      option.value = '';
      option.textContent = 'No patient selected';
      option.disabled = true;
      treatmentPlanSelect.appendChild(option);
      return;
    }

    // Fetch treatment plans via AJAX
    this.fetchTreatmentPlansForPatient(patientId, treatmentPlanSelect);
  }

  /**
   * Get patient ID from modal context
   * @param {Element} modal - The modal element
   * @returns {string|null} - The patient ID or null if not found
   */
  getPatientIdFromModal(modal) {
    // Try to get patient ID from various sources
    const patientIdField = modal.querySelector('input[name="action_data[patient_id]"]');
    if (patientIdField && patientIdField.value) {
      return patientIdField.value;
    }

    // Try to get from URL if we're on a patient page
    const urlMatch = window.location.pathname.match(/\/patients\/(\d+)/);
    if (urlMatch) {
      return urlMatch[1];
    }

    // Try to get from data attributes on the modal
    if (modal.dataset.patientId) {
      return modal.dataset.patientId;
    }

    return null;
  }

  /**
   * Fetch treatment plans for a patient via AJAX
   * @param {string} patientId - The patient ID
   * @param {Element} treatmentPlanSelect - The treatment plan select element
   */
  fetchTreatmentPlansForPatient(patientId, treatmentPlanSelect) {
    console.log(`Fetching treatment plans for patient ${patientId}`);

    // Show loading state
    treatmentPlanSelect.innerHTML = '<option value="">Loading treatment plans...</option>';
    treatmentPlanSelect.disabled = true;

    fetch(`/admin/patients/${patientId}/treatment_plans_data`)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
      })
      .then(data => {
        // Clear loading state
        treatmentPlanSelect.innerHTML = '<option value="">Select treatment plan</option>';
        treatmentPlanSelect.disabled = false;

        if (data.treatment_plans && data.treatment_plans.length > 0) {
          data.treatment_plans.forEach(plan => {
            const option = document.createElement('option');
            option.value = plan.id;
            option.textContent = `${plan.id} ${plan.name}`;
            option.setAttribute('data-appointments-info', JSON.stringify(plan.appointments || []));
            treatmentPlanSelect.appendChild(option);
          });
          console.log(`Populated ${data.treatment_plans.length} treatment plans via AJAX`);
        } else {
          const option = document.createElement('option');
          option.value = '';
          option.textContent = 'No treatment plans available';
          option.disabled = true;
          treatmentPlanSelect.appendChild(option);
          console.log('No treatment plans found for patient');
        }
      })
      .catch(error => {
        console.error('Error fetching treatment plans:', error);
        treatmentPlanSelect.innerHTML = '<option value="">Error loading treatment plans</option>';
        treatmentPlanSelect.disabled = false;
      });
  }

  /**
   * Update appointment options based on selected treatment plan
   * @param {Element} appointmentSelect - The appointment select element
   * @param {Element} selectedOption - The selected treatment plan option
   */
  updateAppointmentOptions(appointmentSelect, selectedOption) {
    // Clear existing options
    appointmentSelect.innerHTML = '<option value="">Select appointment</option>';

    if (!selectedOption || !selectedOption.value) {
      appointmentSelect.disabled = true;
      return;
    }

    // Enable appointment select
    appointmentSelect.disabled = false;

    // Parse appointments data and populate options
    try {
      const appointmentsInfo = JSON.parse(selectedOption.dataset.appointmentsInfo || '[]');

      appointmentsInfo.forEach(([id, date, docId, docName]) => {
        const option = document.createElement('option');
        option.value = id;
        option.textContent = date;
        option.setAttribute('data-dentist-id', docId);
        option.setAttribute('data-dentist-name', docName);
        appointmentSelect.appendChild(option);
      });
    } catch (error) {
      console.error('Error parsing appointments info:', error);
      appointmentSelect.disabled = true;
    }
  }

  /**
   * Handle appointment selection to auto-fill due date
   * @param {Element} modal - The modal element
   * @param {Element} appointmentSelect - The appointment select element
   */
  handleAppointmentSelection(modal, appointmentSelect) {
    const selectedOption = appointmentSelect.selectedOptions[0];

    if (!selectedOption || !selectedOption.value) {
      return;
    }

    // Get the appointment date from the option text
    const appointmentDateText = selectedOption.textContent.trim();

    // Parse the appointment date and convert to dd/mm/yyyy format
    const parsedResult = this.parseAppointmentDateToInputFormat(appointmentDateText);

    if (parsedResult) {
      // Find the action date and time input fields
      const actionDateInput = modal.querySelector('#action_data_action_date');
      const actionTimeInput = modal.querySelector('#action_data_action_time');

      if (actionDateInput) {
        // Handle both string and object return types
        if (typeof parsedResult === 'string') {
          // Just a date, no time
          actionDateInput.value = parsedResult;
          if (actionTimeInput) {
            actionTimeInput.value = ''; // Clear time field
          }
          console.log('Auto-filled due date from appointment (no time):', parsedResult);
        } else if (parsedResult.date && parsedResult.time) {
          // Date and time available from appointment
          actionDateInput.value = parsedResult.date;
          if (actionTimeInput) {
            actionTimeInput.value = parsedResult.time;
          }
          console.log('Auto-filled due date from appointment:', parsedResult.date, 'with time:', parsedResult.time);
        }

        // Trigger change events to update any dependent functionality
        const dateChangeEvent = new Event('change', { bubbles: true });
        actionDateInput.dispatchEvent(dateChangeEvent);

        if (actionTimeInput) {
          const timeChangeEvent = new Event('change', { bubbles: true });
          actionTimeInput.dispatchEvent(timeChangeEvent);
        }
      }
    }
  }

  /**
   * Parse appointment date text to dd/mm/yyyy format
   * @param {string} appointmentDateText - The appointment date text (e.g., "Mon, 15 Jan 2024 at 02:30 PM" or "Appointment 1")
   * @returns {string|null} - Formatted date string or null if parsing fails
   */
  parseAppointmentDateToInputFormat(appointmentDateText) {
    try {
      // Check if this is just a position-based appointment (e.g., "Appointment 1")
      if (appointmentDateText.match(/^Appointment \d+$/)) {
        console.log('Appointment has no date assigned:', appointmentDateText);
        return null;
      }

      // Remove day name and ordinal suffixes (st, nd, rd, th)
      let cleanDateText = appointmentDateText.replace(/^[A-Za-z]+,?\s*/, ''); // Remove day name
      cleanDateText = cleanDateText.replace(/(\d+)(st|nd|rd|th)/, '$1'); // Remove ordinal suffixes

      // Extract time if present (e.g., "at 02:30 PM") before removing it
      const timeMatch = appointmentDateText.match(/\s+at\s+(\d{1,2}:\d{2})\s+(AM|PM)/i);
      let appointmentTime = null;

      if (timeMatch) {
        const time = timeMatch[1];
        const period = timeMatch[2].toUpperCase();

        // Convert to 24-hour format
        const [hours, minutes] = time.split(':');
        let hour24 = parseInt(hours, 10);

        if (period === 'PM' && hour24 !== 12) {
          hour24 += 12;
        } else if (period === 'AM' && hour24 === 12) {
          hour24 = 0;
        }

        appointmentTime = `${String(hour24).padStart(2, '0')}:${minutes}`;
      }

      // Remove time portion from date text
      cleanDateText = cleanDateText.replace(/\s+at\s+\d{1,2}:\d{2}\s+(AM|PM)/i, '');

      // Parse the cleaned date
      const parsedDate = new Date(cleanDateText);

      if (isNaN(parsedDate.getTime())) {
        console.warn('Could not parse appointment date:', appointmentDateText);
        return null;
      }

      // Format as dd/mm/yyyy
      const day = String(parsedDate.getDate()).padStart(2, '0');
      const month = String(parsedDate.getMonth() + 1).padStart(2, '0');
      const year = parsedDate.getFullYear();

      // If we have a time from the appointment, include it in the return value
      const dateString = `${day}/${month}/${year}`;

      return appointmentTime ? { date: dateString, time: appointmentTime } : dateString;
    } catch (error) {
      console.error('Error parsing appointment date:', appointmentDateText, error);
      return null;
    }
  }

  /**
   * Update assignment sections based on selected type
   * @param {Element} modal - The modal element
   * @param {string} assignmentType - The selected assignment type
   */
  updateAssignmentSections(modal, assignmentType) {
    const datetimeSection = modal.querySelector('#datetime-assignment-section');
    const appointmentSection = modal.querySelector('#appointment-assignment-section');

    if (assignmentType === 'appointment') {
      // Show appointment section, hide datetime section
      if (datetimeSection) {
        datetimeSection.style.display = 'none';
        datetimeSection.classList.add('hidden');
      }
      if (appointmentSection) {
        appointmentSection.style.display = 'block';
        appointmentSection.classList.remove('hidden');
      }

      // Load treatment plans for appointment selection
      this.loadTreatmentPlans(modal);
    } else {
      // Show datetime section, hide appointment section
      if (datetimeSection) {
        datetimeSection.style.display = 'block';
        datetimeSection.classList.remove('hidden');
      }
      if (appointmentSection) {
        appointmentSection.style.display = 'none';
        appointmentSection.classList.add('hidden');
      }
    }
  }

  /**
   * Update visual states of priority toggles
   * @param {Element} modal - The modal element
   * @param {string} activePriorityLevel - The currently active priority level
   * @param {Object} priorityConfigs - Configuration for priority styles
   */
  updatePriorityToggleVisualStates(modal, activePriorityLevel, priorityConfigs) {
    const priorityToggles = modal.querySelectorAll('.priority-toggle');

    priorityToggles.forEach(toggle => {
      const toggleLevel = toggle.dataset.priorityLevel;
      const toggleConfig = priorityConfigs[toggleLevel] || priorityConfigs['low'];

      if (toggleLevel === activePriorityLevel) {
        // Active toggle styling
        toggle.classList.remove('bg-white', 'text-gray-600', 'hover:bg-gray-50');
        toggle.classList.add(toggleConfig.textColor);
        toggle.style.background = toggleConfig.bgGradient;

        const toggleText = toggle.querySelector('.tab-text');
        if (toggleText) {
          toggleText.classList.remove('opacity-0', 'w-0');
          toggleText.classList.add('ml-1.5');
        }

        const icon = toggle.querySelector('i');
        if (icon) {
          icon.className = toggle.dataset.icon + ' h-4 w-4 ' + toggleConfig.iconColor;
        }

        // Adjust padding for active toggle
        if (toggle.classList.contains('px-3')) {
          toggle.classList.remove('px-3');
          toggle.classList.add('pl-3', 'pr-4');
        }
      } else {
        // Inactive toggle styling
        toggle.classList.remove(toggleConfig.textColor);
        toggle.classList.add('bg-white', 'text-gray-600', 'hover:bg-gray-50');
        toggle.style.background = '';

        const toggleText = toggle.querySelector('.tab-text');
        if (toggleText) {
          toggleText.classList.add('opacity-0', 'w-0');
          toggleText.classList.remove('ml-1.5');
        }

        const icon = toggle.querySelector('i');
        if (icon) {
          icon.className = toggle.dataset.icon + ' h-4 w-4';
        }

        // Reset padding for inactive toggle
        if (toggle.classList.contains('pl-3') && toggle.classList.contains('pr-4')) {
          toggle.classList.remove('pl-3', 'pr-4');
          toggle.classList.add('px-3');
        }
      }
    });
  }

  /**
   * Update visual states of action tabs
   * @param {Element} modal - The modal element
   * @param {string} activeActionType - The currently active action type
   * @param {Object} buttonConfigs - Configuration for button styles
   */
  updateTabVisualStates(modal, activeActionType, buttonConfigs) {
    const actionTabs = modal.querySelectorAll('.actiontab');

    actionTabs.forEach(tab => {
      const tabType = tab.dataset.actionType;
      const tabConfig = buttonConfigs[tabType] || buttonConfigs['task'];

      // Remove any existing ring/border classes that cause blue border
      tab.classList.remove('ring-2', 'ring-blue-300', 'ring-offset-1', 'ring-2', 'ring-offset-2');

      if (tabType === activeActionType) {
        // Active tab styling
        tab.classList.remove('bg-white', 'text-gray-600', 'hover:bg-gray-50');
        tab.classList.add(tabConfig.textColor);
        tab.style.background = tabConfig.bgGradient;

        const tabText = tab.querySelector('.tab-text');
        if (tabText) {
          tabText.classList.remove('opacity-0', 'w-0');
          tabText.classList.add('ml-1.5');
          tabText.style.transition = 'none';
        }

        const icon = tab.querySelector('i');
        if (icon) {
          icon.className = tab.dataset.icon + ' h-4 w-4 ' + tabConfig.iconColor;
        }

        // Adjust padding for active tab
        if (tab.classList.contains('px-3')) {
          tab.classList.remove('px-3');
          tab.classList.add('pl-3', 'pr-4');
        }
      } else {
        // Inactive tab styling
        tab.classList.remove(tabConfig.textColor);
        tab.classList.add('bg-white', 'text-gray-600', 'hover:bg-gray-50');
        tab.style.background = '';

        const tabText = tab.querySelector('.tab-text');
        if (tabText) {
          tabText.classList.add('opacity-0', 'w-0');
          tabText.classList.remove('ml-1.5');
          tabText.style.transition = 'none';
        }

        const icon = tab.querySelector('i');
        if (icon) {
          icon.className = tab.dataset.icon + ' h-4 w-4';
        }

        // Reset padding for inactive tab
        if (tab.classList.contains('pl-3') && tab.classList.contains('pr-4')) {
          tab.classList.remove('pl-3', 'pr-4');
          tab.classList.add('px-3');
        }
      }
    });
  }

  /**
   * Handle form submission - combine callback date/time fields if needed
   * @param {Element} modal - The modal element
   * @param {Event} event - The form submission event
   */
  handleFormSubmission(modal, event) {
    const actionTypeField = modal.querySelector('input[name="action_data[action_type]"]');
    const actionType = actionTypeField ? actionTypeField.value : '';

    // Combine the separate date and time fields for all action types
    const actionDateInput = modal.querySelector('input[name="action_data[action_date]"]');
    const actionTimeInput = modal.querySelector('input[name="action_data[action_time]"]');
    const dueDateInput = modal.querySelector('input[name="action_data[date_due]"]');

    if (actionDateInput && dueDateInput) {
      const dateValue = actionDateInput.value;
      const timeValue = actionTimeInput ? actionTimeInput.value : '';

      if (dateValue) {
        // Convert dd/mm/yyyy to yyyy-mm-dd format
        const dateParts = dateValue.split('/');
        if (dateParts.length === 3) {
          const isoDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

          if (timeValue) {
            // Use the provided time
            const combinedDateTime = `${isoDate}T${timeValue}`;
            dueDateInput.value = combinedDateTime;
            console.log('Combined date/time:', combinedDateTime);
          } else {
            // No time provided, use default 09:00
            dueDateInput.value = `${isoDate}T09:00`;
            console.log('Using date with default time:', `${isoDate}T09:00`);
          }
        }
      }
    }
  }

  /**
   * Update date input type and label based on action type
   * @param {Element} modal - The modal element
   * @param {string} actionType - The action type
   */
  updateDateInputForActionType(modal, actionType) {
    const dueDateInput = modal.querySelector('input[name="action_data[date_due]"]');
    const dueDateLabel = modal.querySelector('#due-date-label');
    const dueDateContainer = modal.querySelector('#due-date-container');
    const assignmentContainer = modal.querySelector('#assignment-type-container');
    const singleDateInput = modal.querySelector('#single-date-input');
    const splitDatetimeInputs = modal.querySelector('#split-datetime-inputs');
    const callbackDateInput = modal.querySelector('input[name="action_data[callback_date]"]');
    const callbackTimeInput = modal.querySelector('input[name="action_data[callback_time]"]');

    if (dueDateInput && dueDateLabel && dueDateContainer) {
      // Hide date due field and assignment type for comments, complaints and alerts (they don't require due dates)
      if (actionType === 'comment' || actionType === 'complaint' || actionType === 'alert') {
        dueDateContainer.style.display = 'none';
        if (assignmentContainer) {
          assignmentContainer.style.display = 'none';
        }
        dueDateInput.value = ''; // Clear any existing value
        if (callbackDateInput) callbackDateInput.value = '';
        if (callbackTimeInput) callbackTimeInput.value = '';
      } else {
        dueDateContainer.style.display = 'block';
        if (assignmentContainer) {
          assignmentContainer.style.display = 'block';
        }

        // All action types now use the same date/time inputs
        if (actionType === 'callback') {
          dueDateLabel.textContent = 'Callback date & time';
        } else if (actionType === 'reminder') {
          dueDateLabel.textContent = 'Reminder date & time';
        } else {
          dueDateLabel.textContent = 'Date due';
        }

        // Ensure the split datetime inputs are always visible for all action types
        if (splitDatetimeInputs) {
          splitDatetimeInputs.style.display = 'block';
          splitDatetimeInputs.classList.remove('hidden');
        }

        // Reinitialize date/time pickers after showing/hiding inputs
        this.initializeDateTimePicker(modal, {});
      }
    }
  }

  /**
   * Initialize datetime picker functionality
   * @param {Element} modal - The modal element
   * @param {Object} config - Modal configuration
   */
  initializeDateTimePicker(modal, config) {
    if (!modal) return;

    // Prevent double initialization
    if (modal.dataset.datePickerInitialized === 'true') {
      console.log('Date picker already initialized for modal:', modal.id);
      return;
    }

    console.log('Initializing date/time picker for modal:', modal.id);
    console.log('AirDatepicker available:', typeof AirDatepicker !== 'undefined');

    // Initialize date picker for action date input (all action types)
    const actionDateInput = modal.querySelector('#action_data_action_date');
    const actionDateIcon = modal.querySelector('#split-datetime-inputs [data-date-picker-trigger]');
    console.log('Action date input found:', !!actionDateInput);
    console.log('Action date icon found:', !!actionDateIcon);

    if (actionDateInput && typeof AirDatepicker !== 'undefined') {
      // Create AirDatepicker instance that only opens programmatically
      if (actionDateInput.datePicker) {
        actionDateInput.datePicker.destroy();
      }

      console.log('Creating AirDatepicker instance for icon trigger');
      try {
        // Create a hidden input for the datepicker to avoid conflicts
        const hiddenDateInput = document.createElement('input');
        hiddenDateInput.type = 'hidden';
        hiddenDateInput.style.position = 'absolute';
        hiddenDateInput.style.left = '-9999px';
        document.body.appendChild(hiddenDateInput);

        actionDateInput.datePicker = new AirDatepicker(hiddenDateInput, {
          isMobile: true,
          timepicker: false,
          autoClose: true,
          startDate: new Date(), // Set default to current date
          locale: {
            days: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
            daysShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
            daysMin: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],
            months: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
            monthsShort: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            today: 'Today',
            clear: 'Clear',
            dateFormat: 'dd/MM/yyyy',
            firstDay: 0
          },
          onSelect: ({date, formattedDate}) => {
            // Update the visible input when date is selected
            console.log('Date selected:', formattedDate);
            actionDateInput.value = formattedDate;
            // Clear appointment-related attributes since this is a manual date selection
            actionDateInput.removeAttribute('data-appointment-time');
            actionDateInput.removeAttribute('data-appointment-date-only');
            $(actionDateInput).change();
          },
          buttons: [
            {
              content: "Cancel",
              className: "edit-btn",
              onClick: (dp) => {
                dp.hide();
              }
            },
            {
              content: "Submit",
              className: "orangebtn",
              onClick: (dp) => {
                console.log('Single date Submit button clicked');
                console.log('singleDateInput element:', singleDateInput);
                console.log('dp.selectedDates:', dp.selectedDates);
                console.log('dp.opts.startDate:', dp.opts.startDate);

                // Apply the current date - either selected or the default startDate
                let dateToApply = null;

                if (dp.selectedDates && dp.selectedDates.length > 0) {
                  // User made a selection
                  console.log('Using selected date');
                  const selectedDate = dp.selectedDates[0];
                  const year = selectedDate.getFullYear();
                  const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
                  const day = String(selectedDate.getDate()).padStart(2, '0');
                  dateToApply = `${day}/${month}/${year}`;
                } else {
                  // No selection made, use the startDate (current date)
                  console.log('Using startDate/current date');
                  const startDate = dp.opts.startDate || new Date();
                  const year = startDate.getFullYear();
                  const month = String(startDate.getMonth() + 1).padStart(2, '0');
                  const day = String(startDate.getDate()).padStart(2, '0');
                  dateToApply = `${day}/${month}/${year}`;
                }

                console.log('Submit button - setting date:', dateToApply);
                console.log('Setting value on element:', actionDateInput);
                actionDateInput.value = dateToApply;
                // Clear appointment-related attributes since this is a manual date selection
                actionDateInput.removeAttribute('data-appointment-time');
                actionDateInput.removeAttribute('data-appointment-date-only');
                $(actionDateInput).change();
                console.log('Value after setting:', actionDateInput.value);
                dp.hide();
              }
            }
          ]
        });

        // Add click handler to the icon only
        if (actionDateIcon) {
          actionDateIcon.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Icon clicked, showing date picker');
            try {
              actionDateInput.datePicker.show();
            } catch (error) {
              console.error('Error showing date picker:', error);
            }
          });
          console.log('Date picker icon handler added successfully');
        }
      } catch (error) {
        console.error('Error creating AirDatepicker:', error);
      }
    } else {
      console.log('Action date input not found, or AirDatepicker not available');
    }

    // Initialize time picker for action time input
    const actionTimeInput = modal.querySelector('#action_data_action_time');
    const actionTimeIcon = modal.querySelector('#split-datetime-inputs [data-time-picker-trigger]');
    if (actionTimeInput && typeof AirDatepicker !== 'undefined') {
      // Destroy existing instance if it exists
      if (actionTimeInput.timePicker) {
        actionTimeInput.timePicker.destroy();
      }

      // Create a hidden input for the timepicker to avoid conflicts
      const hiddenTimeInput = document.createElement('input');
      hiddenTimeInput.type = 'hidden';
      hiddenTimeInput.style.position = 'absolute';
      hiddenTimeInput.style.left = '-9999px';
      document.body.appendChild(hiddenTimeInput);

      actionTimeInput.timePicker = new AirDatepicker(hiddenTimeInput, {
        isMobile: true,
        timepicker: true,
        onlyTimepicker: true,
        autoClose: true,
        startDate: new Date(), // Set default to current time
        locale: {
          timeFormat: 'HH:mm',
          firstDay: 0
        },
        onSelect: ({date, formattedDate}) => {
          // Update the visible input when time is selected
          console.log('Time selected:', formattedDate);
          actionTimeInput.value = formattedDate;
          $(actionTimeInput).change();
        },
        buttons: [
          {
            content: "Cancel",
            className: "edit-btn",
            onClick: (dp) => {
              dp.hide();
            }
          },
          {
            content: "Submit",
            className: "orangebtn",
            onClick: (dp) => {
              // Apply the current time - either selected or the default startDate
              let timeToApply = null;

              if (dp.selectedDates && dp.selectedDates.length > 0) {
                // User made a selection
                const selectedTime = dp.selectedDates[0];
                const hours = String(selectedTime.getHours()).padStart(2, '0');
                const minutes = String(selectedTime.getMinutes()).padStart(2, '0');
                timeToApply = `${hours}:${minutes}`;
              } else {
                // No selection made, use the startDate (current time)
                const startDate = dp.opts.startDate || new Date();
                const hours = String(startDate.getHours()).padStart(2, '0');
                const minutes = String(startDate.getMinutes()).padStart(2, '0');
                timeToApply = `${hours}:${minutes}`;
              }

              console.log('Submit button - setting time:', timeToApply);
              actionTimeInput.value = timeToApply;
              $(actionTimeInput).change();
              dp.hide();
            }
          }
        ]
      });

      // Add click handler to the time icon
      if (actionTimeIcon) {
        actionTimeIcon.addEventListener('click', () => {
          console.log('Time picker icon clicked');
          actionTimeInput.timePicker.show();
        });
      }
    }

    // Mark as initialized
    modal.dataset.datePickerInitialized = 'true';

    // Initialize date picker for callback date input
    const callbackDateInput = modal.querySelector('#action_data_callback_date');
    const callbackDateIcon = modal.querySelector('#split-datetime-inputs [data-date-picker-trigger]');
    if (callbackDateInput && typeof AirDatepicker !== 'undefined') {
      // Destroy existing instance if it exists
      if (callbackDateInput.datePicker) {
        callbackDateInput.datePicker.destroy();
      }

      // Create a hidden input for the datepicker to avoid conflicts
      const hiddenCallbackDateInput = document.createElement('input');
      hiddenCallbackDateInput.type = 'hidden';
      hiddenCallbackDateInput.style.position = 'absolute';
      hiddenCallbackDateInput.style.left = '-9999px';
      document.body.appendChild(hiddenCallbackDateInput);

      callbackDateInput.datePicker = new AirDatepicker(hiddenCallbackDateInput, {
        isMobile: true,
        timepicker: false,
        autoClose: true,
        startDate: new Date(), // Set default to current date
        locale: {
          days: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
          daysShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
          daysMin: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],
          months: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
          monthsShort: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          today: 'Today',
          clear: 'Clear',
          dateFormat: 'dd/MM/yyyy',
          firstDay: 0
        },
        onSelect: ({date, formattedDate}) => {
          // Update the visible input when date is selected
          console.log('Callback date selected:', formattedDate);
          callbackDateInput.value = formattedDate;
          $(callbackDateInput).change();
        },
        buttons: [
          {
            content: "Cancel",
            className: "edit-btn",
            onClick: (dp) => {
              dp.hide();
            }
          },
          {
            content: "Submit",
            className: "orangebtn",
            onClick: (dp) => {
              // Apply the current date - either selected or the default startDate
              let dateToApply = null;

              if (dp.selectedDates && dp.selectedDates.length > 0) {
                // User made a selection
                const selectedDate = dp.selectedDates[0];
                const year = selectedDate.getFullYear();
                const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
                const day = String(selectedDate.getDate()).padStart(2, '0');
                dateToApply = `${day}/${month}/${year}`;
              } else {
                // No selection made, use the startDate (current date)
                const startDate = dp.opts.startDate || new Date();
                const year = startDate.getFullYear();
                const month = String(startDate.getMonth() + 1).padStart(2, '0');
                const day = String(startDate.getDate()).padStart(2, '0');
                dateToApply = `${day}/${month}/${year}`;
              }

              console.log('Submit button - setting callback date:', dateToApply);
              callbackDateInput.value = dateToApply;
              $(callbackDateInput).change();
              dp.hide();
            }
          }
        ]
      });

      // Add click handler to the icon only
      if (callbackDateIcon) {
        callbackDateIcon.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('Callback date icon clicked, showing date picker');
          callbackDateInput.datePicker.show();
        });
      }
    }

    // Initialize time picker for callback time input
    const callbackTimeInput = modal.querySelector('#action_data_callback_time');
    const callbackTimeIcon = modal.querySelector('#split-datetime-inputs [data-time-picker-trigger]');
    if (callbackTimeInput && typeof AirDatepicker !== 'undefined') {
      // Destroy existing instance if it exists
      if (callbackTimeInput.timePicker) {
        callbackTimeInput.timePicker.destroy();
      }

      // Create a hidden input for the timepicker to avoid conflicts
      const hiddenCallbackTimeInput = document.createElement('input');
      hiddenCallbackTimeInput.type = 'hidden';
      hiddenCallbackTimeInput.style.position = 'absolute';
      hiddenCallbackTimeInput.style.left = '-9999px';
      document.body.appendChild(hiddenCallbackTimeInput);

      callbackTimeInput.timePicker = new AirDatepicker(hiddenCallbackTimeInput, {
        isMobile: true,
        timepicker: true,
        onlyTimepicker: true,
        autoClose: true,
        startDate: new Date(), // Set default to current time
        locale: {
          timeFormat: 'HH:mm',
          firstDay: 0
        },
        onSelect: ({date, formattedDate}) => {
          // Update the visible input when time is selected
          console.log('Time selected:', formattedDate);
          callbackTimeInput.value = formattedDate;
          $(callbackTimeInput).change();
        },
        buttons: [
          {
            content: "Cancel",
            className: "edit-btn",
            onClick: (dp) => {
              dp.hide();
            }
          },
          {
            content: "Submit",
            className: "orangebtn",
            onClick: (dp) => {
              // Apply the current time - either selected or the default startDate
              let timeToApply = null;

              if (dp.selectedDates && dp.selectedDates.length > 0) {
                // User made a selection
                const selectedTime = dp.selectedDates[0];
                const hours = String(selectedTime.getHours()).padStart(2, '0');
                const minutes = String(selectedTime.getMinutes()).padStart(2, '0');
                timeToApply = `${hours}:${minutes}`;
              } else {
                // No selection made, use the startDate (current time)
                const startDate = dp.opts.startDate || new Date();
                const hours = String(startDate.getHours()).padStart(2, '0');
                const minutes = String(startDate.getMinutes()).padStart(2, '0');
                timeToApply = `${hours}:${minutes}`;
              }

              console.log('Submit button - setting time:', timeToApply);
              callbackTimeInput.value = timeToApply;
              $(callbackTimeInput).change();
              dp.hide();
            }
          }
        ]
      });

      // Add click handler to the icon only
      if (callbackTimeIcon) {
        callbackTimeIcon.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('Callback time icon clicked, showing time picker');
          callbackTimeInput.timePicker.show();
        });
      }
    }
  }

  /**
   * Initialize team selector for team selection
   * @param {Element} modal - The modal element
   * @param {Object} config - Modal configuration
   */
  initializeTeamSelectorForModal(modal, config) {
    const modalId = modal.id;
    const searchInput = modal.querySelector(`#${modalId}-team-search-input`);
    const selectedSection = modal.querySelector(`#${modalId}-selected-team-section`);
    const selectedContainer = modal.querySelector(`#${modalId}-selected-team-container`);
    const teamMembersList = modal.querySelector(`#${modalId}-team-members-list`);

    if (!searchInput || !selectedSection || !selectedContainer || !teamMembersList) {
      console.warn('Team selector elements not found in modal');
      return;
    }

    // Initialize selected team members on modal open
    const initializeSelectedTeam = () => {
      const checkedCheckboxes = modal.querySelectorAll('.team-member-checkbox:checked');
      checkedCheckboxes.forEach(checkbox => {
        const userItem = checkbox.closest('[data-user-id]');
        if (userItem) {
          userItem.dataset.selected = 'true';
          moveToSelected(userItem);
        }
      });
    };

    // Handle checkbox changes
    const handleCheckboxChange = (event) => {
      const checkbox = event.target;
      const userItem = checkbox.closest('[data-user-id]');
      userItem.dataset.selected = checkbox.checked ? 'true' : 'false';

      if (checkbox.checked) {
        moveToSelected(userItem);
      } else {
        moveToAvailable(userItem);
      }
    };

    // Move team member to selected section
    const moveToSelected = (userItem) => {
      const userId = userItem.dataset.userId;
      const userName = userItem.dataset.userName;
      const avatarElement = userItem.querySelector('.rounded-full');

      // Check if already in selected section
      const existingBadge = selectedContainer.querySelector(`[data-user-id="${userId}"], [data-remove-user="${userId}"]`);
      if (existingBadge) return;

      // Create selected badge
      const selectedBadge = document.createElement('div');
      selectedBadge.className = 'flex items-center gap-2 p-1.5 pr-2 bg-gray-100 rounded-md text-sm';
      selectedBadge.setAttribute('data-remove-user', userId);
      selectedBadge.innerHTML = `
        <span class="relative flex shrink-0 overflow-hidden rounded-full h-6 w-6">
          ${avatarElement ? avatarElement.outerHTML.replace('h-8 w-8', 'h-6 w-6') : ''}
        </span>
        <span class="font-medium text-gray-900 whitespace-nowrap">${userName}</span>
        <button type="button" class="ml-1 text-gray-400 hover:text-gray-600 transition-colors duration-200" data-remove-user="${userId}">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-3 h-3">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      `;

      selectedContainer.appendChild(selectedBadge);
      selectedSection.classList.remove('hidden');

      // Hide from available list
      userItem.style.display = 'none';
    };

    // Move team member back to available section
    const moveToAvailable = (userItem) => {
      const userId = userItem.dataset.userId;

      // Remove from selected section
      const selectedBadge = selectedContainer.querySelector(`[data-remove-user="${userId}"]`);
      if (selectedBadge) {
        selectedBadge.remove();
      }

      // Show in available list
      userItem.style.display = 'flex';
      userItem.dataset.selected = 'false';

      // Hide selected section if empty
      if (selectedContainer.children.length === 0) {
        selectedSection.classList.add('hidden');
      }
    };

    // Handle remove button clicks
    const handleRemoveUser = (event) => {
      if (event.target.closest('[data-remove-user]')) {
        const removeButton = event.target.closest('[data-remove-user]');
        const userId = removeButton.getAttribute('data-remove-user');

        // Find and uncheck the corresponding checkbox
        const checkbox = modal.querySelector(`input[value="${userId}"]`);
        if (checkbox) {
          checkbox.checked = false;
          const userItem = checkbox.closest('[data-user-id]');
          if (userItem) {
            moveToAvailable(userItem);
          }
        }
      }
    };

    // Handle clicking on available team member (not checkbox)
    const handleAvailableTeamMemberClick = (event) => {
      // Don't trigger if clicking on checkbox or its label
      if (event.target.type === 'checkbox' || event.target.closest('input[type="checkbox"]')) {
        return;
      }

      const userItem = event.target.closest('[data-user-id]');
      if (!userItem) return;

      const checkbox = userItem.querySelector('.team-member-checkbox');
      if (checkbox && !checkbox.checked) {
        checkbox.checked = true;
        userItem.dataset.selected = 'true';
        moveToSelected(userItem);
      }
    };

    // Search functionality
    const handleSearch = (event) => {
      const searchTerm = event.target.value.toLowerCase();
      const userItems = teamMembersList.querySelectorAll('[data-user-id]');
      let visibleCount = 0;

      userItems.forEach(item => {
        const searchText = item.dataset.userSearch;
        const isVisible = searchText.includes(searchTerm);

        if (isVisible) {
          item.style.display = 'flex';
          visibleCount++;
        } else {
          item.style.display = 'none';
        }
      });

      // Show/hide "no results" message if needed
      const noResultsMsg = teamMembersList.querySelector('.no-results-message');
      if (visibleCount === 0 && searchTerm.length > 0) {
        if (!noResultsMsg) {
          const noResults = document.createElement('div');
          noResults.className = 'no-results-message px-2 py-4 text-center text-gray-500 text-sm';
          noResults.textContent = 'No team members found matching your search.';
          teamMembersList.appendChild(noResults);
        }
      } else if (noResultsMsg) {
        noResultsMsg.remove();
      }
    };

    // Event listeners
    modal.addEventListener('change', function(event) {
      if (event.target.classList.contains('team-member-checkbox')) {
        handleCheckboxChange(event);
      }
    });

    selectedContainer.addEventListener('click', handleRemoveUser);
    teamMembersList.addEventListener('click', handleAvailableTeamMemberClick);
    searchInput.addEventListener('input', handleSearch);

    // Initialize on modal open
    setTimeout(() => {
      initializeSelectedTeam();
    }, 100);
  }

  /**
   * Initialize AI input functionality for description field
   * @param {Element} modal - The modal element
   * @param {Object} config - Modal configuration
   */
  initializeAIInputForModal(modal, config) {
    // The AI tools are included via the shared partial
    // The functionality will be automatically initialized by the shared AI tools script
    console.log('AI input functionality initialized via shared AI tools partial');
  }



  /**
   * Move team member to selected section
   */
  moveToSelected(userItem, selectedSection, selectedContainer) {
    const userId = userItem.dataset.userId;
    const userName = userItem.dataset.userName;
    const avatarElement = userItem.querySelector('.rounded-full');

    // Check if already in selected section (both data-user-id and data-remove-user)
    const existingBadge = selectedContainer.querySelector(`[data-user-id="${userId}"], [data-remove-user="${userId}"]`);
    if (existingBadge) return;

    // Create selected badge
    const selectedBadge = document.createElement('div');
    selectedBadge.className = 'flex items-center gap-2 p-1.5 pr-2 bg-gray-100 rounded-md text-sm';
    selectedBadge.dataset.userId = userId;
    selectedBadge.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';

    selectedBadge.innerHTML = `
      <span class="relative flex shrink-0 overflow-hidden rounded-full h-6 w-6">
        ${avatarElement ? avatarElement.outerHTML.replace('h-8 w-8', 'h-6 w-6') : ''}
      </span>
      <span class="font-medium text-gray-900 whitespace-nowrap">${userName}</span>
      <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors hover:bg-red-100 h-5 w-5 text-gray-500 hover:text-red-600"
              data-remove-user="${userId}"
              aria-label="Remove ${userName}">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3.5 w-3.5">
          <path d="M18 6 6 18"></path>
          <path d="m6 6 12 12"></path>
        </svg>
      </button>
    `;

    // Show selected section if hidden (do this first)
    if (selectedSection.classList.contains('hidden')) {
      selectedSection.classList.remove('hidden');
    }
    selectedSection.style.display = 'block';

    // Add to selected container with animation
    selectedBadge.style.opacity = '0';
    selectedBadge.style.transform = 'scale(0.8)';
    selectedContainer.appendChild(selectedBadge);

    // Animate in
    requestAnimationFrame(() => {
      selectedBadge.style.opacity = '1';
      selectedBadge.style.transform = 'scale(1)';
    });

    // Hide from available list with animation
    userItem.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';
    userItem.style.opacity = '0';
    userItem.style.transform = 'translateX(-10px)';

    setTimeout(() => {
      userItem.style.display = 'none';
    }, 200);
  }

  /**
   * Move team member back to available section
   */
  moveToAvailable(userItem, selectedSection, selectedContainer) {
    const userId = userItem.dataset.userId;

    // Remove from selected section (both server-rendered and dynamic badges)
    const selectedBadges = selectedContainer.querySelectorAll(`[data-user-id="${userId}"], [data-remove-user="${userId}"]`);
    selectedBadges.forEach(badge => {
      const badgeContainer = badge.closest('.flex.items-center.gap-2');
      if (badgeContainer) {
        // Animate out
        badgeContainer.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';
        badgeContainer.style.opacity = '0';
        badgeContainer.style.transform = 'scale(0.8)';

        setTimeout(() => {
          badgeContainer.remove();

          // Hide selected section if empty
          const remainingBadges = selectedContainer.querySelectorAll('[data-user-id], [data-remove-user]');
          if (remainingBadges.length === 0) {
            selectedSection.classList.add('hidden');
            selectedSection.style.display = 'none';
          }
        }, 200);
      }
    });

    // Show in available list with animation
    userItem.style.display = 'flex';
    userItem.style.opacity = '0';
    userItem.style.transform = 'translateX(-10px)';

    requestAnimationFrame(() => {
      userItem.style.transition = 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out';
      userItem.style.opacity = '1';
      userItem.style.transform = 'translateX(0)';
    });
  }

  /**
   * Initialize practice/patient toggle functionality
   * @param {HTMLElement} modal - The modal element
   * @param {Object} config - Modal configuration
   */
  initializePracticePatientToggle(modal, config) {
    const practiceToggle = modal.querySelector('#practice-filter-toggle');
    const patientToggle = modal.querySelector('#patient-filter-toggle');
    const patientContainer = modal.querySelector('#patient-selection-container');
    const patientDropdownBtn = modal.querySelector('#patient-filter-toggle-btn');
    const patientMenu = modal.querySelector('#patient-filter-menu');
    const patientSearch = modal.querySelector('#patient-search');
    const patientList = modal.querySelector('#patient-list');
    const selectedPatientName = modal.querySelector('#selected-patient-name');
    const selectedPatientAvatar = modal.querySelector('#selected-patient-avatar-container');

    // Only initialize if the practice/patient toggle elements exist (sidebar context only)
    if (!practiceToggle || !patientToggle) {
      console.log('Practice/patient toggle not found - skipping initialization (not sidebar context)');
      return;
    }

    if (!patientContainer) {
      console.log('Patient selection container not found - skipping patient functionality');
      return;
    }

    // Toggle between practice and patient modes using custom styling (no conflicts with action buttons)
    const toggleHandler = (e) => {
      const clickedButton = e.target.closest('.practice-patient-toggle');
      const filterType = clickedButton.dataset.filterType;
      const isPatientFilter = filterType === 'patient';

      // Reset all buttons to inactive state
      [practiceToggle, patientToggle].forEach(btn => {
        // Reset to inactive state
        btn.classList.remove('pl-3', 'pr-4');
        btn.classList.add('px-3');
        btn.classList.remove('text-blue-800', 'text-green-800');
        btn.classList.add('bg-white', 'text-gray-600', 'hover:bg-gray-50');
        btn.style.background = '';

        // Hide text for inactive buttons
        const textSpan = btn.querySelector('.tab-text');
        const icon = btn.querySelector('i');
        if (textSpan) {
          textSpan.classList.add('opacity-0', 'w-0', 'overflow-hidden');
          textSpan.classList.remove('ml-2');
        }
        if (icon) {
          icon.classList.remove('text-blue-700', 'text-green-700');
          icon.classList.add('text-gray-500');
        }
      });

      // Activate the clicked button
      const bgGradient = clickedButton.dataset.bgGradient;
      const textColor = clickedButton.dataset.textColor;
      const iconColor = clickedButton.dataset.iconColor;

      clickedButton.classList.remove('bg-white', 'text-gray-600', 'hover:bg-gray-50', 'px-3');
      clickedButton.classList.add('pl-3', 'pr-4', textColor);
      clickedButton.style.background = bgGradient;

      // Show text and update icon color
      const textSpan = clickedButton.querySelector('.tab-text');
      const icon = clickedButton.querySelector('i');
      if (textSpan) {
        textSpan.classList.remove('opacity-0', 'w-0', 'overflow-hidden');
        textSpan.classList.add('ml-2');
      }
      if (icon) {
        icon.classList.remove('text-gray-500');
        icon.classList.add(iconColor);
      }

      // Show/hide patient selection container
      if (isPatientFilter) {
        patientContainer.classList.remove('hidden');
      } else {
        patientContainer.classList.add('hidden');
        // Reset patient selection
        if (selectedPatientName) selectedPatientName.textContent = 'Select a patient';
        if (selectedPatientAvatar) {
          selectedPatientAvatar.innerHTML = '<i class="fa-solid fa-user text-gray-400 text-xs"></i>';
        }
      }
    };

    practiceToggle.addEventListener('click', toggleHandler);
    patientToggle.addEventListener('click', toggleHandler);

    // Initialize practice button as active with proper styling
    this.initializePracticeButtonActive(practiceToggle);

    // Patient dropdown functionality
    if (patientDropdownBtn && patientMenu) {
      patientDropdownBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const isOpen = !patientMenu.classList.contains('hidden');

        if (isOpen) {
          patientMenu.classList.add('hidden');
          patientDropdownBtn.setAttribute('aria-expanded', 'false');
        } else {
          patientMenu.classList.remove('hidden');
          patientDropdownBtn.setAttribute('aria-expanded', 'true');
          if (patientSearch) patientSearch.focus();
        }
      });
    }

    // Patient search functionality
    if (patientSearch && patientList) {
      let searchTimeout;

      patientSearch.addEventListener('input', (e) => {
        const query = e.target.value.trim();

        clearTimeout(searchTimeout);

        if (query.length === 0) {
          patientList.innerHTML = '<div class="px-4 py-2 text-sm text-gray-500 text-center">Type to search for patients...</div>';
          return;
        }

        if (query.length < 3) {
          patientList.innerHTML = '<div class="px-4 py-2 text-sm text-gray-500 text-center">Type at least 3 characters to search</div>';
          return;
        }

        searchTimeout = setTimeout(() => {
          this.searchPatients(query, patientList);
        }, 300);
      });
    }

    // Handle patient selection
    if (patientList) {
      patientList.addEventListener('click', (e) => {
        const patientOption = e.target.closest('.patient-option');
        if (!patientOption) return;

        e.preventDefault();
        const patientId = patientOption.dataset.patientId;
        const patientName = patientOption.dataset.patientName;
        const avatarHtml = patientOption.querySelector('.relative.flex.shrink-0').innerHTML;

        // Update the selected patient in the button
        if (selectedPatientName) {
          selectedPatientName.textContent = patientName;
          selectedPatientName.classList.remove('text-gray-500');
          selectedPatientName.classList.add('text-gray-700');
        }

        if (selectedPatientAvatar) {
          selectedPatientAvatar.innerHTML = avatarHtml;
        }

        // Close the dropdown
        if (patientMenu) patientMenu.classList.add('hidden');
        if (patientDropdownBtn) patientDropdownBtn.setAttribute('aria-expanded', 'false');

        // Store selected patient ID for form submission
        const form = modal.querySelector('form');
        if (form) {
          let patientIdField = form.querySelector('input[name="selected_patient_id"]');
          if (!patientIdField) {
            patientIdField = document.createElement('input');
            patientIdField.type = 'hidden';
            patientIdField.name = 'selected_patient_id';
            form.appendChild(patientIdField);
          }
          patientIdField.value = patientId;
        }
      });
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (patientDropdownBtn && patientMenu &&
          !e.target.closest('#patient-filter-toggle-btn') &&
          !e.target.closest('#patient-filter-menu')) {
        patientMenu.classList.add('hidden');
        patientDropdownBtn.setAttribute('aria-expanded', 'false');
      }
    });
  }

  /**
   * Initialize the practice button as active with proper styling
   * @param {HTMLElement} practiceButton - The practice toggle button
   */
  initializePracticeButtonActive(practiceButton) {
    if (!practiceButton) return;

    // Use the data attributes for styling
    const bgGradient = practiceButton.dataset.bgGradient;
    const textColor = practiceButton.dataset.textColor;
    const iconColor = practiceButton.dataset.iconColor;

    // Set active styling
    practiceButton.style.background = bgGradient;
    practiceButton.classList.add(textColor);

    // Ensure text is visible and icon has correct color
    const textSpan = practiceButton.querySelector('.tab-text');
    const icon = practiceButton.querySelector('i');
    if (textSpan) {
      textSpan.classList.remove('opacity-0', 'w-0', 'overflow-hidden');
      textSpan.classList.add('ml-2');
    }
    if (icon) {
      icon.classList.remove('text-gray-500');
      icon.classList.add(iconColor);
    }
  }

  /**
   * Search for patients via AJAX
   * @param {string} query - Search query
   * @param {HTMLElement} patientList - Container to display results
   */
  searchPatients(query, patientList) {
    patientList.innerHTML = '<div class="px-4 py-2 text-sm text-gray-500 text-center">Searching...</div>';

    fetch(`/admin/patients/search?query=${encodeURIComponent(query)}&format=json`)
      .then(response => response.json())
      .then(data => {
        let html = '';

        if (data.length === 0) {
          html = '<div class="px-4 py-2 text-sm text-gray-500 text-center">No patients found</div>';
        } else {
          data.forEach(patient => {
            const avatarHtml = patient.image_url ?
              `<img src="${patient.image_url}" class="w-7 h-7 rounded-full object-cover" alt="${patient.name}">` :
              `<div class="flex-shrink-0 h-7 w-7 bg-gray-200 rounded-full flex items-center justify-center">
                 <i class="fa-solid fa-user text-gray-400 text-xs"></i>
               </div>`;

            html += `
              <a href="#" class="patient-option text-gray-700 group flex items-center px-4 py-2 text-sm hover:bg-gray-100" role="menuitem" data-patient-id="${patient.id}" data-patient-name="${patient.name}">
                <span class="relative flex shrink-0 overflow-hidden h-7 w-7 rounded-full mr-3">
                  ${avatarHtml}
                </span>
                <div class="flex-1">
                  <div class="font-medium">${patient.name}</div>
                  <div class="text-xs text-gray-500">${patient.dob || ''} ${patient.address || ''}</div>
                </div>
              </a>
            `;
          });
        }

        patientList.innerHTML = html;
      })
      .catch(error => {
        console.error('Error searching patients:', error);
        patientList.innerHTML = '<div class="px-4 py-2 text-sm text-red-500 text-center">Error searching patients</div>';
      });
  }
}

// Create global instance
window.unifiedActionModal = new UnifiedActionModal();

// Auto-register common modals when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Register sidebar modal
  if (document.getElementById('sidebarNewActionModal')) {
    window.unifiedActionModal.registerModal('sidebarNewActionModal', {
      context: 'sidebar',
      openTriggerSelector: '[data-modal-target="sidebarNewActionModal"]'
    });
  }

  // Register patient actions modal
  if (document.getElementById('patientNewActionModal')) {
    window.unifiedActionModal.registerModal('patientNewActionModal', {
      context: 'patient-actions',
      openTriggerSelector: '#open-new-action-modal'
    });
  }

  // Register general actions modal (if still used)
  if (document.getElementById('newActionModal')) {
    window.unifiedActionModal.registerModal('newActionModal', {
      context: 'general',
      openTriggerSelector: '#open-new-action-modal'
    });
  }

  // Register assigned team modal
  if (document.getElementById('assigned-team-modal')) {
    window.unifiedActionModal.registerModal('assigned-team-modal', {
      context: 'assigned-team',
      enableAI: false,
      enableTeamSelector: true,
      openTriggerSelector: '#open-assigned-team-modal'
    });
  }
});

// Export for module usage
export default UnifiedActionModal;
