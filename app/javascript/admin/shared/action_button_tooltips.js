/**
 * Action Button Tooltips
 * 
 * This file handles the initialization of Tippy.js tooltips for action buttons
 */

document.addEventListener('DOMContentLoaded', function() {
  initActionButtonTooltips();
});

/**
 * Initialize Tippy tooltips for action buttons
 */
function initActionButtonTooltips() {
  // Initialize tooltips for all action buttons with data-tippy-content
  const actionButtons = document.querySelectorAll('[data-tippy-content]');
  
  if (actionButtons.length > 0 && window.tippy) {
    actionButtons.forEach(button => {
      const content = button.getAttribute('data-tippy-content');
      if (content) {
        window.tippy(button, {
          content: content,
          placement: 'top',
          theme: 'light',
          delay: [500, 0], // 500ms delay on show, 0ms on hide
          duration: [200, 150], // 200ms show animation, 150ms hide animation
          arrow: true,
          interactive: false,
          hideOnClick: true,
          trigger: 'mouseenter focus'
        });
      }
    });
  }
}

/**
 * Reinitialize tooltips for dynamically added content
 * Call this function after adding new action buttons to the DOM
 */
function reinitActionButtonTooltips() {
  initActionButtonTooltips();
}

// Export for use in other modules
window.initActionButtonTooltips = initActionButtonTooltips;
window.reinitActionButtonTooltips = reinitActionButtonTooltips;
