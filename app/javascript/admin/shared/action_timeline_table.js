/**
 * Action Timeline Table
 * 
 * This file handles the functionality of the action timeline toggle in tables
 * Similar to the comments system but for timeline display
 */

document.addEventListener('DOMContentLoaded', function() {
  initActionTimeline();
});

/**
 * Initialize the action timeline functionality
 */
function initActionTimeline() {
  // Find all timeline buttons in the actions table
  const timelineButtons = document.querySelectorAll('.timeline-toggle');

  // Handle timeline toggle buttons
  timelineButtons.forEach(button => {
    button.classList.add('action-timeline-btn');
    setupTimelineButtonListener(button);
  });
}

/**
 * Set up event listener for timeline button
 * @param {HTMLElement} button - The timeline button element
 */
function setupTimelineButtonListener(button) {
  button.addEventListener('click', function(e) {
    e.preventDefault();
    e.stopPropagation();

    // Get the action ID
    const actionId = this.getAttribute('data-action-id');
    if (!actionId) return;

    // Get the action card/row
    const actionCard = this.closest('tr') || this.closest('.action-card');
    if (!actionCard) return;

    // Check if timeline section already exists
    let timelineSection = document.querySelector(`.action-timeline-row[data-action-id="${actionId}"]`);

    // If timeline section doesn't exist, create it
    if (!timelineSection) {
      timelineSection = createTimelineSection(actionCard, actionId);
      // Fetch and load the timeline data
      fetchTimelineData(actionId, timelineSection);

      // Close other sections before showing this one
      closeOtherTimelineSections(timelineSection);

      // Show the newly created timeline section
      if (timelineSection.classList.contains('action-timeline-row')) {
        timelineSection.style.display = 'table-row';

        // Also remove hidden class from the inner timeline section div
        const innerTimelineSection = timelineSection.querySelector('.action-timeline-section');
        if (innerTimelineSection) {
          innerTimelineSection.classList.remove('hidden');
        }
      } else {
        timelineSection.classList.remove('hidden');
      }
      button.classList.add('active');
    } else {
      // Timeline section exists, so toggle it
      // Check if button is active to determine visibility state
      const isCurrentlyVisible = button.classList.contains('active');

      if (isCurrentlyVisible) {
        // Hide the timeline section
        if (timelineSection.classList.contains('action-timeline-row')) {
          timelineSection.style.display = 'none';
        } else {
          timelineSection.classList.add('hidden');
        }
        button.classList.remove('active');
      } else {
        // Close other sections before showing this one
        closeOtherTimelineSections(timelineSection);

        // Show the existing timeline section
        if (timelineSection.classList.contains('action-timeline-row')) {
          timelineSection.style.display = 'table-row';

          // Also remove hidden class from the inner timeline section div
          const innerTimelineSection = timelineSection.querySelector('.action-timeline-section');
          if (innerTimelineSection) {
            innerTimelineSection.classList.remove('hidden');
          }
        } else {
          timelineSection.classList.remove('hidden');
        }
        button.classList.add('active');
      }
    }
  });
}

/**
 * Close other timeline sections
 * @param {HTMLElement} currentTimelineSection - The current timeline section to keep open
 */
function closeOtherTimelineSections(currentTimelineSection) {
  // Close any other open timeline sections
  document.querySelectorAll('.action-timeline-row').forEach(row => {
    if (row !== currentTimelineSection) {
      row.style.display = 'none';

      // Reset the button appearance
      const actionRow = row.previousElementSibling;
      if (actionRow) {
        const btn = actionRow.querySelector('.action-timeline-btn');
        if (btn) {
          btn.classList.remove('active');
        }
      }
    }
  });
}

/**
 * Create the timeline section HTML
 * @param {HTMLElement} actionCard - The action card element
 * @param {string} actionId - The ID of the action
 * @returns {HTMLElement} - The timeline section element
 */
function createTimelineSection(actionCard, actionId) {
  // Create the timeline section element
  let timelineSection = document.createElement('div');
  timelineSection.className = 'action-timeline-section bg-gray-50 p-4';
  timelineSection.setAttribute('data-action-id', actionId);
  
  // Create the timeline section HTML
  timelineSection.innerHTML = `
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-sm font-medium text-gray-900 flex items-center">
        <i class="fas fa-clock h-4 w-4 mr-2 text-purple-600"></i>
        Action Timeline
      </h3>
    </div>
    <div class="timeline-container">
      <div class="flex items-center justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        <span class="ml-3 text-sm text-gray-600">Loading timeline...</span>
      </div>
    </div>
  `;
  
  // Insert the timeline section appropriately based on the layout
  const actionCardContainer = actionCard.querySelector('.action-card-container');

  if (actionCardContainer) {
    actionCardContainer.parentNode.insertBefore(timelineSection, actionCardContainer.nextSibling);
  } else if (actionCard.tagName === 'TR') {
    // For table layouts, create a new table row
    const timelineRow = document.createElement('tr');
    timelineRow.className = 'action-timeline-row bg-gray-50';
    timelineRow.setAttribute('data-action-id', actionId);
    timelineRow.style.display = 'none'; // Initially hidden

    // Get the number of columns in the table
    const columnCount = actionCard.children.length;

    // Create a single cell that spans all columns
    const timelineCell = document.createElement('td');
    timelineCell.setAttribute('colspan', columnCount);
    timelineCell.className = 'p-4 rounded-xl bg-gray-50';

    // Move the timeline section into the cell
    timelineCell.appendChild(timelineSection);
    timelineRow.appendChild(timelineCell);

    // Insert the row after the action row
    actionCard.parentNode.insertBefore(timelineRow, actionCard.nextSibling);

    return timelineRow;
  }

  return timelineSection;
}

/**
 * Fetch timeline data from the server
 * @param {string} actionId - The ID of the action
 * @param {HTMLElement} timelineSection - The timeline section element
 */
function fetchTimelineData(actionId, timelineSection) {
  fetch(`/admin/actions/${actionId}/timeline`)
    .then(response => {
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.text();
    })
    .then(html => {
      // Parse the HTML to extract timeline events
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      const timelineEvents = doc.querySelectorAll('.timeline-event');

      // Find the timeline container within the section
      const timelineContainer = timelineSection.querySelector('.timeline-container');
      if (timelineContainer) {
        timelineContainer.innerHTML = createStyledTimeline(timelineEvents);
      }
    })
    .catch(error => {
      console.error('Error fetching timeline data:', error);
      const timelineContainer = timelineSection.querySelector('.timeline-container');
      if (timelineContainer) {
        timelineContainer.innerHTML = `
          <div class="flex items-center justify-center py-8">
            <div class="text-red-600">
              <i class="fas fa-exclamation-triangle h-5 w-5 mr-2"></i>
              <span class="text-sm">Error loading timeline</span>
            </div>
          </div>
        `;
      }
    });
}

/**
 * Create styled timeline HTML from timeline events
 * @param {NodeList} timelineEvents - The timeline event elements
 * @returns {string} - The styled timeline HTML
 */
function createStyledTimeline(timelineEvents) {
  if (timelineEvents.length === 0) {
    return `
      <div class="flex items-center justify-center py-8">
        <div class="text-gray-500">
          <i class="fas fa-clock h-5 w-5 mr-2"></i>
          <span class="text-sm">No timeline events found</span>
        </div>
      </div>
    `;
  }

  let timelineHTML = '<div class="relative pl-8 border-l-2 border-gray-200 ml-3">';

  timelineEvents.forEach((event, index) => {
    const isLast = index === timelineEvents.length - 1;
    const eventTitle = event.querySelector('h6')?.textContent?.trim() || 'Unknown Event';
    const eventDetails = event.querySelector('p')?.textContent?.trim() || '';
    const eventComment = event.querySelector('small')?.textContent?.trim() || '';

    // Determine icon and colors based on event type
    let iconClass = 'fa-plus';
    let iconBgClass = 'bg-blue-100';
    let iconTextClass = 'text-blue-600';

    if (eventTitle.toLowerCase().includes('comment')) {
      iconClass = 'fa-comment';
      iconBgClass = 'bg-green-100';
      iconTextClass = 'text-green-600';
    } else if (eventTitle.toLowerCase().includes('reminder')) {
      iconClass = 'fa-bell';
      iconBgClass = 'bg-amber-100';
      iconTextClass = 'text-amber-600';
    }

    timelineHTML += `
      <div class="relative mb-6 ${isLast ? 'mb-0' : ''}">
        <div class="absolute -left-[21px] top-0 h-7 w-7 rounded-full ${iconBgClass} flex items-center justify-center shadow-sm z-10">
          <i class="fas ${iconClass} h-3.5 w-3.5 ${iconTextClass}"></i>
        </div>
        <div class="bg-gradient-to-b from-white to-gray-50/90 rounded-lg border border-gray-200/60 shadow-[0_2px_10px_-2px_rgba(0,0,0,0.06)] p-4 transition-all duration-200 hover:shadow-[0_4px_12px_-2px_rgba(0,0,0,0.08)]">
          <div class="flex items-center justify-between mb-2">
            <h5 class="text-sm font-medium text-gray-800">${eventTitle}</h5>
            <span class="text-xs text-gray-500">${eventDetails.split('|').pop()?.trim() || ''}</span>
          </div>
          ${eventDetails.split('|')[0] ? `<p class="text-xs text-gray-600 mb-1">${eventDetails.split('|')[0].trim()}</p>` : ''}
          ${eventComment ? `<p class="text-xs text-gray-500 italic">${eventComment}</p>` : ''}
        </div>
      </div>
    `;
  });

  timelineHTML += '</div>';
  return timelineHTML;
}

// Export functions for use in other modules
window.initActionTimeline = initActionTimeline;
