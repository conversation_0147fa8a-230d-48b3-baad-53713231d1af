$(document).ready(function () {
  if (!($(".course-of-treatment-item.sleected-cot").length || $(".treatment-plan-details").length)) return;

  const courseOfTreatmentId = $(".course-of-treatment-item.sleected-cot").data('course-of-treatment-id') || $(".treatment-plan-details").data('course-of-treatment-id');

  $.ajax({
    url: `/admin/patients/${window._patientId}/charting/load_charted_treatments?course_of_treatment_id=${courseOfTreatmentId}`,
    type: 'GET',
    dataType: 'json',
    success: function (chartedTreatments) {
      chartedTreatments.forEach(function(ct) {
        if (ct.tooth_position.includes(' - ')) {
          applyTreatmentForRange(
            ct.tooth_position,
            ct.treatment_folder.toLowerCase(),
            ct.is_full_tooth_treatment,
            ct.treatment_id,                   // integer
            ct.is_completed,                   // boolean
            ct.ctid,                           // charted_treatment record ID
            ct.surface                         // e.g. "M", "MOD", or null
          );
        } else {
          // Single-tooth logic
          applyTreatmentToTooth(
            ct.tooth_position,
            ct.treatment_folder.toLowerCase(),
            ct.is_full_tooth_treatment,
            ct.treatment_id,
            ct.is_completed,
            ct.ctid,
            ct.surface
          );

          // If you also maintain a "front" facing version:
          const fPos = ct.tooth_position + 'F';
          applyTreatmentToTooth(
            fPos,
            ct.treatment_folder.toLowerCase(),
            ct.is_full_tooth_treatment,
            ct.treatment_id,
            ct.is_completed,
            ct.ctid,
            ct.surface
          );
        }
      });

      calculateTotalPrice();
      updateTotals();
      changeAppointmentsTotals();
    },
    error: function (xhr, status, error) {
      console.error('Failed to load existing charted treatments: ', error);
    }
  });

  function calculateTotalPrice() {
    let total = 0;
    document.querySelectorAll('.price').forEach(function (priceElement) {
      let priceText = priceElement.textContent.trim().replace('£', '');
      let price = parseFloat(priceText);
      if (!isNaN(price)) {
        total += price;
      }
    });
    let totalPrice = document.getElementById('total-price');
    if (totalPrice) {
      totalPrice.textContent = `£${total.toFixed(2)}`;
    }
  }

  // Sends an AJAX request to assign a base treatment to the tooth.
  function saveBaseTreatment(tooth, surface, multiClick, surfaceOrderForThisClick, onSuccess, onError) {
    $.ajax({
      url: `/admin/patients/${window._patientId}/charting/assign_base_treatment`,
      type: 'POST',
      data: {
        tooth_id: tooth,
        surface_id: surface,
        treatment_id: window._selectedTreatmentId,
        patient_id: window._patientId,
        pricing_type: $('#treatment-popout .selected').data('plan'),
        multi_click: multiClick,
        surface_order: surfaceOrderForThisClick
      },
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      success: function (response) {
        if (response && onSuccess) {
          onSuccess(response);
        } else if (onError) {
          onError(response);
        }
      },
      error: function (xhr, status, error) {
        console.error(`Error assigning base treatment for tooth ${tooth}:`, error);
        if (onError) onError(error);
      }
    });
  }

  // Applies the base treatment images to the tooth (and F-tooth if exists).
  function applyBaseTreatmentToTooth(
    toothPosition,
    treatmentName,
    isFullToothTreatment,
    treatmentId,
    ctid,
    removeTreatmentWhenCompleted,
    removeToothWhenCompleted,
    surface
  ) {

    console.log('toothPosition:', toothPosition)

    const $toothElement = $(`.teethgrid > div.${toothPosition.toLowerCase()}`);
    const $fToothElement = $(`.teethgrid > div.${toothPosition.toLowerCase()}f`);

    // Full-tooth treatment
    if (isFullToothTreatment) {
      const imageName = `${treatmentName.toLowerCase()}/${toothPosition}.png`;
      const frontImageName = `${treatmentName.toLowerCase()}/${toothPosition}F.png`;
      const imageTag = `<img src="https://upodmedican.b-cdn.net/${imageName}" 
        class="img-fluid absolute treatment-id-${treatmentId} ctid-${ctid} tooth-treatment-img completed-treatment" 
        style="left:0; z-index: 9; top:0; opacity:1;">`;
      const imageTagF = `<img src="https://upodmedican.b-cdn.net/${frontImageName}" 
        class="img-fluid absolute treatment-id-${treatmentId} ctid-${ctid} tooth-treatment-img completed-treatment" 
        style="left:0; z-index: 9; top:0; opacity:1;">`;

      if ($toothElement.find(`img[src='https://upodmedican.b-cdn.net/${imageName}'].treatment-id-${treatmentId}`).length === 0) {
        $toothElement.append(imageTag);
      }
      if ($fToothElement.find(`img[src='https://upodmedican.b-cdn.net/${frontImageName}'].treatment-id-${treatmentId}`).length === 0) {
        $fToothElement.append(imageTagF);
      }
    }
    // Surface-based treatment
    else {
      const imageName = `${treatmentName.toLowerCase()}/${toothPosition}_${surface}.png`;
      const frontImageName = `${treatmentName.toLowerCase()}/${toothPosition}F_${surface}.png`;
      const imageTag = `<img src="https://upodmedican.b-cdn.net/${imageName}" 
        class="img-fluid absolute treatment-id-${treatmentId} ctid-${ctid} tooth-treatment-img completed-treatment" 
        style="left:0; z-index: 9; top:0; opacity:1;">`;
      const imageTagF = `<img src="https://upodmedican.b-cdn.net/${frontImageName}" 
        class="img-fluid absolute treatment-id-${treatmentId} ctid-${ctid} tooth-treatment-img completed-treatment" 
        style="left:0; z-index: 9; top:0; opacity:1;">`;

      // Only place one image if it doesn't already exist
      if ($toothElement.find(`img[src='https://upodmedican.b-cdn.net/${imageName}'].treatment-id-${treatmentId}`).length === 0) {
        $toothElement.append(imageTag);
      }
      if ($fToothElement.find(`img[src='https://upodmedican.b-cdn.net/${frontImageName}'].treatment-id-${treatmentId}`).length === 0) {
        $fToothElement.append(imageTagF);
      }
    }

    // Hide images if treatment/tooth is completed or removed
    document.querySelectorAll(`.tooth-treatment-img.ctid-${ctid}`).forEach(img => {
      if (removeToothWhenCompleted === 'true') {
        const toothContainer = img.closest('div');
        const images = toothContainer.querySelectorAll('img.completed-treatment, img[class$="-tooth"]');
        images.forEach(image => {
          image.style.display = 'none';
        });
      } else if (removeTreatmentWhenCompleted === 'true') {
        img.style.display = 'none';
      }
    });
  }

  // Appends a tooth image to the grid for normal (non-base) charting
  function appendToothImage(position, surfaceType, chartingTreatmentId) {
    let imageName;
    if (window._isFullToothTreatment) {
      imageName = `${window._selectedTreatment}/${position}.png`;
    } else {
      imageName = `${window._selectedTreatment}/${position}_${surfaceType}.png`;
    }

    const imageUrl = `https://upodmedican.b-cdn.net/${imageName}`;
    const imageTag = `<img src="${imageUrl}" 
      class="ctid-${chartingTreatmentId} img-fluid absolute treatment-id-${window._selectedTreatmentId} tooth-treatment-img" 
      style="left:0; z-index: 9; top:0;">`;

    const targetElement = $(`.teethgrid > div.${position.toLowerCase()}`);
    // Prevent duplicates
    if (targetElement.find(`img[src='${imageUrl}'].treatment-id-${window._selectedTreatmentId}`).length === 0) {
      targetElement.prepend(imageTag);
    }
  }

  // Sends an AJAX request to assign a normal (non-base) treatment to a tooth.
  function assignNormalTreatment(
    toothPosition,
    surface,
    multiClick,
    surfaceOrderForThisClick,
    appointmentId,
    onSuccess,
    onError
  ) {
    $.ajax({
      url: `/admin/patients/${window._patientId}/charting/create_treatment`,
      type: 'POST',
      data: {
        multi_click: multiClick,
        surface_order: surfaceOrderForThisClick,
        tooth_id: toothPosition,
        surface_id: surface,
        pricing_type: $('#treatment-popout .selected').data('plan'),
        appointment_id: appointmentId,
        treatment_id: window._selectedTreatmentId,
        child_tooth: false // If needed, adjust accordingly
      },
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      success: function (response) {
        if (response) {
          onSuccess(response);
        } else {
          onError(response);
        }
      },
      error: function (xhr, status, error) {
        onError(error);
      }
    });
  }

  // Reusable function to handle successful normal assignment
  function handleAssignNormalTreatmentSuccess(response, surface, toothPosition) {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = response;

    const chartedTreatmentContainer = tempDiv.querySelector('.charted-treatment-container');
    const responseId = chartedTreatmentContainer ? chartedTreatmentContainer.getAttribute('data-id') : null;

    // Remove existing container if present, then append new
    $(`.charted-treatment-container[data-id="${responseId}"]`).remove();
    $('.selected > .accordion').append(response);

    $('.selected > .accordion').find('.accordion-body').addClass('selected');

    // Extract the newly created ID for referencing in the appended images
    const chartingTreatmentId = $(response).find('.delete-treatment').data('id');

    // Append images
    appendToothImage(toothPosition, surface, chartingTreatmentId);

    // For F tooth
    let fToothPosition = `${toothPosition}F`;
    let fToothDiv = $(`.teethgrid > div.${fToothPosition.toLowerCase()}`);
    if (fToothDiv.length > 0) {
      appendToothImage(fToothPosition, surface, chartingTreatmentId);
    }

    window.initializeChartedTreatments();
    window.initializeTemplateModal();

    calculateTotalPrice();
    updateTotals();
    changeAppointmentsTotals();
    console.log(`Treatment assigned successfully for tooth: ${toothPosition}`);
    toastr.success("Treatment has been assigned to patient successfully");

    $(`.charted-treatment-container[data-id="${responseId}"]`).find(".tinymce_editor").each(function() {
      console.log(this)

      window.initializeTinyMCE(this);
    })

    // Auto-progression logic
    moveToNextTreatmentInput();
  }

  // Reusable function to handle normal treatment assignment errors
  function handleAssignNormalTreatmentError(error, toothPosition) {
    if (error === 'Forbidden') {
      toastr.error(
        'Cannot edit an appointment on behalf of another dentist. Please assign yourself to this appointment before editing.'
      );
    } else {
      console.error(`Error applying treatment for tooth ${toothPosition}:`, error);
      toastr.error("An error occurred while applying the treatment");
    }
  }

  // Assign a treatment to a single or range of teeth (for bridging or not).
  // This helps remove duplication from bridging logic in normal (non-base) charting.
  function handleRangeOrSingleToothAssignment(
    startIndex,
    endIndex,
    surface,
    multiClick,
    surfaceOrderForThisClick,
    appointmentId,
    teethOrder
  ) {
    // If bridging is enabled
    if (window._isBridgeTreatment) {
      // For bridging, we send a single request with tooth_id containing the range e.g. "UR1 - UR3"
      $.ajax({
        url: `/admin/patients/${window._patientId}/charting/create_treatment`,
        type: 'POST',
        data: {
          multi_click: multiClick,
          surface_order: surfaceOrderForThisClick,
          tooth_id: `${teethOrder[startIndex]} - ${teethOrder[endIndex]}`,
          surface_id: surface,
          pricing_type: $('#treatment-popout .selected').data('plan'),
          appointment_id: appointmentId,
          treatment_id: window._selectedTreatmentId,
          child_tooth: false
        },
        headers: {
          'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
        },
        success: (response) => {
          if (response) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = response;
            const chartedTreatmentContainer = tempDiv.querySelector('.charted-treatment-container');
            const responseId = chartedTreatmentContainer ? chartedTreatmentContainer.getAttribute('data-id') : null;

            // Remove existing container if present, then append new
            $(`.charted-treatment-container[data-id="${responseId}"]`).remove();
            $('.selected > .accordion').append(response);
            $('.selected > .accordion').find('.accordion-body').addClass('selected');

            // ID for referencing appended images
            const chartingTreatmentId = $(response).find('.delete-treatment').data('id');

            // Apply images to each tooth in the bridging range
            for (let i = startIndex; i <= endIndex; i++) {
              let currentTooth = teethOrder[i];
              appendToothImage(currentTooth, surface, chartingTreatmentId);

              // The F tooth
              let fToothPosition = `${currentTooth}F`;
              let fToothDiv = $(`.teethgrid > div.${fToothPosition.toLowerCase()}`);
              if (fToothDiv.length > 0) {
                appendToothImage(fToothPosition, surface, chartingTreatmentId);
              }
            }

            calculateTotalPrice();
            updateTotals();
            changeAppointmentsTotals();
            console.log('Treatment assigned successfully for bridge');
            toastr.success("Treatment has been assigned to patient successfully");

            // Auto-progression logic
            moveToNextTreatmentInput();
          } else {
            toastr.error("Treatment assignment failed");
            console.error('Failed to assign bridge treatment:', response.error);
          }
        },
        error: (xhr, status, error) => {
          handleAssignNormalTreatmentError(error, `${teethOrder[startIndex]} - ${teethOrder[endIndex]}`);
        }
      });
    }
    // If bridging is not enabled, we do separate requests for each tooth in the range
    else {
      for (let i = startIndex; i <= endIndex; i++) {
        let currentTooth = teethOrder[i];
        assignNormalTreatment(
          currentTooth,
          surface,
          multiClick,
          surfaceOrderForThisClick,
          appointmentId,
          (resp) => handleAssignNormalTreatmentSuccess(resp, surface, currentTooth),
          (err) => handleAssignNormalTreatmentError(err, currentTooth)
        );
      }
    }
  }

  // If there's a selected template, we handle it here
  async function loadAppointmentsSequentially(selectedTemplate, selectedTooth, surface, courseOfTreatmentId) {
    for (let i = 0; i < selectedTemplate.length; i++) {
      const appointment = selectedTemplate[i];
      try {
        const response = await $.ajax({
          url: '/admin/charting_appointments',
          type: 'POST',
          dataType: 'json',
          headers: {
            'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
          },
          data: {
            charting_appointment: {
              course_of_treatment_id: courseOfTreatmentId,
              position: i + 1,
              name: appointment.name,
              date: appointment.date
            }
          }
        });

        if (response.error) throw new Error(response.error);

        const temp = document.createElement('div');
        temp.innerHTML = response;
        const appointmentId = response.new_appointment_id;
        const newAppointmentElement = temp.firstElementChild;
        const newApptElement = document.querySelector('#course-of-treatment-container div');
        if (newApptElement) {
          newApptElement.parentNode.insertBefore(newAppointmentElement, newApptElement);
          // Grab the container
          const appointmentContainer = document.querySelector(`.appointment-container[data-appointment-id="${appointmentId}"]`);

          // Assign each treatment to the tooth
          for (let t = 0; t < appointment.treatments.length; t++) {
            const treatment = appointment.treatments[t];
            try {
              const assignResponse = await assignTreatmentToTooth(selectedTooth, surface, appointmentId, treatment.id, treatment.pricing_type);
              console.log('Treatment assigned successfully:', assignResponse);
              // Put the response in the appointment container
              appointmentContainer.querySelector('.accordion').insertAdjacentHTML('beforeend', assignResponse);

              // Then we load all treatments (to ensure images are displayed)
              $.ajax({
                url: '/admin/teeth/load_treatment',
                type: 'POST',
                data: {
                  appointmentId: appointmentId,
                },
                headers: {
                  'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
                },
                success: function (treatmentResponse) {
                  if (treatmentResponse.success) {
                    console.log('Treatments loaded successfully:', treatmentResponse);
                    // Re-apply images
                    treatmentResponse.treatments.forEach(function (tr) {
                      const [toothPos, treatId, srf, isFullTooth, region, isComplete, _, ctid] = tr;
                      const $treatmentElement = $(`.treatment[data-treatment-id="${treatId}"]`);
                      if ($treatmentElement.length > 0) {
                        const folder = ($treatmentElement.data('treatment-folder') || '').trim();
                        const selectedTreat = folder
                          ? folder.toLowerCase()
                          : $treatmentElement.text().trim().toLowerCase();

                        if (region === 'Patient') return;

                        if (toothPos.includes(' - ')) {
                          // Range scenario
                          applyTreatmentForRange(toothPos, selectedTreat, isFullTooth, treatId, isComplete, ctid, srf);
                        } else {
                          // Single tooth
                          applyTreatmentToTooth(toothPos, selectedTreat, isFullTooth, treatId, isComplete, ctid, srf);
                          // Check F position
                          let fPos = `${toothPos}F`;
                          applyTreatmentToTooth(fPos, selectedTreat, isFullTooth, treatId, isComplete, ctid, srf);
                        }
                      }
                    });
                  } else {
                    console.error('Failed to load treatments:', treatmentResponse.error);
                    Swal.fire("Error", "Failed to load treatments", "error");
                  }
                },
                error: function (xhr, status, error) {
                  console.error('Error loading treatments:', error);
                  Swal.fire("Error", "An error occurred while loading treatments", "error");
                },
                complete: function () {
                  $('#overlay').css('display', 'none').removeClass('show');
                }
              });
            } catch (error) {
              console.error('Error assigning treatment:', error);
            }
          }
        }
      } catch (error) {
        console.error('Failed to add appointment:', error);
      }
    }
  }

  // Async function used inside the template logic
  function assignTreatmentToTooth(selectedTooth, surface, appointmentId, treatmentId, pricingType) {
    return new Promise((resolve, reject) => {
      $.ajax({
        url: '/admin/teeth/assign_treatment',
        type: 'POST',
        data: {
          tooth_id: selectedTooth,
          surface_id: surface,
          appointment_id: appointmentId,
          treatment_id: treatmentId,
          pricing_type: pricingType,
          child_tooth: false
        },
        headers: {
          'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
        },
        success: function (response) {
          if (response) {
            resolve(response);
          } else {
            reject(new Error("No response received"));
          }
        },
        error: function (xhr, status, error) {
          reject(error);
        }
      });
    });
  }

  // For each tooth in a range (like bridging), re-apply images from loaded treatments
  function applyTreatmentForRange(toothPositions, selectedTreatment, isFullToothTreatment, treatmentId, isComplete, ctid, surface) {
    const teethOrder = [
      'UR8', 'UR7', 'UR6', 'UR5', 'UR4', 'UR3', 'UR2', 'UR1',
      'UL1', 'UL2', 'UL3', 'UL4', 'UL5', 'UL6', 'UL7', 'UL8',
      'LL8', 'LL7', 'LL6', 'LL5', 'LL4', 'LL3', 'LL2', 'LL1',
      'LR1', 'LR2', 'LR3', 'LR4', 'LR5', 'LR6', 'LR7', 'LR8'
    ];

    const [startTooth, endTooth] = toothPositions.split(' - ').map(t => t.trim());
    let startIndex = teethOrder.indexOf(startTooth);
    let endIndex = teethOrder.indexOf(endTooth);

    if (startIndex === -1 || endIndex === -1) {
      console.error('Invalid tooth positions in bridging range');
      return;
    }

    if (startIndex > endIndex) {
      [startIndex, endIndex] = [endIndex, startIndex];
    }

    for (let i = startIndex; i <= endIndex; i++) {
      const tooth = teethOrder[i];
      applyTreatmentToTooth(tooth, selectedTreatment, isFullToothTreatment, treatmentId, isComplete, ctid, surface);
      const fToothPosition = `${tooth}F`;
      applyTreatmentToTooth(fToothPosition, selectedTreatment, isFullToothTreatment, treatmentId, isComplete, ctid, surface);
    }
  }

  // Re-applies the previously assigned treatments or newly assigned ones to the tooth images
  function applyTreatmentToTooth(toothPosition, selectedTreatment, isFullToothTreatment, treatmentId, isComplete, chartedtreatmentId, surface) {
    const $toothElement = $(`.teethgrid > div.${toothPosition.toLowerCase()}`);
    if (!$toothElement.length) return;

    if (isFullToothTreatment) {
      const imageName = `${selectedTreatment}/${toothPosition}.png`;
      const imageTag = `<img src="https://upodmedican.b-cdn.net/${imageName}" 
        class="img-fluid absolute treatment-id-${treatmentId} tooth-treatment-img ${isComplete ? 'completed-treatment' : ''} ctid-${chartedtreatmentId}" 
        style="left:0; z-index: 9; top:0;">`;

      const existingImage = $toothElement.find(`img[src='https://upodmedican.b-cdn.net/${imageName}'].treatment-id-${treatmentId}`);
      if (existingImage.length === 0 || isComplete) {
        $toothElement.append(imageTag);
      }
    } else {
      // Surface-based
      const imageName = `${selectedTreatment}/${toothPosition}_${surface}.png`;
      const imageTag = `<img src="https://upodmedican.b-cdn.net/${imageName}" 
        class="img-fluid absolute treatment-id-${treatmentId} tooth-treatment-img ${isComplete ? 'completed-treatment' : ''} ctid-${chartedtreatmentId}" 
        style="left:0; z-index: 9; top:0;">`;

      const existingImage = $toothElement.find(`img[src='https://upodmedican.b-cdn.net/${imageName}'].treatment-id-${treatmentId}`);
      if (existingImage.length === 0 || isComplete) {
        $toothElement.append(imageTag);
      }
    }
  }

  /**
   * ---------------------------------------
   * Main Click Handler
   * ---------------------------------------
   */
  $('.teethgrid > div').on('click', '.tooth-svg-overlay path, .tooth-svg-overlay polygon, .tooth-svg-overlay rect', function (e) {
    e.stopPropagation();
    const toothDiv = $(this).closest('.teethgrid > div');
    let toothPosition = toothDiv.attr('class').split(' ')[0].toUpperCase();
    let surface = $(this).data('surface');
    if (surface === 'ignore') return;

    if (window.currentlyEditingTemplateRow) {
      const row = window.currentlyEditingTemplateRow;
      const realAppointmentId = row.data('real-appointment-id');
      const rowTreatmentId    = row.data('treatment-id');
      const rowPricingType    = row.data('pricing-type');

      const toothDiv = $(this).closest('.teethgrid > div');
      const toothPosition = toothDiv.attr('class').split(' ')[0].toUpperCase();
      let surface = $(this).data('surface');
      if (surface === 'ignore') return;
      row.find('.treatment-location-input').val(toothPosition);
      const $treatmentElement = $(`.treatment[data-treatment-id="${rowTreatmentId}"]`);
      if ($treatmentElement.length) {
        selectTreatment($treatmentElement);
      }
      $.ajax({
        url: `/admin/patients/${window._patientId}/charting/create_treatment`,
        type: 'POST',
        data: {
          tooth_id: toothPosition,
          surface_order: 1,
          surface_id: surface,
          appointment_id: realAppointmentId,
          treatment_id: rowTreatmentId,
          pricing_type: rowPricingType,
          child_tooth: false
        },
        headers: { 'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content') },
        success: function (response) {
          if (response) {
            toastr.success("Treatment assigned successfully!");
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = response;
            const chartedTreatmentContainer = tempDiv.querySelector('.charted-treatment-container');
            const responseId = chartedTreatmentContainer ? chartedTreatmentContainer.getAttribute('data-id') : null;

            const $targetAccordion = $(`.appointment-container[data-appointment-id="${realAppointmentId}"] .accordion`);
            if ($targetAccordion.length > 0) {
              $(`.charted-treatment-container[data-id="${responseId}"]`).remove();
              $targetAccordion.append(response);
              $targetAccordion.find('.accordion-body').addClass('selected');

              $(".tinymce_editor").each(function() {
                initializeTinyMCE(this);
              })
              window.initializeChartedTreatments();
              window.initializeTemplateModal();

              const chartingTreatmentId = $(response).find('.delete-treatment').data('id') || responseId;

              if (window._isFullToothTreatment) {
                const imageName = `${window._selectedTreatment}/${toothPosition}.png`;
                const imageTag = `<img src="https://upodmedican.b-cdn.net/${imageName}" class="ctid-${chartingTreatmentId} img-fluid absolute treatment-id-${rowTreatmentId} tooth-treatment-img" style="left:0; z-index: 9; top:0;">`;
                const $toothElement = $(`.teethgrid > div.${toothPosition.toLowerCase()}`);
                if ($toothElement.find(`img[src='https://upodmedican.b-cdn.net/${imageName}'].treatment-id-${rowTreatmentId}`).length === 0) {
                  $toothElement.prepend(imageTag);
                }
              } else {
                let imageName = `${window._selectedTreatment}/${toothPosition}_${surface}.png`;
                const imageTag = `<img src="https://upodmedican.b-cdn.net/${imageName}" class="ctid-${chartingTreatmentId} img-fluid absolute treatment-id-${rowTreatmentId} tooth-treatment-img" style="left:0; z-index: 9; top:0;">`;
                const $toothElement = $(`.teethgrid > div.${toothPosition.toLowerCase()}`);
                if ($toothElement.find(`img[src='https://upodmedican.b-cdn.net/${imageName}'].treatment-id-${rowTreatmentId}`).length === 0) {
                  $toothElement.prepend(imageTag);
                }
              }

              calculateTotalPrice();
              updateTotals();
              changeAppointmentsTotals();
              moveToNextTreatmentInput();
            } else {
              toastr.error("Failed to create charted treatment");
              console.error("Server response:", response);
            }
          } else {
            toastr.error("Failed to create charted treatment");
            console.error("Server response:", response);
          }
        },
        error: function (xhr, status, error) {
          toastr.error("Error creating charted treatment");
          console.error("assign_treatment error:", error);
        },
        complete: function() {
          // Only clear if auto-progression didn't set a new row for editing
          if (!window.currentlyEditingTemplateRow) {
            $(".treatment-location-input").removeClass("active-input");
          }
        }
      });

      return;
    }

    if (window._isEraserActive) {
      removeTreatmentFromToothSurface(toothPosition, surface);
      return;
    }

    // If there's a patient-based treatment
    if (window._selectedTreatment !== '') {
      const $treatment = $(`.treatment[data-treatment-id="${window._selectedTreatmentId}"]`);
      const region = $treatment.data('region');
      if (region === 'Patient') {
        applyPatientTreatment($treatment);
        return;
      }
    }

    // If window._isMissingTooth is true
    if (window._isMissingTooth) {
      applyConditionToTooth(toothPosition, 'missing', false);
      // Re-extract everything (duplicated in original, but refactored to avoid overhead)
      let multiClick = false;
      let surfaceOrderForThisClick = 1;
      if (window._isCKeyPressed && surface) {
        multiClick = true;
        if (surfaceOrder[toothPosition]) {
          surfaceOrder[toothPosition] += 1;
        } else {
          surfaceOrder[toothPosition] = 1;
        }
        surfaceOrderForThisClick = surfaceOrder[toothPosition];
      }
      // Save base charting
      saveBaseTreatment(
        toothPosition,
        surface,
        multiClick,
        surfaceOrderForThisClick,
        (response) => {
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = response;
          const chartedTreatmentContainer = tempDiv.querySelector('.charted-treatment-container');
          if (chartedTreatmentContainer) {
            const responseId = chartedTreatmentContainer.getAttribute('data-id');
            const removeTreatmentWhenCompleted = chartedTreatmentContainer.getAttribute('data-remove-treatment-when-completed');
            const removeToothWhenCompleted = chartedTreatmentContainer.getAttribute('data-remove-tooth-when-completed');
            const isMissingTooth = chartedTreatmentContainer.getAttribute('data-missing-tooth');

            if (isMissingTooth !== 'true') {
              applyBaseTreatmentToTooth(
                toothPosition,
                window._selectedTreatment,
                window._isFullToothTreatment,
                window._selectedTreatmentId,
                responseId,
                removeTreatmentWhenCompleted,
                removeToothWhenCompleted,
                surface
              );
            }

            $(`.charted-treatment-container[data-id="${responseId}"]`).remove();
            $('#course-of-treatment-container > .appointment-container').prepend(response);
            $('.base-treatments-content > .appointment-container').prepend(response);
            console.log(`Base treatment assigned successfully for tooth ${toothPosition}`);
          }
        },
        (error) => console.error(`Error saving missing tooth treatment: ${error}`)
      );
      return;
    }

    // Handle base charting mode
    console.log(window.isBaseChartingMode)

    if (window.isBaseChartingMode && window._selectedTreatment !== '') {

      console.log(window.isBaseChartingMode)
      let multiClick = false;
      let surfaceOrderForThisClick = 1;
      if (window._isCKeyPressed && surface) {
        multiClick = true;
        if (surfaceOrder[toothPosition]) {
          surfaceOrder[toothPosition] += 1;
        } else {
          surfaceOrder[toothPosition] = 1;
        }
        surfaceOrderForThisClick = surfaceOrder[toothPosition];
      }

      // Save base treatment (main tooth)
      saveBaseTreatment(
        toothPosition,
        surface,
        multiClick,
        surfaceOrderForThisClick,
        (response) => {
          // On success
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = response;
          const chartedTreatmentContainer = tempDiv.querySelector('.charted-treatment-container');
          if (chartedTreatmentContainer) {
            const responseId = chartedTreatmentContainer.getAttribute('data-id');
            const removeTreatmentWhenCompleted = chartedTreatmentContainer.getAttribute('data-remove-treatment-when-completed');
            const removeToothWhenCompleted = chartedTreatmentContainer.getAttribute('data-remove-tooth-when-completed');
            const isMissingTooth = chartedTreatmentContainer.getAttribute('data-missing-tooth');

            if (isMissingTooth !== 'true') {
              applyBaseTreatmentToTooth(
                toothPosition,
                window._selectedTreatment,
                window._isFullToothTreatment,
                window._selectedTreatmentId,
                responseId,
                removeTreatmentWhenCompleted,
                removeToothWhenCompleted,
                surface
              );
            }

            $(`.charted-treatment-container[data-id="${responseId}"]`).remove();
            $('#course-of-treatment-container > .appointment-container').prepend(response);
            $('.base-treatments-content > .appointment-container').prepend(response);
            console.log(`Base treatment assigned successfully for tooth ${toothPosition}`);
          }
        },
        (error) => {
          console.error(`Failed to assign base treatment for tooth ${toothPosition}:`, error);
        }
      );

      toastr.success('Base treatment assigned and completed');
      return;
    }


    if ($(".treatment-plan-details").length) return;
    // Handle normal charting mode
    // Must ensure an appointment is selected, a treatment is selected, etc.
    if ($('.appointment-container.selected').length > 0 && window._selectedTreatment !== '') {
      let multiClick = false;
      let surfaceOrderForThisClick = 1;

      // if control key was pressed
      if (e.ctrlKey || e.metaKey || window._isCKeyPressed) {
        multiClick = true;
        if (surface) {
          if (surfaceOrder[toothPosition]) {
            surfaceOrder[toothPosition] += 1;
          } else {
            surfaceOrder[toothPosition] = 1;
          }
          surfaceOrderForThisClick = surfaceOrder[toothPosition];
        }
      }

      // Check for range selection
      if (window._isRangeSelectionActive === true) {
        if (!window._rangeStartTooth) {
          window._rangeStartTooth = toothPosition;
        } else {
          const teethOrder = [
            'UR8', 'UR7', 'UR6', 'UR5', 'UR4', 'UR3', 'UR2', 'UR1',
            'UL1', 'UL2', 'UL3', 'UL4', 'UL5', 'UL6', 'UL7', 'UL8',
            'LL8', 'LL7', 'LL6', 'LL5', 'LL4', 'LL3', 'LL2', 'LL1',
            'LR1', 'LR2', 'LR3', 'LR4', 'LR5', 'LR6', 'LR7', 'LR8'
          ];
          let startIndex = teethOrder.indexOf(window._rangeStartTooth);
          let endIndex = teethOrder.indexOf(toothPosition);
          if (startIndex > endIndex) {
            [startIndex, endIndex] = [endIndex, startIndex];
          }

          // Bridge or normal
          handleRangeOrSingleToothAssignment(
            startIndex,
            endIndex,
            surface,
            multiClick,
            surfaceOrderForThisClick,
            $('.appointment-container.selected').data('appointment-id'),
            teethOrder
          );

          window._rangeStartTooth = null; // reset for next range
        }
      } else {
        // Single tooth logic
        assignNormalTreatment(
          toothPosition,
          surface,
          multiClick,
          surfaceOrderForThisClick,
          $('.appointment-container.selected').data('appointment-id'),
          (resp) => handleAssignNormalTreatmentSuccess(resp, surface, toothPosition),
          (err) => handleAssignNormalTreatmentError(err, toothPosition)
        );
      }

    }
    // If there's a selected template scenario
    else if (window._selectedTemplate) {
      // We have a template with multiple appointments and treatments
      const selectedTooth = toothPosition;
      const courseOfTreatmentId = $(".course-of-treatment-item.sleected-cot").data('course-of-treatment-id');
      loadAppointmentsSequentially(selectedTemplate, selectedTooth, surface, courseOfTreatmentId);

    }
    // If there's a single selectedTemplateTreatment in a global var
    else if (window.selectedTemplateTreatment) {
      const selectedTooth = toothPosition;
      const surfaceVal = surface || 'full_tooth';
      const { appointmentId, treatmentId, pricingType, inputElement } = window.selectedTemplateTreatment;

      // Reuse existing function for normal assignment
      assignTreatmentToTooth(selectedTooth, surfaceVal, appointmentId, treatmentId, pricingType)
        .then(response => {
          console.log('Treatment assigned successfully:', response);
          // Place the response in the appointment container
          const appointmentContainer = $(`.appointment-container[data-appointment-id="${appointmentId}"]`);
          appointmentContainer.find('.accordion').append(response);

          $(".tinymce_editor").each(function() {
            initializeTinyMCE(this);
          })
          window.initializeChartedTreatments();
          window.initializeTemplateModal();

          // Apply the treatment visually
          function applyTreatmentToSelectedTooth(pos, tid, surf) {
            const $treatmentElement = $(`.treatment[data-treatment-id="${tid}"]`);
            if (!$treatmentElement.length) return;

            const region = $treatmentElement.data('region');
            if (region === 'Patient') return;

            let folder = ($treatmentElement.data('treatment-folder') || '').trim();
            let localSelectedTreatment = folder
              ? folder.toLowerCase()
              : $treatmentElement.text().trim().toLowerCase().replace(/\s+/g, '_');

            let $toothElement = $(`.teethgrid > div.${pos.toLowerCase()}`);
            if (!$toothElement.length) return;

            let imageName;
            if (surf === 'full_tooth') {
              imageName = `${localSelectedTreatment}/${pos}.png`;
            } else {
              imageName = `${localSelectedTreatment}/${pos}_${surf}.png`;
            }
            const finalUrl = `https://yourcdn.com/${imageName}`;
            const imageTag = `<img src="${finalUrl}" 
              class="img-fluid absolute treatment-id-${tid} tooth-treatment-img" 
              style="left:0; z-index: 9; top:0;">`;

            // Prevent duplicates
            if ($toothElement.find(`img[src='${finalUrl}'].treatment-id-${tid}`).length === 0) {
              $toothElement.prepend(imageTag);
            }
          }

          applyTreatmentToSelectedTooth(selectedTooth, treatmentId, surfaceVal);

          // Update the input element with the tooth position
          inputElement.val(selectedTooth);

          // Clear the selection
          window.selectedTemplateTreatment = null;
          $('.position-input').removeClass('selected');
        })
        .catch(error => {
          console.error('Error assigning treatment:', error);
          Swal.fire("Error", "Failed to assign treatment", "error");
        });

    }
    // If none of the conditions are met
    else {
      if ($('#treatment-popout .selected').length === 0) {
        Swal.fire("Error", "Please select a Course of Treatment", "error");
      } else {
        Swal.fire("Error", "Please select an Appointment and Treatment to start charting", "error");
      }
    }
  });
});

// Reusable function for auto-progression to next treatment input
function moveToNextTreatmentInput() {
  // Find and focus next empty treatment location input
  const nextEmptyInput = $(".treatment-location-input").filter(function() {
    return $(this).val() === "";
  }).first();

  if (nextEmptyInput.length > 0) {
    // Remove current focus styles from all inputs
    $(".treatment-location-input").removeClass("ring-2 ring-blue-500 border-blue-500");

    // Add focus styles to next input
    nextEmptyInput.addClass("ring-2 ring-blue-500 border-blue-500");

    // Auto-scroll to the selected input within the template form
    const templateForm = $("#template-form");
    if (templateForm.length > 0 && templateForm.is(":visible")) {
      const inputOffset = nextEmptyInput.offset();
      const formOffset = templateForm.offset();
      const formScrollTop = templateForm.scrollTop();
      const formHeight = templateForm.height();

      // Calculate the position of the input relative to the form
      const relativeTop = inputOffset.top - formOffset.top + formScrollTop;

      // Calculate the ideal scroll position to center the input in the form
      const idealScrollTop = relativeTop - (formHeight / 2);

      // Smoothly scroll to the input position
      templateForm.animate({
        scrollTop: Math.max(0, idealScrollTop)
      }, 300);
    }

    // Set up the next input for editing
    const nextRow = nextEmptyInput.closest("[data-treatment-id]");
    if (nextRow.length) {
      const nextRowTreatmentId = nextRow.data("treatment-id");
      const nextAppointmentId = nextRow.data("real-appointment-id");
      const $nextTreatment = $(`.treatment[data-treatment-id="${nextRowTreatmentId}"]`);

      // Make the corresponding appointment active
      $('.appointment-container').removeClass('selected');
      $('.appointment-toolbar').removeClass('selected');
      $('.accordion-body').removeClass('selected');

      const $nextAppointmentContainer = $(`.appointment-container[data-appointment-id="${nextAppointmentId}"]`);
      if ($nextAppointmentContainer.length) {
        $nextAppointmentContainer.addClass('selected');
        $nextAppointmentContainer.parent().find('.appointment-toolbar').addClass('selected');
        $nextAppointmentContainer.find('.accordion-body').addClass('selected');
      }

      if ($nextTreatment.length) {
        selectTreatment($nextTreatment);
      }

      window.currentlyEditingTemplateRow = nextEmptyInput.closest(".template-treatment-row");

      return true; // Successfully moved to next input
    }
  }

  return false; // No more inputs to move to
}
