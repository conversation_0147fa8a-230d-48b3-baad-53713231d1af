$(document).ready(function() {
  if (!$('[data-terminal-payment-btn]').length) {
    return;
  }

  let currentPaymentIntentId = null;
  let terminalPaymentModal = null;

  function initializeTerminalPayment() {
    const $terminalForm = $('#payment-form-terminal');
    const $terminalBtn = $('[data-terminal-payment-btn]');
    const $terminalSelect = $('[data-terminal-select]');

    $terminalForm.on('submit', function(e) {
      e.preventDefault();

      const terminalId = $terminalSelect.val();
      if (!terminalId) {
        if (typeof toastr !== 'undefined') {
          toastr.error('Please select a terminal');
        }
        return;
      }

      const amount = parseFloat($terminalForm.find('[name="payment[amount]"]').val());
      if (!amount || amount <= 0) {
        if (typeof toastr !== 'undefined') {
          toastr.error('Please enter a valid amount');
        }
        return;
      }

      sendPaymentToTerminal($terminalForm);
    });

    $terminalBtn.on('click', function() {
      $terminalForm.trigger('submit');
    });
  }

  function sendPaymentToTerminal($form) {
    const formData = new FormData($form[0]);
    const patientId = window._patientId;

    if (!patientId) {
      if (typeof toastr !== 'undefined') {
        toastr.error('Patient ID not found');
      }
      return;
    }

    const selectedInvoiceId = $('.tab-pane.active').find('select[name="pay_invoice"]').val();
    if (selectedInvoiceId && selectedInvoiceId !== '') {
      $form.find('.invoice-id-field').val(selectedInvoiceId);
      formData.set('payment[invoice_id]', selectedInvoiceId);
    }

    const $submitBtn = $form.find('[data-terminal-payment-btn]');
    const originalText = $submitBtn.text();
    $submitBtn.prop('disabled', true).text('Processing...');

    $.ajax({
      url: `/admin/patients/${patientId}/payments/send_to_terminal`,
      type: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        if (response.success) {
          currentPaymentIntentId = response.stripe_payment_intent_id;
          showTerminalModal('Payment Sent To Terminal', response.payment_id);
          setupPusherListener(response.payment_id);
        } else {
          if (typeof toastr !== 'undefined') {
            toastr.error(response.error || 'Failed to send payment to terminal');
          }
        }
      },
      error: function(xhr) {
        let errorMessage = 'An error occurred while sending payment to terminal';
        if (xhr.responseJSON && xhr.responseJSON.error) {
          errorMessage = xhr.responseJSON.error;
        }
        if (typeof toastr !== 'undefined') {
          toastr.error(errorMessage);
        }
      },
      complete: function() {
        $submitBtn.prop('disabled', false).text(originalText);
      }
    });
  }

  function showTerminalModal(title, paymentId) {
    const modalHtml = `
      <div id="terminal-payment-modal" class="fixed inset-0 z-50 flex items-center justify-center p-4" style="background-color: rgba(0, 0, 0, 0.5)">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
          <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
              </svg>
            </div>
            
            <h3 class="text-lg font-semibold text-gray-900 text-center mb-2" data-modal-title>
              ${title}
            </h3>
            
            <p class="text-sm text-gray-600 text-center mb-6" data-modal-message>
              Please present the card to the terminal to complete the payment.
            </p>
            
            <div class="flex gap-3">
              <button type="button" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium transition-colors" data-cancel-terminal>
                Cancel Payment
              </button>
              <button type="button" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors hidden" data-close-modal>
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    $('body').append(modalHtml);
    terminalPaymentModal = $('#terminal-payment-modal');

    terminalPaymentModal.find('[data-cancel-terminal]').on('click', function() {
      cancelTerminalPayment();
    });

    terminalPaymentModal.find('[data-close-modal]').on('click', function() {
      closeTerminalModal();
    });

    $(document).on('keydown.terminalModal', function(e) {
      if (e.key === 'Escape') {
        if (terminalPaymentModal.find('[data-close-modal]').is(':visible')) {
          closeTerminalModal();
        }
      }
    });
  }

  function cancelTerminalPayment() {
    if (!currentPaymentIntentId) {
      closeTerminalModal();
      return;
    }

    const patientId = window._patientId;
    if (!patientId) {
      if (typeof toastr !== 'undefined') {
        toastr.error('Patient ID not found');
      }
      return;
    }

    const $cancelBtn = terminalPaymentModal.find('[data-cancel-terminal]');
    const originalText = $cancelBtn.text();
    $cancelBtn.prop('disabled', true).text('Cancelling...');

    $.ajax({
      url: `/admin/patients/${patientId}/payments/cancel_terminal`,
      type: 'POST',
      data: {
        payment_intent_id: currentPaymentIntentId
      },
      headers: {
        'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        if (response.success) {
          if (typeof toastr !== 'undefined') {
            toastr.success(response.message || 'Payment cancelled successfully');
          }
          closeTerminalModal();
        } else {
          if (typeof toastr !== 'undefined') {
            toastr.error(response.error || 'Failed to cancel payment');
          }
        }
      },
      error: function(xhr) {
        let errorMessage = 'An error occurred while cancelling payment';
        if (xhr.responseJSON && xhr.responseJSON.error) {
          errorMessage = xhr.responseJSON.error;
        }
        if (typeof toastr !== 'undefined') {
          toastr.error(errorMessage);
        }
      },
      complete: function() {
        $cancelBtn.prop('disabled', false).text(originalText);
      }
    });
  }

  function updateModalForSuccess() {
    if (!terminalPaymentModal) return;

    const $icon = terminalPaymentModal.find('svg');
    const $iconContainer = $icon.closest('div');
    const $title = terminalPaymentModal.find('[data-modal-title]');
    const $message = terminalPaymentModal.find('[data-modal-message]');
    const $cancelBtn = terminalPaymentModal.find('[data-cancel-terminal]');
    const $closeBtn = terminalPaymentModal.find('[data-close-modal]');

    $iconContainer.removeClass('bg-blue-100').addClass('bg-green-100');
    $icon.removeClass('text-blue-600').addClass('text-green-600');
    $icon.html(`
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
    `);

    $title.text('Payment Successful');
    $message.text('The payment has been processed successfully.');

    $cancelBtn.addClass('hidden');
    $closeBtn.removeClass('hidden');

    if (typeof toastr !== 'undefined') {
      toastr.success('Payment completed successfully');
    }
  }

  function updateModalForFailure() {
    if (!terminalPaymentModal) return;

    const $icon = terminalPaymentModal.find('svg');
    const $iconContainer = $icon.closest('div');
    const $title = terminalPaymentModal.find('[data-modal-title]');
    const $message = terminalPaymentModal.find('[data-modal-message]');
    const $cancelBtn = terminalPaymentModal.find('[data-cancel-terminal]');
    const $closeBtn = terminalPaymentModal.find('[data-close-modal]');

    $iconContainer.removeClass('bg-blue-100').addClass('bg-red-100');
    $icon.removeClass('text-blue-600').addClass('text-red-600');
    $icon.html(`
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
    `);

    $title.text('Payment Failed');
    $message.text('The payment could not be processed. Please try again.');

    $cancelBtn.addClass('hidden');
    $closeBtn.removeClass('hidden');

    if (typeof toastr !== 'undefined') {
      toastr.error('Payment failed');
    }
  }

  function setupPusherListener(paymentId) {
    if (typeof window.usePusher !== 'function') {
      console.log('Pusher not available');
      return;
    }

    const pusher = window.usePusher();
    const channel = pusher.subscribe(`private-payment-${paymentId}`);

    channel.bind('payment_succeeded', function(data) {
      updateModalForSuccess();
    });

    channel.bind('payment_failed', function(data) {
      updateModalForFailure();
    });
  }

  function closeTerminalModal() {
    if (terminalPaymentModal) {
      $(document).off('keydown.terminalModal');
      terminalPaymentModal.remove();
      terminalPaymentModal = null;
      currentPaymentIntentId = null;

      setTimeout(function() {
        window.location.reload();
      }, 1000);
    }
  }

  initializeTerminalPayment();
});
