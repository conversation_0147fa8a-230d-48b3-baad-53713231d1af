// Patient Notes Management - Refactored
$(document).ready(function() {
  const PatientNotes = {
    // Configuration
    config: {
      selectors: {
        toggleButton: '#toggle-archived-notes',
        notesContainer: '#patient-notes-container',
        archiveLinks: 'a[href*="/patient_notes/"][href*="/archive"]',
        pinLinks: 'a[href*="/patient_notes/"][href*="/pin"]',
        dropdownContainer: '.notes-dropdown-container',
        dropdownMenu: '.notes-dropdown-menu',
        countBadge: '.notes-dropdown-trigger .flex.items-center.justify-center'
      },
      colors: {
        yellow: { rgb: '251, 191, 36', tailwind: 'yellow' },
        amber: { rgb: '251, 191, 36', tailwind: 'yellow' },
        blue: { rgb: '59, 130, 246', tailwind: 'blue' },
        green: { rgb: '34, 197, 94', tailwind: 'green' },
        pink: { rgb: '244, 114, 182', tailwind: 'pink' }
      },
      defaultColor: 'blue'
    },

    // Utility functions
    utils: {
      getPatientId: () => window.location.pathname.split('/').pop(),

      getCsrfToken: () => $('meta[name="csrf-token"]').attr('content'),

      formatDate: (dateString) => new Date(dateString)
        .toLocaleDateString('en-GB', {
          day: '2-digit', month: '2-digit', year: 'numeric',
          hour: '2-digit', minute: '2-digit', hour12: false
        }).replace(',', ''),

      getTimeAgo: (date) => {
        const diffInSeconds = Math.floor((new Date() - date) / 1000);
        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours`;
        if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days`;
        if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months`;
        return `${Math.floor(diffInSeconds / 31536000)} years`;
      },

      getColorConfig: (color) => {
        const config = PatientNotes.config.colors[color] || PatientNotes.config.colors[PatientNotes.config.defaultColor];
        return { ...config, name: color || PatientNotes.config.defaultColor };
      },

      showToast: (message, type = 'success') => {
        if (typeof toastr !== 'undefined') {
          toastr[type](message);
        }
      },

      showLoading: (container) => {
        container.html('<div class="flex justify-center py-8"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div></div>');
      },

      showEmptyState: (container, message) => {
        container.html(`<div class="text-center py-8 text-gray-500">${message}</div>`);
      },

      showError: (container, message = 'Error loading notes. Please try again.') => {
        container.html(`<div class="text-center py-8 text-red-500">${message}</div>`);
      }
    },

    // Template generators
    templates: {
      noteActions: (note) => {
        const actions = [];
        if (note.archived) {
          actions.push(`<a href="/admin/patient_notes/${note.id}/archive" class="flex items-center px-3 py-1.5 text-[13px] text-red-600 hover:bg-gray-100 w-full text-left">Unarchive note</a>`);
        } else {
          actions.push(
            `<a href="/admin/patient_notes/${note.id}/pin" class="flex items-center px-3 py-1.5 text-[13px] text-gray-700 hover:bg-gray-100 w-full text-left" data-remote="true" data-method="patch">${note.pinned ? 'Unpin note' : 'Pin note'}</a>`,
            `<button type="button" class="flex items-center px-3 py-1.5 text-[13px] text-gray-700 hover:bg-gray-100 w-full text-left edit-note-btn" data-note-id="${note.id}" onclick="openEditNoteModal(${note.id})">Edit note</button>`,
            `<button type="button" class="flex items-center px-3 py-1.5 text-[13px] text-gray-700 hover:bg-gray-100 w-full text-left update-note-btn" data-note-id="${note.root_id || note.id}" onclick="openUpdateNoteModal(${note.root_id || note.id})">Update note</button>`,
            `<a href="/admin/patient_notes/${note.id}/archive" class="flex items-center px-3 py-1.5 text-[13px] text-red-600 hover:bg-gray-100 w-full text-left">Archive note</a>`
          );
        }
        return actions.join('');
      },

      colorPicker: (note, color) => `
        <div class="note-color-picker absolute right-0 top-full mt-1 p-2 bg-white rounded-md shadow-lg z-10 hidden flex-wrap gap-2" style="width: 100px;">
          ${Object.keys(PatientNotes.config.colors).map(colorName => 
            `<button type="button" class="note-color-option h-6 w-6 rounded-full bg-${colorName}-200 hover:ring-2 hover:ring-${colorName}-400 hover:ring-offset-2" data-color="${colorName}" data-note-id="${note.id}"></button>`
          ).join('')}
        </div>
      `,

      noteCard: (note) => {
        const color = PatientNotes.utils.getColorConfig(note.color);
        const createdAt = PatientNotes.utils.formatDate(note.created_at);
        const rootId = note.root_id || note.id;

        return `
          <div class="note-${rootId} bg-${color.name}-100 rounded-lg p-4 relative border-0 shadow-lg hover:scale-[1.01] transition-transform duration-200" 
               data-note-id="${note.id}" 
               style="box-shadow: rgba(${color.rgb}, 0.1) 0px 10px 15px -3px, rgba(${color.rgb}, 0.2) 0px 4px 6px -4px;">
            <div class="absolute right-0 top-0 w-8 h-8 bg-${color.name}-200/50 rounded-bl-xl" style="clip-path: polygon(100% 0px, 0px 0px, 100% 100%);"></div>
            
            <div class="flex justify-between items-start mb-2">
              <span class="text-[11px] px-2 py-0.5 bg-${color.name}-200/80 rounded-full text-${color.name}-800 font-medium">${note.user_name || 'Admin User'}</span>
              <div class="flex gap-1">
                ${PatientNotes.templates.menuButton(note, color.name)}
                ${PatientNotes.templates.colorPickerButton(note, color.name)}
              </div>
            </div>
            
            <h4 class="font-medium text-[14px] text-${color.name}-900">${note.title || ''}</h4>
            <p class="text-[11px] text-${color.name}-700/70 mb-2">${createdAt}</p>
            <p class="text-[13px] text-${color.name}-800">${note.text || ''}</p>
            
            ${PatientNotes.templates.childNotesHistory(note, color.name)}
          </div>
        `;
      },

      menuButton: (note, colorName) => `
        <div class="relative">
          <button class="note-menu-button inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-6 w-6 text-${colorName}-600 hover:text-${colorName}-800 hover:bg-${colorName}-200/50" data-note-id="${note.id}">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis h-3.5 w-3.5">
              <circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle>
            </svg>
          </button>
          <div class="note-menu absolute right-0 z-50 hidden w-36 mt-1 py-1 bg-white rounded-md shadow-lg" data-note-id="${note.id}">
            ${PatientNotes.templates.noteActions(note)}
          </div>
        </div>
      `,

      colorPickerButton: (note, colorName) => `
        <div class="relative">
          <button class="note-color-picker-btn inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-6 w-6 text-${colorName}-600 hover:text-${colorName}-800 hover:bg-${colorName}-200/50" data-note-id="${note.id}">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-droplet h-3.5 w-3.5">
              <path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"></path>
            </svg>
          </button>
          ${PatientNotes.templates.colorPicker(note, colorName)}
        </div>
      `,

      childNotesHistory: (note, colorName) => {
        if (!note.has_updates || !note.child_notes || note.child_notes.length === 0) return '';

        const rootId = note.root_id || note.id;
        let html = `<div class="note-history mt-4 space-y-3" data-note-id="${rootId}">`;

        // Previous updates
        if (note.child_notes.length > 1) {
          const previousUpdates = note.child_notes.slice(1);
          previousUpdates.forEach((updateNote, index) => {
            html += PatientNotes.templates.historyItem(updateNote, colorName, index > 0);
          });
        }

        // Original note
        if (note.original_note) {
          const shouldHide = note.child_notes.length > 1;
          html += PatientNotes.templates.historyItem(note.original_note, colorName, shouldHide, true);
        }

        // Toggle button
        if (note.child_notes.length > 1) {
          html += PatientNotes.templates.historyToggle(rootId, colorName);
        }

        return html + '</div>';
      },

      historyItem: (note, colorName, hidden = false, isOriginal = false) => {
        const createdAt = PatientNotes.utils.formatDate(note.created_at);
        const label = isOriginal ? ' (Original)' : '';

        return `
          <div class="bg-white/60 rounded-lg p-3 border-l-2 border-${colorName}-300" style="${hidden ? 'display: none;' : ''}">
            <div class="flex justify-between items-start mb-1">
              <span class="text-[10px] px-2 py-0.5 bg-${colorName}-200/60 rounded-full text-${colorName}-800 font-medium">${note.user_name}</span>
              <span class="text-[10px] text-${colorName}-600">${createdAt}${label}</span>
            </div>
            ${note.title ? `<h5 class="font-medium text-[12px] text-${colorName}-800 mb-1">${note.title}</h5>` : ''}
            <p class="text-[12px] text-${colorName}-700">${note.text || ''}</p>
          </div>
        `;
      },

      historyToggle: (rootId, colorName) => `
        <button class="text-[11px] text-${colorName}-600 hover:text-${colorName}-800 font-medium flex items-center gap-1" onclick="toggleChildNotes(${rootId});">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down" id="collapse-icon-${rootId}">
            <path d="m6 9 6 6 6-6"></path>
          </svg>
          <span id="collapse-text-${rootId}">Show Full History</span>
        </button>
      `,

      pinnedNote: (note) => {
        const color = PatientNotes.utils.getColorConfig(note.color);
        const timeAgo = PatientNotes.utils.getTimeAgo(new Date(note.created_at));
        const rootId = note.root_id || note.id;

        // Generate child notes HTML if there are updates
        let historyHtml = '';
        if (note.has_updates && note.child_notes && note.child_notes.length > 0) {
          const childNotesHtml = note.child_notes.map((childNote, index) => {
            const displayStyle = index === 0 ? 'display: block;' : 'display: none;';
            return `
              <div class="bg-white/60 rounded-lg p-2 border-l-2 border-${color.name}-300" style="${displayStyle}">
                <div class="flex justify-between items-start mb-1">
                  <span class="text-[9px] px-1.5 py-0.5 bg-${color.name}-200/60 rounded-full text-${color.name}-800 font-medium">${childNote.user_name}</span>
                  <span class="text-[9px] text-${color.name}-600">${PatientNotes.utils.formatDate(childNote.created_at)}</span>
                </div>
                ${childNote.title ? `<h5 class="font-medium text-[11px] text-${color.name}-800 mb-1">${childNote.title}</h5>` : ''}
                <p class="text-[11px] text-${color.name}-700">${childNote.text}</p>
              </div>
            `;
          }).join('');

          const originalNoteHtml = note.original_note ? `
            <div class="bg-white/60 rounded-lg p-2 border-l-2 border-${color.name}-300" style="display: none;">
              <div class="flex justify-between items-start mb-1">
                <span class="text-[9px] px-1.5 py-0.5 bg-${color.name}-200/60 rounded-full text-${color.name}-800 font-medium">${note.original_note.user_name}</span>
                <span class="text-[9px] text-${color.name}-600">${PatientNotes.utils.formatDate(note.original_note.created_at)} (Original)</span>
              </div>
              ${note.original_note.title ? `<h5 class="font-medium text-[11px] text-${color.name}-800 mb-1">${note.original_note.title}</h5>` : ''}
              <p class="text-[11px] text-${color.name}-700">${note.original_note.text}</p>
            </div>
          ` : '';

          const toggleButtonHtml = note.child_notes.length > 0 ? `
            <button class="text-[10px] text-${color.name}-600 hover:text-${color.name}-800 font-medium flex items-center gap-1" onclick="toggleChildNotes(${rootId})">
              <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down" id="collapse-icon-${rootId}">
                <path d="m6 9 6 6 6-6"></path>
              </svg>
              <span id="collapse-text-${rootId}">Show Full History</span>
            </button>
          ` : '';

          historyHtml = `
            <div class="note-history mt-3 space-y-2" data-note-id="${rootId}">
              ${childNotesHtml}
              ${originalNoteHtml}
              ${toggleButtonHtml}
            </div>
          `;
        }

        return `
          <div class="note-${rootId} bg-${color.name}-100 rounded-lg p-3 mb-2 relative border-0 shadow-sm hover:scale-[1.01] transition-transform duration-200"
               data-note-id="${note.id}"
               style="box-shadow: rgba(${color.rgb}, 0.1) 0px 10px 15px -3px, rgba(${color.rgb}, 0.2) 0px 4px 6px -4px;">
            <div class="absolute right-0 top-0 w-6 h-6 bg-${color.name}-200/50 rounded-bl-xl" style="clip-path: polygon(100% 0px, 0px 0px, 100% 100%);"></div>

            <!-- Main note content -->
            <div class="font-medium text-[14px] text-${color.name}-900 mb-1">${note.title || 'Note'}</div>
            <div class="text-[11px] text-${color.name}-700/70 mb-2">${PatientNotes.utils.formatDate(note.created_at)}</div>
            <div class="text-[13px] text-${color.name}-800 mb-2">${note.text || ''}</div>

            ${historyHtml}

            <div class="mt-2 text-[10px] text-${color.name}-700/70">Pinned ${timeAgo} ago</div>
          </div>
        `;
      }
    },

    // API functions
    api: {
      loadNotes: (archived = false) => {
        const patientId = PatientNotes.utils.getPatientId();
        return $.ajax({
          url: `/admin/patients/${patientId}/notes`,
          method: 'GET',
          data: { archived },
          dataType: 'json'
        });
      },

      toggleArchive: (url) => {
        return $.ajax({
          url,
          method: 'PATCH',
          data: { authenticity_token: PatientNotes.utils.getCsrfToken() },
          dataType: 'json'
        });
      },

      togglePin: (url) => {
        return $.ajax({
          url,
          method: 'PATCH',
          dataType: 'json',
          headers: { 'X-CSRF-Token': PatientNotes.utils.getCsrfToken() }
        });
      },

      loadPinnedNotes: () => {
        const patientId = PatientNotes.utils.getPatientId();
        return $.ajax({
          url: `/admin/patients/${patientId}/notes`,
          method: 'GET',
          dataType: 'json',
          data: { pinned_only: true },
          headers: { 'X-CSRF-Token': PatientNotes.utils.getCsrfToken() }
        });
      }
    },

    // UI update functions
    ui: {
      updateToggleButton: ($button, newState) => {
        const classes = {
          true: { add: 'bg-green-100 text-green-600 border-green-200', remove: 'bg-red-100 text-red-600 border-red-200', title: 'Show active notes' },
          false: { add: 'bg-red-100 text-red-600 border-red-200', remove: 'bg-green-100 text-green-600 border-green-200', title: 'Show archived notes' }
        };
        const config = classes[newState];
        $button.addClass(config.add).removeClass(config.remove).attr('title', config.title);
      },

      renderNotes: (notes, container) => {
        container.empty();
        notes.forEach(note => {
          container.append(PatientNotes.templates.noteCard(note));
        });

        // Re-initialize components
        ['initializeNoteMenus', 'initializeNoteHistoryCollapse'].forEach(funcName => {
          if (typeof window[funcName] === 'function') window[funcName]();
        });

        if (typeof window.toggleChildNotes !== 'function' && typeof toggleChildNotes === 'function') {
          window.toggleChildNotes = toggleChildNotes;
        }
      },

      updatePinnedNotes: (notes) => {
        const $dropdownMenu = $(PatientNotes.config.selectors.dropdownMenu);
        const $countBadge = $(PatientNotes.config.selectors.countBadge);

        $countBadge.text(notes.length);
        $dropdownMenu.find('[data-note-id], .text-center.text-gray-400').remove();

        const $muteContainer = $dropdownMenu.find('.mute-notes-container');
        if (notes.length > 0) {
          notes.forEach(note => {
            $muteContainer.after(PatientNotes.templates.pinnedNote(note));
          });
        } else {
          $muteContainer.after('<div class="p-4 text-center text-gray-400 text-xs">No pinned notes</div>');
        }
      }
    },

    // Event handlers
    handlers: {
      toggleArchived: function(e) {
        const $button = $(this);
        const showingArchived = $button.data('showing-archived') || false;
        const newState = !showingArchived;
        const $container = $(PatientNotes.config.selectors.notesContainer);

        $button.data('showing-archived', newState);
        PatientNotes.ui.updateToggleButton($button, newState);
        PatientNotes.utils.showLoading($container);

        PatientNotes.api.loadNotes(newState)
          .done(response => {
            if (!response.notes?.length) {
              PatientNotes.utils.showEmptyState($container, `No ${newState ? 'archived' : 'active'} notes found`);
              return;
            }
            PatientNotes.ui.renderNotes(response.notes, $container);
          })
          .fail(() => {
            PatientNotes.utils.showError($container);
            $button.data('showing-archived', showingArchived);
            PatientNotes.ui.updateToggleButton($button, showingArchived);
          });
      },

      toggleArchiveNote: function(e) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();

        const $link = $(this);
        const url = $link.attr('href');
        const noteId = url.match(/\/(\d+)\/archive/)[1];
        const $noteElement = $(`[data-note-id="${noteId}"]`);
        const showingArchived = $(PatientNotes.config.selectors.toggleButton).data('showing-archived');

        $link.css('pointer-events', 'none');

        PatientNotes.api.toggleArchive(url)
          .done(() => {
            $noteElement.fadeOut(300, function() {
              $(this).remove();
              const $container = $(PatientNotes.config.selectors.notesContainer);
              const remainingNotes = $container.find('[data-note-id]').length;

              if (remainingNotes === 0) {
                const message = showingArchived ? 'No archived notes found' : 'No active notes found';
                PatientNotes.utils.showEmptyState($container, message);
              }
            });

            const actionText = showingArchived ? 'unarchived' : 'archived';
            PatientNotes.utils.showToast(`Note ${actionText} successfully`);
            PatientNotes.refreshPinnedNotes();
          })
          .fail(() => {
            $link.css('pointer-events', 'auto');
            PatientNotes.utils.showToast('Failed to archive/unarchive note. Please try again.', 'error');
          });

        return false;
      },

      togglePinNote: function(e) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();

        const $link = $(this);
        const url = $link.attr('href');
        const currentText = $link.text().trim();
        const isPinning = currentText === 'Pin note';

        $link.css('pointer-events', 'none');

        PatientNotes.api.togglePin(url)
          .done(() => {
            $link.text(isPinning ? 'Unpin note' : 'Pin note');
            $link.closest('.note-menu').addClass('hidden');

            const actionText = isPinning ? 'pinned' : 'unpinned';
            PatientNotes.utils.showToast(`Note ${actionText} successfully`);
            PatientNotes.refreshPinnedNotes();
          })
          .fail(() => {
            PatientNotes.utils.showToast('Failed to pin/unpin note. Please try again.', 'error');
          })
          .always(() => {
            $link.css('pointer-events', 'auto');
          });

        return false;
      }
    },

    // Public methods
    refreshPinnedNotes: function() {
      const patientId = $(PatientNotes.config.selectors.dropdownContainer).data('patient-id');
      if (!patientId) {
        console.error('Patient ID not found for refreshing pinned notes');
        return;
      }

      PatientNotes.api.loadPinnedNotes()
        .done(response => {
          if (response.success) {
            PatientNotes.ui.updatePinnedNotes(response.notes);
          }
        })
        .fail((xhr, status, error) => {
          console.error('Error refreshing pinned notes:', error);
        });
    },

    // Initialize all event listeners
    init: function() {
      const { selectors } = PatientNotes.config;

      $(document)
        .on('click', selectors.toggleButton, PatientNotes.handlers.toggleArchived)
        .on('click', selectors.archiveLinks, PatientNotes.handlers.toggleArchiveNote)
        .on('click', selectors.pinLinks, PatientNotes.handlers.togglePinNote);
    }
  };

  // Initialize the module
  PatientNotes.init();

  // Expose public methods to global scope
  window.PatientNotes = {
    refreshPinnedNotes: PatientNotes.refreshPinnedNotes
  };
});