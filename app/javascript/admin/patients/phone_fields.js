// Phone Fields with International Input
document.addEventListener('DOMContentLoaded', function() {
  if (!document.querySelector('.phone-field-container')) return;

  const form = document.getElementById('patient-details-form');
  const patientId = form?.dataset.patientId;
  if (!patientId) return;

  // Initialize phone field handlers
  initializePhoneFields();

  function initializePhoneFields() {
    document.querySelectorAll('.phone-field-container').forEach(container => {
      const display = container.querySelector('.phone-display');
      const edit = container.querySelector('.phone-edit');
      const input = container.querySelector('.phone-input');
      const fieldName = container.dataset.field;

      let itiInstance = null;

      // Initialize intl-tel-input immediately since edit mode is always shown
      itiInstance = window.intlTelInput(input, {
        loadUtils: () => import("https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js"),
        initialCountry: "auto",
        geoIpLookup: callback => {
          fetch('https://ipapi.co/json')
            .then(res => res.json())
            .then(data => callback(data.country_code))
            .catch(() => callback("au"));
        },
        preferredCountries: ["au", "nz", "us", "gb"],
        separateDialCode: false, // Keep flag with input, no separate dial code
        autoFormat: true,
        formatOnDisplay: true,
        nationalMode: false, // Keep full international format
        autoPlaceholder: "aggressive"
      });

      // Store original value when user starts editing
      input.addEventListener('focus', function() {
        input.dataset.originalValue = input.value;
      });

      // Save on blur
      input.addEventListener('blur', function() {
        savePhoneField();
      });

      // Save on Enter key
      input.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
          input.blur(); // Trigger save
        }
      });

      function showEditMode() {
        // Not needed anymore since edit mode is always shown
      }

      function hideEditMode() {
        // Not needed anymore since edit mode is always shown
      }

      function savePhoneField() {
        let phoneNumber = '';

        if (itiInstance) {
          // Get the full international number - this handles the formatting correctly
          phoneNumber = itiInstance.getNumber();
          console.log('Full international number:', phoneNumber);

          // If getNumber() returns empty but there's a value, try to get it manually
          if (!phoneNumber && input.value.trim()) {
            phoneNumber = input.value.trim();
          }
        } else {
          phoneNumber = input.value;
        }

        // Update the input field immediately with the formatted number
        // This provides instant visual feedback to the user
        if (phoneNumber && phoneNumber !== input.value) {
          input.value = phoneNumber;
        }

        // Show loading state
        container.classList.add('opacity-50');

        // Prepare data for AJAX request
        const data = {};
        data[`patient[${fieldName}]`] = phoneNumber;

        // Send AJAX request
        $.ajax({
          url: `/admin/patients/${patientId}/update_field`,
          method: 'POST',
          data: data,
          dataType: 'json',
          headers: {
            'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
          },
          success: function(response) {
            if (response.success) {
              // Update display with the saved value (even though it's hidden)
              const displaySpan = display.querySelector('span');
              if (phoneNumber) {
                displaySpan.textContent = phoneNumber;
                displaySpan.className = 'text-[14px] font-medium text-gray-800';
              } else {
                displaySpan.textContent = '-';
                displaySpan.className = 'text-[14px] text-gray-400';
              }

              toastr.success(`${fieldName.replace(/_/g, ' ')} updated`);
            } else {
              toastr.error(`Failed to update ${fieldName.replace(/_/g, ' ')}`);
              // If save failed, revert the input to its original value
              if (itiInstance) {
                itiInstance.setNumber(input.dataset.originalValue || '');
              }
            }
          },
          error: function() {
            toastr.error(`Failed to update ${fieldName.replace(/_/g, ' ')}`);
            // If save failed, revert the input to its original value
            if (itiInstance) {
              itiInstance.setNumber(input.dataset.originalValue || '');
            }
          },
          complete: function() {
            // Reset loading state
            container.classList.remove('opacity-50');
          }
        });
      }
    });
  }
});
