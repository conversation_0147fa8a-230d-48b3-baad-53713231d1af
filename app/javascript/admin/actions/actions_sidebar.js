// Actions sidebar handling
import '../actions/reminder_dropdown';
import SidebarManager from '../shared/sidebar_manager';
class ActionsSidebarManager {
  static instance = null;
  
  constructor() {
    // Singleton pattern
    if (ActionsSidebarManager.instance) {
      return ActionsSidebarManager.instance;
    }
    ActionsSidebarManager.instance = this;
    
    // Initialize properties
    this.actionsSidebar = document.getElementById('actions-sidebar');
    this.overlay = document.getElementById('shared-sidebar-overlay'); // Use the shared overlay
    this.closeActionsBtn = document.getElementById('close-actions');
    this.toggleCompletedBtn = document.getElementById('toggle-completed-actions');
    this.completedActionsList = document.querySelector('.completed-actions-list');
    this.filterButtons = document.querySelectorAll('.actions-filter-tabs .nav-link');
    this.tabButtons = document.querySelectorAll('.actions-header .tab-container .tab');

    // Only initialize if the required elements exist
    if (this.actionsSidebar && this.overlay) {
      this.initializeEventListeners();
      this.initializeTabs();
      this.initializeEditButtons();

      // Register with the global sidebar manager
      SidebarManager.register('actions-sidebar',
        () => this.openSidebarInternal(),
        () => this.closeSidebarInternal()
      );
    }
  }

  initializeEventListeners() {
    // Navbar toggle button
    const actionsToggle = document.getElementById('actions-toggle');
    
    if (actionsToggle) {
      actionsToggle.addEventListener('click', (e) => {
        this.toggleSidebar(e);
      });
    }
    
    // Sidebar toggle
    this.closeActionsBtn?.addEventListener('click', () => this.closeSidebar());

    // Toggle completed actions
    this.toggleCompletedBtn?.addEventListener('click', () => this.toggleCompletedActions());

    // Filter actions
    this.filterButtons.forEach(button => {
      button.addEventListener('click', (e) => this.filterActions(e));
    });

    // Action complete/uncomplete buttons
    document.addEventListener('click', (e) => {
      const completeBtn = e.target.closest('.action-complete-btn');
      if (completeBtn) {
        const actionId = completeBtn.dataset.actionId;
        this.markActionAsCompleted(actionId, true);
      }

      const uncompleteBtn = e.target.closest('.action-uncomplete-btn');
      if (uncompleteBtn) {
        const actionId = uncompleteBtn.dataset.actionId;
        this.markActionAsCompleted(actionId, false);
      }
    });

    // Action comment button
    document.addEventListener('click', (e) => {
      const commentBtn = e.target.closest('.action-comment-btn');
      if (commentBtn) {
        const actionId = commentBtn.dataset.actionId;
        document.querySelector('#action_comment_action_id').value = actionId;
      }
      
      // Handle reminder button click
      const reminderBtn = e.target.closest('.action-reminder-btn');
      if (reminderBtn) {
        const actionId = reminderBtn.dataset.actionId;
        document.querySelector('#reminder_action_id').value = actionId;
      }
      
      // Handle reminder option clicks
      const reminderOption = e.target.closest('.set-reminder-btn');
      if (reminderOption) {
        e.preventDefault();
        const actionId = document.querySelector('#reminder_action_id').value;
        const minutes = reminderOption.dataset.minutes;
        this.setActionReminder(actionId, minutes);
        
        // Close the modal
        const reminderModal = bootstrap.Modal.getInstance(document.getElementById('reminderModal'));
        if (reminderModal) {
          reminderModal.hide();
        }
      }
    });

    // Action type selector in modal
    document.addEventListener('click', (e) => {
      const typeButton = e.target.closest('.sender-tab');
      if (typeButton) {
        document.querySelectorAll('.sender-tab').forEach(btn => btn.classList.remove('active'));
        typeButton.classList.add('active');

        const actionType = typeButton.dataset.actionType;
        document.querySelector('#action_data_action_type').value = actionType;

        // Priority is now available for all action types - no need to hide/show
      }
    });

    // Priority selection in modal - no longer needed since we use a proper select field
  }

  initializeTabs() {
    // Add event listeners to tab buttons in the header
    const tabs = document.querySelectorAll('.actions-header .tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        tabs.forEach(t => t.classList.remove('active'));
        tab.classList.add('active');
        this.filterByDate(tab.textContent.trim());
      });
    });

    // Initialize with All Actions tab
    this.filterByDate('All Actions');
  }

  toggleSidebar(e) {
    // Prevent any default behavior and stop event propagation
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    // Check if this sidebar is currently open
    const isOpen = SidebarManager.sidebars['actions-sidebar']?.isOpen;
    
    if (isOpen) {
      // If it's open, close it
      SidebarManager.close('actions-sidebar');
    } else {
      // If it's closed, open it
      SidebarManager.open('actions-sidebar');
    }
  }
  
  openSidebar() {
    // Use the global sidebar manager to handle opening
    SidebarManager.open('actions-sidebar');
  }
  
  // Internal function that actually performs the opening
  openSidebarInternal() {
    if (!this.actionsSidebar) {
      return;
    }

    // Directly set the transform style to show the sidebar
    this.actionsSidebar.style.transform = 'translateX(0)';
    this.actionsSidebar.classList.add('open');

    document.body.classList.add('sidebar-open');

    // When opening the sidebar, make sure we're showing all actions
    this.filterByDate('All Actions');

    // Initialize the default active filter button state
    setTimeout(() => {
      if (window.appleFilterButtons) {
        // Set the action type filter "All" as active
        const allFilterButton = this.actionsSidebar.querySelector('[data-filter="all"]');
        if (allFilterButton) {
          window.appleFilterButtons.setActiveButton(allFilterButton);
        }

        // Set the date tab "All Actions" as active
        const allActionsTab = this.actionsSidebar.querySelector('[data-tab="all"]');
        if (allActionsTab) {
          window.appleFilterButtons.setActiveButton(allActionsTab);
        }
      }
    }, 100);

    // Update the actions counter to reflect the current view
    if (window.ActionsSidebar && window.ActionsSidebar.updateActionsCounter) {
      window.ActionsSidebar.updateActionsCounter();
    }
  }

  closeSidebar() {
    // Use the global sidebar manager to handle closing
    SidebarManager.close('actions-sidebar');
  }
  
  // Internal function that actually performs the closing
  closeSidebarInternal() {
    if (!this.actionsSidebar) {
      return;
    }

    // Reset the transform to hide the sidebar
    this.actionsSidebar.style.transform = 'translateX(100%)';
    this.actionsSidebar.classList.remove('open');

    document.body.classList.remove('sidebar-open');

    // When closing sidebar, restore the server-side counter by triggering a page refresh of the counter
    // This ensures we get back to the accurate total count
    if (window.ActionsSidebar && window.ActionsSidebar.restoreServerSideCounter) {
      window.ActionsSidebar.restoreServerSideCounter();
    }
  }

  toggleCompletedActions() {
    const isVisible = this.completedActionsList.style.display !== 'none';
    this.completedActionsList.style.display = isVisible ? 'none' : 'block';
    this.toggleCompletedBtn.textContent = isVisible ? 'Show Completed Actions' : 'Hide Completed Actions';
  }

  filterActions(e) {
    const filterType = e.target.dataset.filter;

    // Update active button
    this.filterButtons.forEach(btn => btn.classList.remove('active'));
    e.target.classList.add('active');

    // Filter action cards
    const actionCards = document.querySelectorAll('.action-card:not(.completed)');

    if (filterType === 'all') {
      actionCards.forEach(card => card.style.display = 'block');
    } else {
      actionCards.forEach(card => {
        const cardType = card.dataset.actionType;
        card.style.display = cardType === filterType ? 'block' : 'none';
      });
    }

    // Show/hide empty state
    const visibleCards = Array.from(actionCards).filter(card => card.style.display !== 'none');
    const emptyState = document.querySelector('.actions-content .empty-state');

    if (emptyState) {
      emptyState.style.display = visibleCards.length === 0 ? 'flex' : 'none';
    }
  }

  filterByDate(tabName) {
    if (!this.actionsSidebar) {
      return;
    }
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const actionCards = document.querySelectorAll('.action-card:not(.completed)');
    if (!actionCards || actionCards.length === 0) {
      return;
    }

    if (tabName === 'Today') {
      actionCards.forEach(card => {
        const dateElement = card.querySelector('.action-date');
        if (dateElement) {
          const dateStr = dateElement.textContent.trim(); // e.g., "01 Jan 2023"

          // Parse the date in the format "DD MMM YYYY"
          const [day, month, year] = dateStr.split(' ');
          const monthMap = {
            'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
            'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
          };

          const cardDate = new Date(year, monthMap[month], parseInt(day));
          cardDate.setHours(0, 0, 0, 0);

          // Check if the date is today
          const isToday = cardDate.getDate() === today.getDate() &&
                         cardDate.getMonth() === today.getMonth() &&
                         cardDate.getFullYear() === today.getFullYear();

          card.style.display = isToday ? 'block' : 'none';
        }
      });
    } else {
      actionCards.forEach(card => card.style.display = 'block');
    }

    // Reset type filter when switching tabs
    this.filterButtons.forEach(btn => {
      if (btn.dataset.filter === 'all') {
        btn.classList.add('active');
      } else {
        btn.classList.remove('active');
      }
    });

    // Show/hide empty state
    const visibleCards = Array.from(actionCards).filter(card => card.style.display !== 'none');
    const emptyState = document.querySelector('.actions-content .empty-state');

    if (emptyState) {
      emptyState.style.display = visibleCards.length === 0 ? 'flex' : 'none';
    }
  }

  markActionAsCompleted(actionId, completed) {
    fetch(`/admin/actions/${actionId}/mark_as_completed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    })
    .then(response => {
      if (response.ok) {
        // Refresh the timeline if it exists instead of reloading the page
        if (typeof window.refreshTimelineIfExists === 'function') {
          window.refreshTimelineIfExists(actionId);
        }

        // Refresh the actions sidebar
        window.location.reload();
      }
    })
    .catch(error => {
      // Silently handle error
    });
  }

  setActionReminder(actionId, minutes) {
    // Show loading state on the button
    const reminderBtn = document.querySelector(`.set-reminder-btn[data-minutes="${minutes}"]`);
    if (reminderBtn) {
      const originalHtml = reminderBtn.innerHTML;
      reminderBtn.innerHTML = '<i class="fa-light fa-spinner fa-spin"></i> Setting...';
      reminderBtn.disabled = true;
    }
    
    fetch(`/admin/actions/${actionId}/set_reminder`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ minutes: minutes })
    })
    .then(response => {
      if (response.ok) {
        // Get the reminder text based on minutes
        let reminderText;
        switch(parseInt(minutes)) {
          case 5:
          case 15:
          case 30:
          case 60:
            reminderText = `${minutes} mins`;
            break;
          case 120:
            reminderText = "2 Hours";
            break;
          case 240:
            reminderText = "4 Hours";
            break;
          case 1440:
            reminderText = "Tomorrow";
            break;
          case 10080:
            reminderText = "Next Week";
            break;
          default:
            reminderText = `${minutes} mins`;
        }
        
        toastr.success(`Reminder set for ${reminderText}`);

        // Refresh the timeline if it exists instead of reloading the page
        if (typeof window.refreshTimelineIfExists === 'function') {
          setTimeout(() => {
            window.refreshTimelineIfExists(actionId);
          }, 500); // Small delay to ensure the reminder is processed
        } else {
          // Fallback to page reload if timeline refresh is not available
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }
      } else {
        // Reset button state if there was an error
        if (reminderBtn) {
          reminderBtn.innerHTML = originalHtml;
          reminderBtn.disabled = false;
        }
        
        response.json().then(data => {
          toastr.error(data.error || 'Error setting reminder');
        }).catch(() => {
          toastr.error('Error setting reminder');
        });
      }
    })
    .catch(error => {
      // Reset button state
      if (reminderBtn) {
        reminderBtn.innerHTML = originalHtml;
        reminderBtn.disabled = false;
      }
      
      toastr.error('Error setting reminder. Please try again.');
    });
  }

  refreshActions() {
    fetch('/admin/actions', {
      method: 'GET',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    })
    .then(response => response.json())
    .then(data => {
      // Update the actions content
      // Implementation depends on the response format
    })
    .catch(error => {
      // Silently handle error
    });
  }

  initializeEditButtons() {
    // Handle edit action button clicks using event delegation
    document.addEventListener('click', (e) => {
      if (e.target.closest('.edit-action-btn')) {
        e.preventDefault();
        const button = e.target.closest('.edit-action-btn');
        const actionId = button.getAttribute('data-action-id');

        if (!actionId) {
          console.error('No action ID found for edit button');
          return;
        }

        // Fetch action data via AJAX
        fetch(`/admin/actions/${actionId}/edit`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success && data.action) {
            // Open modal in edit mode with action data
            if (window.unifiedActionModal) {
              window.unifiedActionModal.openModal('sidebarNewActionModal', {
                editAction: data.action
              });
            } else {
              console.error('Unified action modal manager not available');
            }
          } else {
            console.error('Failed to fetch action data:', data);
            alert('Failed to load action data for editing');
          }
        })
        .catch(error => {
          console.error('Error fetching action data:', error);
          alert('Failed to load action data for editing');
        });
      }
    });
  }
}

// Initialize actions sidebar manager when document is ready
document.addEventListener('DOMContentLoaded', initializeActionsSidebar);

function initializeActionsSidebar() {
  window.actionsSidebarManager = new ActionsSidebarManager();
  
  // Add a global document click listener for the actions toggle button
  document.addEventListener('click', function(event) {
    const toggleButton = event.target.closest('#actions-toggle');
    if (toggleButton) {
      event.preventDefault();
      if (window.actionsSidebarManager) {
        window.actionsSidebarManager.openSidebar();
      }
    }
  });
}

// Select2 initialization is now handled in shared/actions.js

// Export the module
export default ActionsSidebarManager;
