$(document).ready(function () {
    if (!($('#templateNoteForm').length || $('.cot-template-notes-page').length)) return;

    initializeSelect2();
    initializeFormSubmission();
    initializeTemplateTypeToggle();

    document.querySelectorAll('.toggle-popout-menu-button').forEach(button => {
        button.addEventListener('click', function (event) {
            event.preventDefault();
            event.stopPropagation();

            const noteId = button.dataset.id;
            const popout = document.querySelector(`.index-note-templates-popout[data-note-id="${noteId}"]`);

            document.querySelectorAll('.index-note-templates-popout').forEach(popout => {
                popout.classList.add('hidden');
            });

            popout.classList.toggle('hidden');
        });
    })

    document.addEventListener('click', function (event) {
        var popups = $(".index-note-templates-popout");

        popups.each(function () {
            var popup = $(this);
            if ($(event.target).closest('button').data('note-id') !== popup.data('note-id')) {
                popup.addClass('hidden');
            }
        });
    });

    $(".toggle-popout").on("click", function () {
        togglePopoutMenu("index-form-popout", $(this).data("id"));
    });

    function togglePopoutMenu(popoutClass, planId) {
        const popout = document.querySelector(`.${popoutClass}[data-note-id="${planId}"]`);
        popout.classList.toggle('hidden');
    }

    function initializeFormSubmission() {
        const form = $('#templateNoteForm');

        if (form.length) {
            // Add submit event listener
            form.on('submit', function(e) {
                // Sync TinyMCE content before form submission
                if (typeof tinymce !== 'undefined') {
                    const editor = tinymce.get('templateContent');
                    if (editor) {
                        editor.save();
                    }
                }

                // Let the form submit normally
                return true;
            });

            // Add direct click event on submit button as backup
            const submitBtn = form.find('input[type="submit"]');

            submitBtn.on('click', function(e) {
                // Sync TinyMCE content BEFORE validation
                if (typeof tinymce !== 'undefined') {
                    const editor = tinymce.get('templateContent');
                    if (editor) {
                        editor.save();
                    }
                }

                // Check if form is valid
                const formElement = form[0];
                if (formElement.checkValidity && !formElement.checkValidity()) {
                    return false;
                }

                // Let the click proceed normally
                return true;
            });
        }
    }

    function initializeSelect2() {
        if ($('#templateUsers').length) {
            const selectElement = $('#templateUsers');
            selectElement.select2({
                theme: 'tailwindcss-3',
                placeholder: 'Select users',
                allowClear: true,
                multiple: true,
                ajax: {
                    url: '/admin/general_settings/users/select2_search',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term
                        };
                    },
                    processResults: function (data, params) {
                        return data;
                    }
                },
                minimumInputLength: 1,
                width: '100%'
            });
        }
    }

    function initializeUsersSelect2() {
        if ($('#templateUsers').length) {
            const selectElement = $('#templateUsers');
            selectElement.select2({
                theme: 'tailwindcss-3',
                placeholder: 'Select users',
                allowClear: true,
                multiple: true,
                ajax: {
                    url: '/admin/general_settings/users/select2_search',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term
                        };
                    },
                    processResults: function (data, params) {
                        return data;
                    }
                },
                minimumInputLength: 1,
                width: '100%'
            });
        }
    }

    function initializeTemplateTypeToggle() {
        // Handle template type toggle clicks
        $('.template-type-toggle').on('click', function(e) {
            e.preventDefault();
            
            const $clickedButton = $(this);
            const templateType = $clickedButton.data('template_type');
            const toggleGroup = $clickedButton.data('toggle_group');
            const color = $clickedButton.data('color') || 'blue';
            
            // Remove active state from all buttons in the same group
            $(`[data-toggle-group="${toggleGroup}"]`).each(function() {
                const $button = $(this);
                $button.removeClass('active-filter pl-3 pr-4')
                       .addClass('px-3 bg-white text-gray-600 hover:bg-gray-50')
                       .removeAttr('style');
                
                // Hide the text span for inactive buttons
                $button.find('.tab-text').removeClass('ml-1.5').addClass('opacity-0 w-0');
            });
            
            // Add active state to clicked button
            $clickedButton.removeClass('px-3 bg-white text-gray-600 hover:bg-gray-50')
                          .addClass('active-filter pl-3 pr-4');
            
            // Apply gradient style for active button
            const gradients = {
                'blue': 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                'purple': 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                'green': 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                'orange': 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                'red': 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
            };
            const gradient = gradients[color] || gradients['blue'];
            $clickedButton.attr('style', `background: ${gradient};`);
            
            // Show the text span for active button
            $clickedButton.find('.tab-text').removeClass('opacity-0 w-0').addClass('ml-1.5');
            
            // Show/hide appropriate selectors based on template type
            if (templateType === 'practice') {
                $('#clinicianSelector').addClass('hidden').hide();
                
                // Make practice selector required and clinician not required
                $('#templatePractice').prop('required', true);
                $('#templateUsers').prop('required', false);
                
                // Clear clinician selection when switching to practice
                $('#templateUsers').val(null).trigger('change');
            } else if (templateType === 'clinician') {
                $('#clinicianSelector').removeClass('hidden').show();
                
                // Make clinician selector required and practice not required
                $('#templatePractice').prop('required', false);
                $('#templateUsers').prop('required', true);
                
                // Clear practice selection when switching to clinician
                $('#templatePractice').val('').trigger('change');
                
                // Destroy existing Select2 instance if it exists, then reinitialize
                if ($('#templateUsers').hasClass('select2-hidden-accessible')) {
                    $('#templateUsers').select2('destroy');
                }
                
                // Reinitialize Select2 for the clinician selector when it becomes visible
                setTimeout(function() {
                    initializeUsersSelect2();
                }, 100);
            }
        });
        
        // Initialize default state (practice template selected by default)
        const $defaultButton = $('.template-type-toggle[data-template-type="practice"]');
        if ($defaultButton.length) {
            $('#clinicianSelector').addClass('hidden').hide();
            $('#templatePractice').prop('required', true);
            $('#templateUsers').prop('required', false);
        }
    }
})
