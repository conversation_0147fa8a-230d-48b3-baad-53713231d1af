document.addEventListener('DOMContentLoaded', function() {
  if (document.getElementById('week-picker') !== null) {
    const currentDateStr = document.getElementById('current-week').dataset.selectedDate;
    const weekLabel = document.getElementById('current-week');

    const fpWeek = $('#week-picker').flatpickr({
      "plugins": [new weekSelect({})],
      dateFormat: "Y-m-d",
      defaultDate: currentDateStr,
      altFormat: "F Y",
      monthSelectorType: 'static',
      inline: false,
      static: true,
      clickOpens: false,
      locale: {
        firstDayOfWeek: 1
      },
      onClose: function(selectedDates, dateStr, instance) {
        if (dateStr && dateStr !== currentDateStr) {
          const currentUrl = new URL(window.location.href);
          currentUrl.searchParams.set('date', dateStr);

          const practiceId = new URLSearchParams(window.location.search).get('selected_practice_id');
          if (practiceId) {
            currentUrl.searchParams.set('selected_practice_id', practiceId);
          }
          window.location.href = currentUrl.toString();
        }
      },
      onOpen: function(selectedDates, dateStr, instance) {
        positionCalendar();
        window.addEventListener('scroll', positionCalendar, { passive: true });
      },
    });

    function positionCalendar() {
      const rect = weekLabel.getBoundingClientRect();
      const calContainer = fpWeek.calendarContainer;

      calContainer.style.display = 'block';
      calContainer.style.zIndex = '2024';
      calContainer.style.position = 'fixed';

      const calendarHeight = calContainer.offsetHeight || 300;
      calContainer.style.top = `${rect.bottom}px`;
      calContainer.style.left = `${rect.left}px`;
    }

    weekLabel.addEventListener('click', function() {
      fpWeek.open();
    });
  }

  const modalToggle = document.getElementById('calendar-reserved-block-type-modal-toggle');

  if (!modalToggle) {
    return;
  }

  setupRowSearch();
  setupPracticeSelector();

  function setupPracticeSelector() {
    const practiceButton = document.getElementById('practice-selector-button');
    const practiceDropdown = document.getElementById('practice-selector-dropdown');
    const practiceSearchInput = document.getElementById('practice-search-input');
    const practiceLinks = document.querySelectorAll('#practice-selector-dropdown a');

    if (!practiceButton || !practiceDropdown) return;

    // Toggle dropdown when clicking the practice button
    practiceButton.addEventListener('click', function(event) {
      event.stopPropagation();
      const isVisible = practiceDropdown.classList.contains('hidden');

      if (isVisible) {
        practiceDropdown.classList.remove('hidden');
        practiceButton.querySelector('svg').style.transform = 'rotate(180deg)';
      } else {
        practiceDropdown.classList.add('hidden');
        practiceButton.querySelector('svg').style.transform = 'rotate(0deg)';
      }
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      if (!practiceDropdown.classList.contains('hidden')) {
        const isClickInsideDropdown = practiceDropdown.contains(event.target);
        const isClickOnButton = practiceButton.contains(event.target);

        if (!isClickInsideDropdown && !isClickOnButton) {
          practiceDropdown.classList.add('hidden');
          practiceButton.querySelector('svg').style.transform = 'rotate(0deg)';
        }
      }
    });

    // Filter practices based on search input
    if (practiceSearchInput) {
      practiceSearchInput.addEventListener('input', function() {
        const searchValue = this.value.toLowerCase().trim();

        practiceLinks.forEach(link => {
          const practiceName = link.textContent.trim().toLowerCase();
          if (practiceName.includes(searchValue)) {
            link.style.display = 'flex';
          } else {
            link.style.display = 'none';
          }
        });
      });
    }
  }

  function setupRowSearch() {
    $('#calendar-reserved-block-types-search-field').on('keyup', function () {
      var query = $(this).val();
      $(".admin-table-row").each(function () {
        if ($(this).text().toLowerCase().search(query.toLowerCase()) !== -1) {
          $(this).removeClass('hidden');
        } else {
          $(this).addClass('hidden');
        }
      })
    });
  }

  function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.classList.remove('hidden');
      modal.classList.add('opacity-0');
      requestAnimationFrame(() => modal.classList.remove('opacity-0'));
    }
  }

  function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.classList.add('hidden');
    }
  }

  function setupModalCloseHandlers(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    const closeButton = modal.querySelector('[data-bs-dismiss="modal"]');
    if (closeButton) {
      closeButton.addEventListener('click', () => hideModal(modalId));
    }

    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        hideModal(modalId);
      }
    });
  }

  setupModalCloseHandlers('calendarReservedBlockTypeModal');

  $('.delete-calendar-reserved-block').click(function() {
    const typeId = $(this).data('calendar-reserved-block-type-id');
    const blockId = $(this).data('calendar-reserved-block-id');

    Swal.fire({
      title: 'Are you sure?',
      text: 'Are you sure you want to delete this reserved block?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: '/admin/general_settings/calendar_setting',
          type: 'PATCH',
          contentType: 'application/json',
          headers: {
            'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content'),
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
          },
          data: JSON.stringify({
            calendar_setting: {
              calendar_reserved_block_types_attributes: [{
                id: typeId,
                calendar_reserved_blocks_attributes: [{
                  id: blockId,
                  _destroy: true
                }]
              }]
            },
            selected_practice_id: $('#calendar-reserved-block-type-practice-id').val()
          }),
          success: function() {
            Swal.fire(
              'Deleted!',
              'The reserved block has been deleted.',
              'success'
            ).then(() => {
              window.location.reload();
            });
          },
          error: function(xhr) {
            console.error('Error:', xhr.responseText);
            Swal.fire('Error', 'Unable to delete this reserved block.', 'error');
          }
        });
      }
    });
  });

  $('.delete-calendar-reserved-block-type').click(function() {
    const typeId = $(this).data('calendar-reserved-block-type-id');

    Swal.fire({
      title: 'Are you sure?',
      text: "This will delete the entire block type and all its time blocks!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: '/admin/general_settings/calendar_setting',
          type: 'PATCH',
          contentType: 'application/json',
          headers: {
            'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content'),
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
          },
          data: JSON.stringify({
            calendar_setting: {
              calendar_reserved_block_types_attributes: [{
                id: typeId,
                _destroy: true
              }]
            },
            selected_practice_id: $('#calendar-reserved-block-type-practice-id').val()
          }),
          success: function() {
            Swal.fire(
              'Deleted!',
              'The block type has been deleted.',
              'success'
            ).then(() => {
              window.location.reload();
            });
          },
          error: function(xhr) {
            console.error('Error:', xhr.responseText);
            Swal.fire('Error', 'Unable to delete this block type.', 'error');
          }
        });
      }
    });
  });

  const modalForm = document.getElementById('calendarReservedBlockTypeForm');
  const depositRequiredCheckbox = modalForm.querySelector('#calendarReservedBlockTypeDepositRequired');
  const newPatientDepositInput = modalForm.querySelector('#calendarReservedBlockTypeNewPatientDepositSize');
  const oldPatientDepositInput = modalForm.querySelector('#calendarReservedBlockTypeOldPatientDepositSize');
  const depositSizeContainer = modalForm.querySelector('#deposit_size_container')

  depositRequiredCheckbox.addEventListener('change', function() {
    depositSizeContainer.classList.toggle('hidden', !this.checked);
    if (this.checked) {
      if (!newPatientDepositInput.value) {
        newPatientDepositInput.value = '0.00';
      }
      if (!oldPatientDepositInput.value) {
        oldPatientDepositInput.value = '0.00';
      }
    }
  });

  function setupPractitionerSelect2(practitionerDropdown, modal) {
    if (practitionerDropdown.data('select2')) {
      practitionerDropdown.select2('destroy');
    }

    practitionerDropdown.select2({
      theme: 'tailwindcss-3',
      width: '100%',
      allowClear: false,
      selectionCssClass: 'form-control',
      dropdownCssClass: 'form-control',
      placeholder: "Any practitioner",
      dropdownParent: modal,
    });

    practitionerDropdown.on("change", function () {
      let selected = $(this).val();

      if (selected && selected.length > 0) {
        $(this).find('option[value=""]').remove();
      } else {
        if ($(this).find('option[value=""]').length === 0) {
          $(this).prepend('<option value="">Any practitioner</option>');
        }
      }
    });
  }

  setupPractitionerSelect2($("#calendarReservedBlockTypeUserIds"), modalForm);

  function proceedWithFormSubmission() {
    resetTypeModalForm();
    showModal('calendarReservedBlockTypeModal');

    const practiceId = document.getElementById('calendar-reserved-block-type-practice-id').value;

    modalForm.action = "/admin/general_settings/calendar_setting";
    modalForm.method = "POST";

    const methodInput = document.createElement('input');
    methodInput.type = 'hidden';
    methodInput.name = '_method';
    methodInput.value = 'PATCH';
    modalForm.appendChild(methodInput);

    const selectedPracticeIdInput = document.createElement('input');
    selectedPracticeIdInput.type = 'hidden';
    selectedPracticeIdInput.name = 'selected_practice_id';
    selectedPracticeIdInput.value = practiceId;
    modalForm.appendChild(selectedPracticeIdInput);

    const practiceIdInput = document.createElement('input');
    practiceIdInput.type = 'hidden';
    practiceIdInput.name = 'calendar_setting[calendar_reserved_block_types_attributes][0][practice_id]';
    practiceIdInput.value = practiceId;
    modalForm.appendChild(practiceIdInput);
  }

  const createButton = document.getElementById('calendar-reserved-block-type-modal-toggle');
  createButton.addEventListener('click', function() {
    let $calendarReservedBlockTypeForm = $("#calendarReservedBlockTypeForm");
    if (!$calendarReservedBlockTypeForm.length) return;

    const selectedPracticeId = document.getElementById('calendar-reserved-block-type-practice-id').value;
    $("#calendar-reserved-block-type-practice-id").val(selectedPracticeId);
    $("#calendarReservedBlockTypeUserIds").val(' ').trigger('change');

    proceedWithFormSubmission();
  });

  function resetTypeModalForm() {
    modalForm.reset();
    Array.from(modalForm.querySelectorAll('input[type="hidden"]')).forEach(i => i.remove());
    newPatientDepositInput.value = '0.00';
    oldPatientDepositInput.value = '0.00';
  }

  $('#calendarReservedBlockTypeTreatmentSelect').select2({
    theme: 'tailwindcss-3',
    allowClear: false,
    selectionCssClass: 'form-control',
    dropdownCssClass: 'form-control',
    placeholder: 'Search for a treatment',
    dropdownParent: $('#calendarReservedBlockTypeModal'),
    minimumInputLength: 3,
    width: '100%',
    ajax: {
      url: '/admin/general_settings/treatments/select2_search',
      dataType: 'json',
      delay: 250,
      data: function (params) {
        return {
          q: params.term
        };
      },
      processResults: function (data, params) {
        return data;
      }
    }
  });

  $('#calendar-reserved-block-practitioner-select').select2({
    theme: 'tailwindcss-3',
    allowClear: false,
    selectionCssClass: 'form-control',
    dropdownCssClass: 'form-control',
    minimumInputLength: 3,
    width: '100%',
    placeholder: 'Search for a practitioner',
    dropdownParent: $('#calendarReservedBlockModal'),
    ajax: {
      url: '/admin/general_settings/users/select2_search',
      dataType: 'json',
      delay: 250,
      data: function (params) {
        return {
          q: params.term
        };
      },
      processResults: function (data, params) {
        return data;
      }
    }
  });

  const descriptionTextarea = document.getElementById('calendarReservedBlockTypeDescription');
  const generateAIButton = document.getElementById('generateDescriptionAI');
  const treatmentSelect = document.getElementById('calendarReservedBlockTypeTreatmentSelect');

  if (generateAIButton && treatmentSelect && descriptionTextarea) {
    generateAIButton.addEventListener('click', function() {
      const treatmentId = treatmentSelect.value;
      if (!treatmentId) {
        Swal.fire({
          title: 'No Treatment Selected',
          text: 'Please select a treatment first.',
          icon: 'warning',
          confirmButtonText: 'OK'
        });
        return;
      }
      generateAIButton.disabled = true;
      generateAIButton.textContent = 'Generating...';
      fetch('/admin/general_settings/calendar_setting/generate_description_ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ treatment_id: treatmentId })
      })
      .then(response => response.json())
      .then(data => {
        console.log('AI response:', data);
        if (data.description && data.description.trim()) {
          descriptionTextarea.value = data.description;
        } else {
          Swal.fire({
            title: 'No Description Generated',
            text: 'AI could not generate a description for this treatment.',
            icon: 'info',
            confirmButtonText: 'OK'
          });
        }
      })
      .catch((error) => {
        console.error('Error generating description:', error);
        Swal.fire({
          title: 'Error',
          text: 'Failed to generate description. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK'
        });
      })
      .finally(() => {
        generateAIButton.disabled = false;
        generateAIButton.textContent = 'Generate with AI';
      });
    });

    treatmentSelect.addEventListener('change', function() {
      if (!descriptionTextarea.value.trim() && treatmentSelect.value) {
        generateAIButton.click();
      }
    });
  }

  document.querySelectorAll('.edit-type-link').forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();

      resetTypeModalForm();

      const typeId = this.dataset.id;
      const color = this.dataset.color;
      const treatmentId = this.dataset.treatmentId;
      const treatmentName = this.dataset.treatmentName;
      const treatmentDescription = this.dataset.treatmentDescription;
      const noPatient = (this.dataset.noPatientRequired === "true");
      const indicatesNew = (this.dataset.indicatesNew === "true");
      const onlineBookable = (this.dataset.onlineBookable === "true");
      const depositRequired = (this.dataset.depositRequired === "true");
      const onlyBookableWithinReservedSlots = (this.dataset.onlyBookableWithinReservedSlots === "true");
      let newPatientDepositSize = this.dataset.newPatientDepositSize;
      let oldPatientDepositSize = this.dataset.oldPatientDepositSize;
      const userIds = this.dataset.userIds;
      modalForm.action = "/admin/general_settings/calendar_setting";
      modalForm.method = "POST";

      const methodInput = document.createElement('input');
      methodInput.type = 'hidden';
      methodInput.name = '_method';
      methodInput.value = 'PATCH';
      modalForm.appendChild(methodInput);

      const idInput = document.createElement('input');
      idInput.type = 'hidden';
      idInput.name = "calendar_setting[calendar_reserved_block_types_attributes][0][id]";
      idInput.value = typeId;
      modalForm.appendChild(idInput);

      const selectedPracticeIdInput = document.createElement('input');
      selectedPracticeIdInput.type = 'hidden';
      selectedPracticeIdInput.name = 'selected_practice_id';
      selectedPracticeIdInput.value = document.getElementById('calendar-reserved-block-type-practice-id').value;
      modalForm.appendChild(selectedPracticeIdInput);

      const practiceIdInput = document.createElement('input');
      practiceIdInput.type = 'hidden';
      practiceIdInput.name = 'calendar_setting[calendar_reserved_block_types_attributes][0][practice_id]';
      practiceIdInput.value = document.getElementById('calendar-reserved-block-type-practice-id').value;
      modalForm.appendChild(practiceIdInput);

      let colorRadio = modalForm.querySelector(`input[type="radio"][value="${color}"]`);
      if (colorRadio) { colorRadio.checked = true; }

      if (treatmentId) {
        $('#calendarReservedBlockTypeTreatmentSelect').empty();
        let newOption = new Option(treatmentName, treatmentId, true, true);
        $('#calendarReservedBlockTypeTreatmentSelect').append(newOption).trigger('change');
      }

      if (treatmentDescription) {
        const descriptionTextarea = modalForm.querySelector('#calendarReservedBlockTypeDescription');
        if (descriptionTextarea) {
          descriptionTextarea.value = treatmentDescription;
        }
      }

      let noPatientCheckbox = modalForm.querySelector('#calendarReservedBlockTypeNoPatient');
      noPatientCheckbox.checked = noPatient;

      let indicatesNewCheckbox = modalForm.querySelector('#calendarReservedBlockTypeIndicatesNew');
      indicatesNewCheckbox.checked = indicatesNew;
      let onlineBookableCheckbox = modalForm.querySelector('#calendarReservedBlockTypeOnlineBookable');
      onlineBookableCheckbox.checked = onlineBookable;
      let onlyBookableWithinReservedSlotsCheckbox = modalForm.querySelector('#calendarReservedBlockTypeOnlyBookableWithinReservedSlots');
      onlyBookableWithinReservedSlotsCheckbox.checked = onlyBookableWithinReservedSlots;
      depositRequiredCheckbox.checked = depositRequired;

      if (newPatientDepositSize === undefined || newPatientDepositSize === null || newPatientDepositSize === '') {
        newPatientDepositSize = 0.0;
      }
      if (oldPatientDepositSize === undefined || oldPatientDepositSize === null || oldPatientDepositSize === '') {
        oldPatientDepositSize = 0.0;
      }

      const newPatientNumValue = parseFloat(newPatientDepositSize) || 0;
      const oldPatientNumValue = parseFloat(oldPatientDepositSize) || 0;
      newPatientDepositInput.value = newPatientNumValue.toFixed(2);
      oldPatientDepositInput.value = oldPatientNumValue.toFixed(2);
      depositSizeContainer.classList.toggle('hidden', !depositRequired);
      const userIdsArray = JSON.parse(userIds);
      $("#calendarReservedBlockTypeUserIds").val(userIdsArray).trigger('change');
      showModal('calendarReservedBlockTypeModal');
    });
  });
});
