document.addEventListener("DOMContentLoaded", () => {
    // Initialize practice form functionality
    initializePractices();
    initializeFormValidation();
    initializeTwoFactorAuth();

    // Existing keypress handler
    $(".edit-practice").bind("keypress", function (e) {
        if (e.keyCode === 13) {
            return false;
        }
    });
});

// Initialize all components
function initializePractices() {
    initializeTabs();
    initializeColorPickers();
    initializeOpeningHours();
    initializePaymentProcessingTabs();
    initializeInformationTab();
    initializeModalHandlers();
    initializeDropdowns();
    initializeComponentDropdowns();
    initializeCommunicationsDropdowns();
    initializeConnectTerminal();
}

// Tab Management
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.practice-tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    // Ensure information tab is active on page load
    const informationTab = document.querySelector('[data-tab="information-tab"]');
    const informationPane = document.getElementById('information-tab');

    if (informationTab && informationPane) {
        // Set information tab as active
        tabButtons.forEach(btn => {
            btn.classList.remove('border-blue-600', 'text-blue-600', 'active');
            btn.classList.add('border-transparent', 'text-gray-500');
        });

        informationTab.classList.remove('border-transparent', 'text-gray-500');
        informationTab.classList.add('border-blue-600', 'text-blue-600', 'active');

        // Show information pane, hide others
        tabPanes.forEach(pane => {
            pane.classList.add('hidden');
            pane.classList.remove('active');
        });

        // Make sure information tab is visible
        informationPane.classList.remove('hidden');
        informationPane.classList.add('active');

        console.log('Information tab initialized as active');
    }

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.disabled) return;

            const targetTab = this.getAttribute('data-tab');

            // Remove active state from all tabs
            tabButtons.forEach(btn => {
                btn.classList.remove('border-blue-600', 'text-blue-600', 'active');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            // Add active state to clicked tab
            this.classList.remove('border-transparent', 'text-gray-500');
            this.classList.add('border-blue-600', 'text-blue-600', 'active');

            // Hide all tab panes
            tabPanes.forEach(pane => {
                pane.classList.add('hidden');
                pane.classList.remove('active');
            });

            // Show target tab pane
            const targetPane = document.getElementById(targetTab);
            if (targetPane) {
                targetPane.classList.remove('hidden');
                targetPane.classList.add('active');
            }
        });
    });
}

// Opening Hours Management
function initializeOpeningHours() {
    const checkboxes = document.querySelectorAll('.opening-hours-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const dayIndex = this.getAttribute('data-day');
            const timeInputs = document.querySelector(`.opening-hours-times[data-day="${dayIndex}"]`);

            if (timeInputs) {
                toggleTimeInputs(timeInputs, !this.checked);
            }
        });

        // Initialize state on page load
        const dayIndex = checkbox.getAttribute('data-day');
        const timeInputs = document.querySelector(`.opening-hours-times[data-day="${dayIndex}"]`);
        if (timeInputs) {
            toggleTimeInputs(timeInputs, !checkbox.checked);
        }
    });
}

// Toggle time inputs based on closed status
function toggleTimeInputs(timeContainer, enable) {
    const inputs = timeContainer.querySelectorAll('input[type="time"]');
    const dayIndex = timeContainer.getAttribute('data-day');
    const closedButton = document.querySelector(`label[for="closed_${dayIndex}"]`);

    inputs.forEach(input => {
        input.disabled = !enable;
        if (enable) {
            input.classList.remove('bg-gray-100', 'cursor-not-allowed', 'text-gray-400');
            input.classList.add('bg-white');
        } else {
            input.classList.add('bg-gray-100', 'cursor-not-allowed', 'text-gray-400');
            input.classList.remove('bg-white');
        }
    });

    // Update closed button styling
    if (closedButton) {
        if (!enable) {
            closedButton.classList.add('bg-red-100', 'text-red-700');
            closedButton.classList.remove('bg-gray-100');
            closedButton.querySelector('.material-symbols-outlined').classList.add('text-red-500');
            closedButton.querySelector('.material-symbols-outlined').classList.remove('text-gray-400');
        } else {
            closedButton.classList.remove('bg-red-100', 'text-red-700');
            closedButton.classList.add('bg-gray-100');
            closedButton.querySelector('.material-symbols-outlined').classList.remove('text-red-500');
            closedButton.querySelector('.material-symbols-outlined').classList.add('text-gray-400');
        }
    }
}

// Form Validation
function initializeFormValidation() {
    // Only apply validation to information forms that have practice name field
    const form = document.querySelector('form[action*="practices"][action*="information"]');
    if (!form) return;

    form.addEventListener('submit', function(e) {
        if (!validatePracticeForm()) {
            e.preventDefault();
            return false;
        }
    });
}

// Validate practice form
function validatePracticeForm() {
    const name = document.querySelector('#practice_name');
    const email = document.querySelector('#practice_email');

    let isValid = true;
    let errorMessage = '';

    // Only validate practice name if the field exists (for information form)
    if (name && (!name.value || !name.value.trim())) {
        errorMessage += 'Practice name is required.\n';
        isValid = false;
    }

    // Validate email format if provided
    if (email && email.value.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email.value.trim())) {
            errorMessage += 'Please enter a valid email address.\n';
            isValid = false;
        }
    }

    if (!isValid) {
        alert(errorMessage);
    }

    return isValid;
}

// Color Picker Management
function initializeColorPickers() {
    const colorInputs = document.querySelectorAll('.color-picker');

    colorInputs.forEach(input => {
        const wrapper = document.createElement('div');
        wrapper.style.position = 'relative';
        wrapper.style.display = 'inline-block';
        wrapper.style.width = '100%';

        input.parentNode.insertBefore(wrapper, input);
        wrapper.appendChild(input);

        const colorInput = document.createElement('input');
        colorInput.type = 'color';
        colorInput.value = input.value || '#ffffff';
        colorInput.className = 'absolute opacity-0 pointer-events-none';
        colorInput.id = input.id + '_color';
        colorInput.style.top = '20px';
        colorInput.style.left = '0';
        colorInput.style.zIndex = '-1';

        wrapper.appendChild(colorInput);

        input.addEventListener('click', function() {
            colorInput.click();
        });

        colorInput.addEventListener('change', function() {
            input.value = colorInput.value;
            updateColorPreview(input);
        });

        colorInput.addEventListener('input', function() {
            input.value = colorInput.value;
            updateColorPreview(input);
        });

        input.addEventListener('input', function() {
            updateColorPreview(this);
            if (this.value.match(/^#[0-9A-F]{6}$/i)) {
                colorInput.value = this.value;
            }
        });

        input.addEventListener('blur', function() {
            updateColorPreview(this);
            if (this.value.match(/^#[0-9A-F]{6}$/i)) {
                colorInput.value = this.value;
            }
        });

        input.style.cursor = 'pointer';

        const colorPreview = document.querySelector(`[data-color-input="${input.id}"]`);
        if (colorPreview) {
            colorPreview.style.cursor = 'pointer';
            colorPreview.addEventListener('click', function() {
                colorInput.click();
            });
        }

        updateColorPreview(input);
    });
}

// Update color preview based on input value
function updateColorPreview(input) {
    const inputId = input.id;
    const colorPreview = document.querySelector(`[data-color-input="${inputId}"]`);

    if (colorPreview) {
        let colorValue = input.value.trim();

        // Validate hex color format
        if (colorValue && !colorValue.startsWith('#')) {
            colorValue = '#' + colorValue;
        }

        // Check if it's a valid hex color
        const isValidHex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(colorValue);

        if (isValidHex) {
            colorPreview.style.backgroundColor = colorValue;
            colorPreview.style.border = '1px solid #d1d5db'; // gray-300
        } else {
            // Default to white if invalid color
            colorPreview.style.backgroundColor = '#ffffff';
            colorPreview.style.border = '1px solid #d1d5db'; // gray-300
        }
    }
}

// Initialize payment processing nested tabs
function initializePaymentProcessingTabs() {
    const subtabButtons = document.querySelectorAll('.payment-subtab-btn');
    const subtabPanes = document.querySelectorAll('.payment-subtab-pane');

    if (subtabButtons.length === 0) return;

    subtabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetSubtab = this.getAttribute('data-subtab');

            // Remove active state from all subtab buttons
            subtabButtons.forEach(btn => {
                btn.classList.remove('border-blue-600', 'text-blue-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            // Add active state to clicked subtab button
            this.classList.remove('border-transparent', 'text-gray-500');
            this.classList.add('border-blue-600', 'text-blue-600');

            // Hide all subtab panes
            subtabPanes.forEach(pane => {
                pane.classList.add('hidden');
                pane.classList.remove('active');
            });

            // Show target subtab pane
            const targetPane = document.getElementById(targetSubtab);
            if (targetPane) {
                targetPane.classList.remove('hidden');
                targetPane.classList.add('active');
            }
        });
    });

    // Set initial active subtab on page load
    const activeSubtab = document.querySelector('.payment-subtab-btn.border-blue-600');
    if (activeSubtab) {
        const targetSubtab = activeSubtab.getAttribute('data-subtab');
        const targetPane = document.getElementById(targetSubtab);
        if (targetPane) {
            targetPane.classList.remove('hidden');
            targetPane.classList.add('active');
        }
    }
}

// Initialize modal handlers for custom modal functionality
function initializeModalHandlers() {
    const modalTriggers = document.querySelectorAll('[data-toggle="modal"]');
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    const modalOverlays = document.querySelectorAll('.modal-overlay');

    // Handle modal trigger clicks
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            const targetModalId = this.getAttribute('data-target');
            const targetModal = document.querySelector(targetModalId);

            if (targetModal) {
                showModal(targetModal);
            }
        });
    });

    // Handle modal close button clicks
    modalCloseButtons.forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            const modal = this.closest('.modal-overlay');
            if (modal) {
                hideModal(modal);
            }
        });
    });

    // Handle clicking outside modal to close
    modalOverlays.forEach(overlay => {
        overlay.addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal(this);
            }
        });
    });

    // Handle escape key to close modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const visibleModal = document.querySelector('.modal-overlay:not(.hidden)');
            if (visibleModal) {
                hideModal(visibleModal);
            }
        }
    });
}

// Show modal function
function showModal(modal) {
    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling

    // Focus on the modal for accessibility
    const firstInput = modal.querySelector('input, textarea, select, button');
    if (firstInput) {
        setTimeout(() => firstInput.focus(), 100);
    }
}

// Hide modal function
function hideModal(modal) {
    modal.classList.add('hidden');
    document.body.style.overflow = ''; // Restore background scrolling
}

// Initialize information tab
function initializeInformationTab() {
    // Add custom logic for information tab if needed
}

// Initialize 2FA functionality
function initializeTwoFactorAuth() {
    $("#practice_two_factor_enabled").change(function(e) {
        const practiceId = $(this).data("practice-id");
        if (!$(this).is(":checked")) {
            e.preventDefault();
            $(this).prop('checked', true);
            Swal.fire({
                title: 'Are you sure you want to disable 2FA?',
                text: 'Upod Medical is not liable for any unauthorised access when 2FA is disabled.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Disable 2FA',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    function sendDisableVerificationCode() {
                        fetch(`/admin/general_settings/practices/${practiceId}/send_verification_code`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
                                'Content-Type': 'application/json'
                            }
                        }).then(r => r.json()).then(j => {
                            Swal.fire({
                                title: 'Enter Verification Code',
                                text: 'Please enter the verification code sent via SMS to your phone.',
                                input: 'text',
                                inputPlaceholder: 'Enter your verification code',
                                showCancelButton: true,
                                confirmButtonText: 'Verify',
                                cancelButtonText: 'Cancel'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    const verificationCode = result.value;
                                    fetch(`/admin/general_settings/practices/${practiceId}/disable_two_factor`, {
                                        method: 'POST',
                                        headers: {
                                            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({ code: verificationCode, verification_id: j.verification_id })
                                    }).then(r => r.json()).then(j => {
                                        if(j.success) {
                                            $(this).prop('checked', false);
                                        } else {
                                            sendDisableVerificationCode();
                                        }
                                    })
                                }
                            });
                        })
                    }

                    sendDisableVerificationCode();
                }
            });
        }
    });
}

// Initialize dropdowns
function initializeDropdowns() {
    // Disabled - practices dropdowns now handled by unified dropdown system
    console.log('Practices: dropdown handlers disabled - using unified dropdown system');

    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    const dropdownMenus = document.querySelectorAll('.dropdown-menu');

    // Handle dropdown toggle clicks
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const templateId = this.getAttribute('data-template-id');
            const correspondingMenu = document.querySelector(`.dropdown-menu[data-template-id="${templateId}"]`);

            // Close all other dropdowns
            dropdownMenus.forEach(menu => {
                if (menu !== correspondingMenu) {
                    menu.classList.add('hidden');
                }
            });

            // Toggle the clicked dropdown
            if (correspondingMenu) {
                correspondingMenu.classList.toggle('hidden');
            }
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-toggle')) {
            const dropdownMenus = document.querySelectorAll('.dropdown-menu');
            dropdownMenus.forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // Handle escape key to close dropdowns
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const dropdownMenus = document.querySelectorAll('.dropdown-menu');
            dropdownMenus.forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });
}

// Initialize component dropdowns
function initializeComponentDropdowns() {
    const componentDropdownToggles = document.querySelectorAll('.component-dropdown-toggle');
    const componentDropdownMenus = document.querySelectorAll('.component-dropdown-menu');

    componentDropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const componentType = this.getAttribute('data-component');
            const correspondingMenu = document.querySelector(`.component-dropdown-menu[data-component="${componentType}"]`);

            // Close all other component dropdowns
            componentDropdownMenus.forEach(menu => {
                if (menu !== correspondingMenu) {
                    menu.classList.add('hidden');
                }
            });

            // Toggle the clicked dropdown
            if (correspondingMenu) {
                correspondingMenu.classList.toggle('hidden');
            }
        });
    });

    // Close component dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.component-dropdown-toggle')) {
            componentDropdownMenus.forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // Handle escape key to close component dropdowns
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            componentDropdownMenus.forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });
}

// Communications account card dropdown management
function initializeCommunicationsDropdowns() {
    const accountDropdownToggles = document.querySelectorAll('[data-account-id].dropdown-toggle');
    const accountDropdownMenus = document.querySelectorAll('[data-account-id].dropdown-menu');

    accountDropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const accountId = this.getAttribute('data-account-id');
            const correspondingMenu = document.querySelector(`[data-account-id="${accountId}"].dropdown-menu`);

            // Close other account dropdowns
            accountDropdownMenus.forEach(menu => {
                if (menu !== correspondingMenu) {
                    menu.classList.add('hidden');
                }
            });

            // Toggle current dropdown
            if (correspondingMenu) {
                correspondingMenu.classList.toggle('hidden');
            }
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('[data-account-id].dropdown-toggle')) {
            accountDropdownMenus.forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // Close dropdowns on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            accountDropdownMenus.forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });
}

function initializeConnectTerminal() {
    const connectTerminalBtn = document.querySelector('[data-action="connect-terminal"]');
    const connectTerminalForm = document.querySelector('#connectTerminalForm');

    if (connectTerminalBtn && connectTerminalForm) {
        connectTerminalBtn.addEventListener('click', function(e) {
            e.preventDefault();

            const practiceId = connectTerminalForm.getAttribute('data-practice-id');
            const formData = new FormData(connectTerminalForm);

            const defaultText = this.querySelector('[data-text="default"]');
            const loadingText = this.querySelector('[data-text="loading"]');

            defaultText.classList.add('hidden');
            loadingText.classList.remove('hidden');
            this.disabled = true;

            const payload = {
                practice_id: practiceId,
                registration_code: formData.get('registration_code'),
                name: formData.get('name'),
                line1: formData.get('line1'),
                line2: formData.get('line2'),
                city: formData.get('city'),
                postcode: formData.get('postcode')
            };

            $.ajax({
                url: `/admin/general_settings/practices/${practiceId}/payment_processing/connect_terminal`,
                method: 'POST',
                data: payload,
                headers: {
                    'X-CSRF-Token': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        if (typeof toastr !== 'undefined') {
                            toastr.success('Terminal connected successfully!');
                        }

                        const modal = document.querySelector('#connectTerminalModal');
                        if (modal) {
                            hideModal(modal);
                            connectTerminalForm.reset();
                            location.reload();
                        }
                    } else {
                        if (typeof toastr !== 'undefined') {
                            toastr.error(response.message || 'Failed to connect terminal');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Connect terminal error:', error);
                    let errorMessage = 'Failed to connect terminal. Please try again.';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }

                    if (typeof toastr !== 'undefined') {
                        toastr.error(errorMessage);
                    }
                },
                complete: function() {
                    defaultText.classList.remove('hidden');
                    loadingText.classList.add('hidden');
                    connectTerminalBtn.disabled = false;
                }
            });
        });
    }
}

// Export functions for external access if needed
window.PracticesForm = {
    initializeTabs,
    initializeOpeningHours,
    validatePracticeForm,
    toggleTimeInputs,
    initializeColorPickers,
    updateColorPreview,
    initializePaymentProcessingTabs,
    initializeModalHandlers,
    initializeInformationTab,
    initializeTwoFactorAuth,
    initializeDropdowns,
    initializeComponentDropdowns,
    initializeCommunicationsDropdowns,
    showModal,
    hideModal
};

window.initializeCommunicationsDropdowns = initializeCommunicationsDropdowns;
