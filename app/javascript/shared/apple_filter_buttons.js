class AppleFilterButtons {
  constructor(containerSelector = '.filter-btn') {
    this.containerSelector = containerSelector;
    this.colorGradients = {
      'blue': 'linear-gradient(to right, #bfdbfe, #93c5fd)',
      'purple': 'linear-gradient(to right, #ddd6fe, #c4b5fd)',
      'amber': 'linear-gradient(to right, #fde68a, #fcd34d)',
      'orange': 'linear-gradient(to right, #fed7aa, #fdba74)',
      'teal': 'linear-gradient(to right, #99f6e4, #5eead4)',
      'green': 'linear-gradient(to right, #bbf7d0, #86efac)',
      'red': 'linear-gradient(to right, #fecaca, #fca5a5)',
      'indigo': 'linear-gradient(to right, #c7d2fe, #a5b4fc)',
      'pink': 'linear-gradient(to right, #fbcfe8, #f9a8d4)',
      'emerald': 'linear-gradient(to right, #a7f3d0, #6ee7b7)',
      'cyan': 'linear-gradient(to right, #cffafe, #67e8f9)',
      'gray': 'linear-gradient(to right, #e5e7eb, #d1d5db)'
    };
    
    this.textColors = {
      'blue': 'text-blue-800',
      'purple': 'text-purple-800',
      'amber': 'text-amber-800',
      'orange': 'text-orange-800',
      'teal': 'text-teal-800',
      'green': 'text-green-800',
      'red': 'text-red-800',
      'indigo': 'text-indigo-800',
      'pink': 'text-pink-800',
      'emerald': 'text-emerald-800',
      'cyan': 'text-cyan-800',
      'gray': 'text-gray-800'
    };
    
    this.iconColors = {
      'blue': 'text-blue-700',
      'purple': 'text-purple-700',
      'amber': 'text-amber-700',
      'orange': 'text-orange-700',
      'teal': 'text-teal-700',
      'green': 'text-green-700',
      'red': 'text-red-700',
      'indigo': 'text-indigo-700',
      'pink': 'text-pink-700',
      'emerald': 'text-emerald-700',
      'cyan': 'text-cyan-700',
      'gray': 'text-gray-700'
    };
    
    this.init();
  }
  
  init() {
    this.attachEventListeners();
  }
  
  attachEventListeners() {
    // Handle action-type-filter buttons specifically - only within sidebar
    $(document).on('click', '#actions-sidebar .action-type-filter', (e) => {
      const button = e.currentTarget;
      this.setActiveButton(button);
    });

    // Handle tab buttons specifically (Today/All/Completed) - only within sidebar
    $(document).on('click', '#actions-sidebar .tab', (e) => {
      const button = e.currentTarget;
      this.setActiveButton(button);
    });

    // Only attach automatic event listeners to buttons that don't have custom handlers - only within sidebar
    $(document).on('click', `#actions-sidebar ${this.containerSelector}`, (e) => {
      const button = e.currentTarget;

      // Skip if the button has a custom handler (already handled above)
      if ($(button).hasClass('tab') || $(button).hasClass('action-type-filter') || $(button).hasClass('top-filter-btn')) {
        return;
      }

      this.setActiveButton(button);
    });
  }
  
  setActiveButton(activeButton) {
    const $activeButton = $(activeButton);
    const color = $activeButton.data('color') || 'blue';

    // Reset all buttons in the same group
    this.resetButtonsInGroup($activeButton);

    // Set the clicked button as active
    this.activateButton($activeButton, color);
  }
  
  resetButtonsInGroup($activeButton) {
    const group = $activeButton.data('group');

    if (group) {
      // Reset only buttons in the same group within the sidebar context
      $(`#actions-sidebar .filter-btn[data-group="${group}"]`).each((index, button) => {
        this.resetButton($(button));
      });
    } else {
      // Fallback to container-based reset for backwards compatibility, but only within sidebar
      this.resetAllButtons($activeButton);
    }
  }

  resetAllButtons($activeButton) {
    // Find all filter buttons in the same specific container
    const $container = $activeButton.closest('.filter-container');
    const $buttons = $container.length ? $container.find('.filter-btn') : $('.filter-btn');

    $buttons.each((index, button) => {
      this.resetButton($(button));
    });
  }

  resetButton($button) {
    // Remove active classes
    $button.removeClass('active-filter');

    // Remove all color classes
    Object.values(this.textColors).forEach(colorClass => {
      $button.removeClass(colorClass);
    });

    // Reset to default styling
    $button.addClass('bg-white text-gray-600');
    $button.css('background', '');

    // Reset padding
    $button.removeClass('pl-3 pr-4').addClass('px-3');

    // Reset text span
    const $textSpan = $button.find('.tab-text');
    if ($textSpan.length) {
      $textSpan.removeClass('ml-1.5').addClass('opacity-0 w-0');
    }

    // Reset icon color
    const $icon = $button.find('i');
    if ($icon.length) {
      Object.values(this.iconColors).forEach(iconColorClass => {
        $icon.removeClass(iconColorClass);
      });
    }
  }
  
  activateButton($button, color) {
    // Add active class
    $button.addClass('active-filter');
    
    // Remove default styling
    $button.removeClass('bg-white text-gray-600');
    
    // Add color styling
    const textColor = this.textColors[color] || this.textColors['blue'];
    $button.addClass(textColor);
    
    // Set gradient background
    const gradient = this.colorGradients[color] || this.colorGradients['blue'];
    $button.css('background', gradient);
    
    // Expand the button
    $button.removeClass('px-3').addClass('pl-3 pr-4');
    
    // Expand the text
    const $textSpan = $button.find('.tab-text');
    if ($textSpan.length) {
      $textSpan.removeClass('opacity-0 w-0').addClass('ml-1.5');
    }
    
    // Set icon color
    const $icon = $button.find('i');
    if ($icon.length) {
      const iconColor = this.iconColors[color] || this.iconColors['blue'];
      $icon.addClass(iconColor);
    }
  }
  
  // Public method to programmatically set active button
  setActive(buttonSelector, color = 'blue') {
    const $button = $(buttonSelector);
    if ($button.length) {
      this.setActiveButton($button[0]);
    }
  }
  
  // Public method to reset all buttons - only within sidebar
  resetAll() {
    $('#actions-sidebar .filter-btn').each((index, button) => {
      this.resetAllButtons($(button));
    });
  }
}

// Auto-initialize when DOM is ready
$(document).ready(() => {
  window.appleFilterButtons = new AppleFilterButtons();
});

// Export for manual initialization
export default AppleFilterButtons;