# frozen_string_literal: true

# == Schema Information
#
# Table name: charting_appointments
#
#  id                       :bigint           not null, primary key
#  course_of_treatment_id   :bigint
#  dentist_id               :bigint
#  completed                :boolean          default(FALSE)
#  completed_at             :datetime
#  locked                   :boolean          default(FALSE)
#  locked_at                :datetime
#  position                 :integer
#  appointment_type         :string
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  calendar_booking_id      :bigint
#  time_until_booking_count :integer          default(0)
#  time_until_booking_unit  :integer          default("weeks")
#  import_id                :string
#  import_source            :integer
#  import_data              :jsonb
#
class ChartingAppointment < ApplicationRecord
  include EventLoggable
  include Importable
  include IconStateObservable

  belongs_to :course_of_treatment, optional: true
  belongs_to :dentist, optional: true, class_name: 'User'
  belongs_to :calendar_booking, optional: true

  has_many :charted_treatments, dependent: :destroy
  has_many :lab_works, dependent: :destroy
  has_many :appointment_notes, dependent: :destroy
  has_many :signature_requests, dependent: :nullify

  has_one :icon_state, dependent: :destroy

  acts_as_list scope: :course_of_treatment

  accepts_nested_attributes_for :charted_treatments, allow_destroy: true, reject_if: :all_blank

  delegate :patient_id, to: :course_of_treatment

  enum time_until_booking_unit: {
    days: 0,
    weeks: 1,
    months: 2,
    years: 3
  }

  def completed?
    charted_treatments.any? && charted_treatments.where(completed: false).empty?
  end

  private

  def icon_state_observable_records
    [self, calendar_booking].compact
  end
end
