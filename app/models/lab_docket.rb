# frozen_string_literal: true

# == Schema Information
#
# Table name: lab_dockets
#
#  id                               :bigint           not null, primary key
#  docket_info                      :jsonb
#  lab_work_id                      :bigint           not null
#  interoral_scan                   :boolean
#  photos_sent                      :boolean
#  physical_impressions_taken       :boolean
#  draft                            :boolean          default(FALSE)
#  created_at                       :datetime         not null
#  updated_at                       :datetime         not null
#  instructions                     :text
#  disinfected_with                 :string
#  disinfected_by_id                :bigint
#  made_available                   :string
#  disinfectant_further_information :text
#
class LabDocket < ApplicationRecord
  belongs_to :lab_work
  belongs_to :disinfected_by, class_name: 'User', optional: true

  has_many :lab_docket_items, dependent: :destroy
  has_one :patient_asset, dependent: :destroy

  accepts_nested_attributes_for :lab_docket_items, reject_if: :all_blank, allow_destroy: true

  after_commit :create_patient_asset, on: %i[create update]

  def total_days
    (date_return - date_in).to_i
  rescue StandardError
    0
  end

  def days_passed
    (Time.zone.today.in_time_zone('Europe/London').to_date - date_in).to_i
  rescue StandardError
    0
  end

  def due_in_days
    days = begin
      (date_return - Time.zone.today.in_time_zone('Europe/London').to_date).to_i
    rescue StandardError
      0
    end
    days >= 0 ? "#{days} DAYS" : 'OVERDUE'
  end

  def date_in
    info = docket_info.is_a?(String) ? JSON.parse(docket_info) : docket_info
    Time.use_zone('Europe/London') { Time.zone.parse(info['date_in'].to_s) }.to_date
  rescue StandardError
    Time.zone.today
  end

  def date_return
    info = docket_info.is_a?(String) ? JSON.parse(docket_info) : docket_info
    Time.use_zone('Europe/London') { Time.zone.parse(info['date_return'].to_s) }.to_date
  rescue StandardError
    Time.zone.today
  end

  def create_patient_asset
    LabDocketJob.perform_later(id)
  end
end
