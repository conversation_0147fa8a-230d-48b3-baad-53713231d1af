# frozen_string_literal: true

# == Schema Information
#
# Table name: practices
#
#  id                          :bigint           not null, primary key
#  name                        :string
#  email                       :string
#  phone                       :string
#  address_line_1              :string
#  address_line_2              :string
#  city                        :string
#  postcode                    :string
#  primary_color               :string
#  secondary_color             :string
#  tertiary_color              :string
#  num_surgeries               :integer
#  website                     :string
#  latitude                    :decimal(, )
#  longitude                   :decimal(, )
#  opening_hours               :json
#  facebook_page_id            :string
#  facebook_page_token         :string
#  pandadoc_key                :string
#  azure_url                   :string
#  azure_token                 :string
#  stripe_account_id           :string
#  stripe_tos_accepted_at      :datetime
#  stripe_tos_accepted_ip      :string
#  stannp_key                  :string
#  sinch_project_id            :string
#  sinch_app_id                :string
#  outbound_email              :string
#  medical_history_success_url :string
#  created_at                  :datetime         not null
#  updated_at                  :datetime         not null
#  facilities                  :jsonb
#  about_us                    :text
#  expertise                   :text
#  two_factor_enabled          :boolean          default(TRUE)
#  two_factor_disabled_data    :json
#  import_id                   :string
#  import_source               :integer
#  import_data                 :jsonb
#  number_of_surgeries         :integer
#
class Practice < ApplicationRecord
  has_many :registered_devices, dependent: :destroy
  include Importable

  has_many :practices_patients, dependent: :destroy
  has_many :patients, through: :practices_patients
  has_many :practices_users, dependent: :destroy
  has_many :users, through: :practices_users
  has_many :treatment_plans, dependent: :destroy
  has_many :treatment_plan_templates, dependent: :destroy
  has_many :calendar_bookings, dependent: :destroy
  has_many :calendar_day_notes, dependent: :destroy
  has_many :calendar_reserved_block_types, dependent: :destroy
  has_many :treatment_plan_informations, dependent: :destroy
  has_many :treatment_plan_stories, dependent: :destroy
  has_many :labs, dependent: :destroy
  has_many :lab_works, dependent: :destroy
  has_many :whatsapp_templates
  has_many :communication_accounts
  has_many :sinch_numbers
  has_many :crm_boards, dependent: :destroy
  has_many :hr_templates
  has_many :actions, as: :actionable, dependent: :destroy
  has_many :alerts, dependent: :destroy
  has_many :patient_gps, dependent: :destroy
  has_many :cot_payment_plans, dependent: :destroy
  has_many :dashboard_widgets, dependent: :destroy
  has_many :holidays, dependent: :destroy
  has_many :shifts, dependent: :destroy
  has_many :group_meetings, dependent: :destroy
  has_many :announcements, dependent: :destroy
  has_many :meetings, dependent: :destroy
  has_many :tasks, dependent: :destroy
  has_many :onboarding_forms, dependent: :destroy
  has_many :documents, dependent: :destroy
  has_many :recalls, dependent: :destroy
  has_many :recall_schedules, dependent: :destroy
  has_many :cot_categories, dependent: :destroy
  has_many :letters, dependent: :destroy
  has_many :course_of_treatments, dependent: :destroy
  has_many :treatment_categories, dependent: :destroy
  has_many :treatments, dependent: :destroy

  has_one :calendar_setting, dependent: :destroy

  has_one_attached :image
  has_one_attached :favicon_image

  after_create :create_default_crm_board!
  has_one_attached :logo
  has_one_attached :estimates_first_image
  has_one_attached :estimates_second_image
  has_one_attached :stripe_terminal_logo

  after_create :setup_practice

  def full_address
    [
      address_line_1,
      address_line_2,
      city,
      'GB',
      postcode
    ].compact_blank.join(', ')
  end

  def dynamic_display_name
    name
  end

  def setup_practice
    PracticeSetupJob.perform_later(id)
  end

  def azure_configured?
    azure_url.present? && azure_token.present?
  end

  DYNAMIC_ALLOW_BLANK = false

  def create_default_crm_board!
    board = crm_boards.create!(name: "#{name} CRM Board") if crm_boards.empty?

    board.crm_lists.create!(title: 'New Enquiries', position: 0, is_default: true)
  end
end
