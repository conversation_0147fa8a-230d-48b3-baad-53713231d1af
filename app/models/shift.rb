# frozen_string_literal: true

# == Schema Information
#
# Table name: shifts
#
#  id                      :bigint           not null, primary key
#  repeat_indefinitely     :boolean          default(FALSE)
#  repeat_shift            :boolean          default(FALSE)
#  end_repeat_date         :date
#  date                    :date
#  event_start             :datetime
#  event_end               :datetime
#  surgery_id              :integer
#  user_id                 :integer
#  working_time_adjustment :integer
#  break_time_adjustment   :integer
#  overtime_adjustment     :integer
#  description             :string
#  recurrent_identifier    :string
#  working_location        :string
#  event_type              :string
#  custom_description      :string
#  status                  :string           default("scheduled")
#  repeat_days             :string           default([]), is an Array
#  alternating_shift       :string
#  practice_id             :bigint
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  shift_type              :integer          default("clinical"), not null
#
class Shift < ApplicationRecord
  belongs_to :practice, optional: true
  belongs_to :user

  validates :date, presence: true
  validates :event_start, :event_end, presence: true
  validate :check_start_end_validity
  validate :check_clashes

  has_many :shift_intervals, dependent: :destroy

  # TODO: get rid of event_type
  enum shift_type: {
    clinical: 0,
    operational: 1
  }

  scope :today, -> { where(event_start: Time.zone.today.beginning_of_day..Time.zone.today.end_of_day) }
  scope :clinical, -> { where(shift_type: :clinical) }
  scope :operational, -> { where(shift_type: :operational) }

  def duration_in_minutes
    ((event_end - event_start).to_f / 60).round if event_start && event_end
  end

  def working_time_seconds
    working_time_adjustment || shift_intervals.select(&:working?).select { |si| si.ended_at.present? }
                                              .sum { |si| si.ended_at - si.started_at }
  end

  def break_time_seconds
    break_time_adjustment || shift_intervals.select(&:break?).select { |si| si.ended_at.present? }
                                            .sum { |si| si.ended_at - si.started_at }
  end

  def overtime_seconds
    overtime_adjustment || shift_intervals.select(&:overtime?).select { |si| si.ended_at.present? }
                                          .sum { |si| si.ended_at - si.started_at }
  end

  def progress_percentage
    ((Time.zone.now - event_start) / (event_end - event_start) * 100).to_f
  end

  def assign_event_start=(value)
    return unless date.present? && value.present?

    self.event_start = Time.zone.parse("#{date} #{value} London")
  end

  def assign_event_end=(value)
    return unless date.present? && value.present?

    self.event_end = Time.zone.parse("#{date} #{value} London")
  end

  def check_start_end_validity
    return unless event_start > event_end

    errors.add(:event_start, 'should be before the end time')
  end

  def check_clashes
    clash_candidates = Shift.where(user_id: user_id, date: date, practice_id: practice_id).where.not(id: id)

    return unless clash_candidates.any? { _1.event_start < event_end && _1.event_end > event_start }

    errors.add(:event_start, 'clashes with another event')
  end

  # NOTE: temporary stub for form; TODO: remove
  def repeat_pattern; end
  def repeat_pattern=(value); end
end
