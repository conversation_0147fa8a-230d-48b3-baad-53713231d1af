# frozen_string_literal: true

module IconStateObservable
  extend ActiveSupport::Concern

  included do
    after_commit :refresh_related_icon_states, on: %i[create update destroy]
  end

  private

  def refresh_related_icon_states
    icon_state_observable_records.each do |record|
      unless record.is_a?(ChartingAppointment) || record.is_a?(CalendarBooking)
        raise "Record #{record.class.name}##{record.id} is not a ChartingAppointment or CalendarBooking"
      end

      IconStateRefresherJob.perform_async(record.class.name, record.id)
    end
  rescue StandardError => e
    Rails.logger.error "Failed to refresh icon states for #{self.class.name}##{id}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
  end

  # NOTE: override in each model to define which bookables should be refreshed
  def icon_state_observable_records
    []
  end
end
