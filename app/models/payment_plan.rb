# frozen_string_literal: true

# == Schema Information
#
# Table name: payment_plans
#
#  id                       :bigint           not null, primary key
#  plan_type                :string
#  frequency                :string
#  weekly_day               :integer
#  notes                    :text
#  patient_id               :bigint           not null
#  main_payment_method_id   :string
#  backup_payment_method_id :string
#  practice_id              :bigint
#  course_of_treatment_id   :bigint
#  invoice_id               :bigint
#  cancelled                :boolean          default(FALSE)
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#
class PaymentPlan < ApplicationRecord
  include IconStateObservable

  belongs_to :course_of_treatment, optional: true
  belongs_to :invoice, optional: true
  belongs_to :practice, optional: true
  belongs_to :patient

  has_many :payments, dependent: :destroy

  accepts_nested_attributes_for :payments, allow_destroy: true, reject_if: :reject_blank_payment

  def status
    if cancelled
      'Cancelled'
    elsif payments.succeeded.count == payments.count
      'Paid'
    else
      'Active'
    end
  end

  private

  def reject_blank_payment(attributes)
    attributes['amount'].blank? || attributes['amount'].to_f == 0.0
  end

  def icon_state_observable_records
    patient.icon_state_related_records
  end
end
