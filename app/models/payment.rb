# frozen_string_literal: true

# == Schema Information
#
# Table name: payments
#
#  id                       :bigint           not null, primary key
#  invoice_id               :bigint
#  patient_id               :bigint           not null
#  course_of_treatment_id   :bigint
#  amount                   :decimal(10, 2)
#  payment_method           :string
#  status                   :string
#  payment_type             :string
#  stripe_payment_intent_id :string
#  summary                  :text
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  payment_date             :datetime
#  name                     :string
#  currency                 :string           default("GBP")
#  transactioned_by_id      :bigint
#  payment_mean             :string
#  import_id                :string
#  import_source            :integer
#  import_data              :jsonb
#  payment_plan_id          :bigint
#  due_date                 :datetime
#  calendar_booking_id      :bigint
#
class Payment < ApplicationRecord
  include Importable
  include EventLoggable
  include IconStateObservable

  has_many :refunds

  belongs_to :patient
  belongs_to :payment_plan, optional: true
  belongs_to :transactioned_by, class_name: 'User', optional: true
  belongs_to :invoice, optional: true
  belongs_to :course_of_treatment, optional: true
  belongs_to :calendar_booking, optional: true

  PAYMENT_STATUSES = %w[pending failed succeeded refunded].freeze
  PAYMENT_TYPES = %w[full_payment installment].freeze
  PAYMENT_MEANS = %w[card terminal manual online].freeze

  validates :status, inclusion: { in: PAYMENT_STATUSES }
  validates :payment_type, inclusion: { in: PAYMENT_TYPES }
  validates :payment_mean, inclusion: { in: PAYMENT_MEANS }

  scope :succeeded, -> { where(status: 'succeeded') }
  scope :not_succeeded, -> { where.not(status: 'succeeded') }

  after_save :refresh_invoice_status
  after_create :send_payment_link, if: -> { payment_mean == 'online' }
  after_commit :notify_payment_received, on: %i[create update]
  after_commit :notify_payment_failed, on: %i[create update]

  scope :within_date, lambda { |start_date, end_date|
    where(created_at: start_date.beginning_of_day..end_date.end_of_day) if start_date && end_date
  }

  scope :payment_within_date, lambda { |start_date, end_date|
    where(payment_date: start_date.beginning_of_day..end_date.end_of_day) if start_date && end_date
  }

  def stripe_amount
    (amount * 100).to_i
  end

  def practice
    invoice&.practice || patient.current_practice
  end

  def refresh_invoice_status
    invoice&.refresh_status
  end

  def send_payment_link
    Payments::OnlineLinkSender.new(self).call
  end

  def amount_refunded
    refunds.sum(:amount)
  end

  def amount_unrefunded
    amount - amount_refunded
  end

  def notify_payment_received
    return unless status == 'succeeded'

    if previous_changes['status'] || previous_changes['id']
      description = <<~HTML.strip
        #{patient.full_name} has made a payment of £#{amount}. Please ensure the patient’s account is accurate and up to date, with all payments correctly allocated to the corresponding invoices.
      HTML
      patient.assigned_staff.uniq.each do |member|
        Notification.create(
          recipient: member,
          title: "#{patient.full_name} made a payment of £#{amount}",
          description: description,
          data: { type: 'patient_finances',
                  price: amount,
                  color: '#4D7CFE',
                  sender: patient.full_name,
                  avatar_url: patient.image&.url },
          actions: [
            { text: 'View Payment Details', primary: true, action: 'redirect', href: "/admin/patients/#{patient.id}/account" },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end
    end
  end

  def notify_payment_failed
    return unless status == 'failed'

    if previous_changes['status'] || previous_changes['id']
      description = <<~HTML.strip
        An automated payment of <strong>£#{amount}</strong> for #{patient.full_name},
        attempted on <strong>£#{created_at.strftime('%d %B')}</strong>, has failed.
        Please review the payment details and contact the patient to resolve the issue.
        Ensure any outstanding balances are settled before their next appointment.
      HTML
      patient.assigned_staff.uniq.each do |member|
        Notification.create(
          recipient: member,
          title: "Automated payment of £#{amount} failed for #{patient.full_name}",
          description: description,
          data: { type: 'patient_finances',
                  price: amount,
                  color: '#4D7CFE',
                  sender: patient.full_name,
                  avatar_url: patient.image&.url },
          actions: [
            { text: 'View Payment Details', primary: true, action: 'redirect', href: "/admin/patients/#{patient.id}/account" },
            { text: 'Mark Read', action: 'mark_as_read' },
            { text: 'Remind Me Later', action: 'remind_in' }
          ]
        )
      end
    end
  end

  private

  def icon_state_observable_records
    patient.icon_state_related_records
  end
end
