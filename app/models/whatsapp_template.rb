# frozen_string_literal: true

# == Schema Information
#
# Table name: whatsapp_templates
#
#  id               :bigint           not null, primary key
#  name             :string
#  practice_id      :bigint           not null
#  category         :string
#  submitted        :boolean          default(FALSE)
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  automations_only :boolean          default(FALSE)
#
class WhatsappTemplate < ApplicationRecord
  belongs_to :practice
  has_many :whatsapp_components, dependent: :destroy
  accepts_nested_attributes_for :whatsapp_components, allow_destroy: true, reject_if: :all_blank
  validates :category, inclusion: { in: %w[UTILITY AUTHENTICATION MARKETING] }, allow_nil: true
  validates :name, uniqueness: { scope: :practice_id }

  attr_accessor :sinch_data

  def self.where_approved(practice)
    sinch = Sinch::WhatsappService.new(practice)
    templates = sinch.whatsapp_templates
    approved_templates = templates.filter { |t| t['state'] == 'APPROVED' }

    where(practice: practice).filter do |wt|
      approved_templates.any? { |t| t['name'] == wt.sinch_name }
    end
  end

  def self.with_sinch_data(practice)
    sinch = Sinch::WhatsappService.new(practice)
    where(practice: practice).update(sinch_data: sinch.whatsapp_templates)
  end

  def self.automation_options
    all.map { |t| [t.name, t.id] }
  end

  def status
    return unless submitted

    if !sinch_data.nil?
      sinch_template = sinch_data.find { |t| t['name'] == sinch_name }
      sinch_template['state'] if sinch_template
    else
      sinch = Sinch::WhatsappService.new(practice)
      template = sinch.get_whatsapp_template(sinch_name)
      template['state'] || 'PENDING' if template
    end
  end

  def sinch_name
    name&.downcase&.gsub(/[^a-z0-9_]/, '_')
  end

  def to_template
    {
      name: sinch_name,
      language: 'EN_GB',
      category: category,
      details: {
        components: whatsapp_components.map(&:to_template)
      }
      # status: 'DRAFT'
    }
  end

  def to_message(*data)
    {
      template_message: {
        channel_template: {
          WHATSAPP: {
            template_id: sinch_name,
            language_code: 'en_GB',
            parameters: whatsapp_components.filter(&:has_tags?).map do |c|
                          c.to_parameters(data)
                        end.flatten(1).reject(&:blank?).to_h
          }
        }
      }
    }
  end

  def to_html(*data)
    whatsapp_components.map { |c| c.to_html(data) }.reject(&:blank?).join('<br/>')
  end

  def resubmit
    sinch = Sinch::WhatsappService.new(practice)

    sinch.update_whatsapp_template(to_template)
  end

  def submit
    sinch = Sinch::WhatsappService.new(practice)
    if !submitted
      response = sinch.create_whatsapp_template(to_template)
      update(submitted: true) if response[:error].blank?
    else
      sinch.create_whatsapp_template(to_template)
    end
  end

  after_destroy do
    # sinch = SinchService.new(practice)
    # sinch.delete_whatsapp_template(sinch_name)
  end
end
