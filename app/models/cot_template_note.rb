# frozen_string_literal: true

# == Schema Information
#
# Table name: cot_template_notes
#
#  id            :bigint           not null, primary key
#  title         :string           not null
#  content       :text             not null
#  user_id       :bigint
#  practice_id   :bigint
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  import_id     :string
#  import_source :integer
#  import_data   :jsonb
#
class CotTemplateNote < ApplicationRecord
  include Importable

  belongs_to :practice, optional: true

  has_many :cot_template_note_users, dependent: :destroy
  has_many :users, through: :cot_template_note_users

  validates :title, presence: true
  validates :content, presence: true

  def user_ids=(ids)
    self.users = User.where(id: ids.reject(&:blank?))
  end

  def assigned_users
    users
  end
end
