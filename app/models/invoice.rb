# frozen_string_literal: true

# == Schema Information
#
# Table name: invoices
#
#  id                     :bigint           not null, primary key
#  created_by_id          :bigint           not null
#  clinician_id           :bigint
#  patient_id             :bigint           not null
#  practice_id            :bigint           not null
#  course_of_treatment_id :bigint
#  status                 :string
#  notes                  :text
#  discount               :decimal(10, 2)
#  discount_type          :string
#  due_date               :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  import_id              :string
#  import_source          :integer
#  import_data            :jsonb
#  invoice_key            :string
#
class Invoice < ApplicationRecord
  include EventLoggable
  include Importable
  include IconStateObservable

  belongs_to :patient
  belongs_to :practice
  belongs_to :clinician, optional: true, class_name: 'User'
  belongs_to :created_by, class_name: 'User'
  belongs_to :course_of_treatment, optional: true

  has_many :payments
  has_many :invoice_items

  STATUSES = %w[unpaid part-paid paid cancelled].freeze
  DISCOUNT_TYPES = %w[percentage fixed].freeze

  accepts_nested_attributes_for :invoice_items, allow_destroy: true

  validates :status, inclusion: { in: STATUSES }

  scope :not_paid, -> { where.not(status: 'paid').where.not(status: 'cancelled') }

  after_create :generate_invoice_key

  after_save :trigger_automations

  def refresh_status
    if amount_due.zero?
      update(status: 'paid')
    elsif amount_due < total_discounted
      update(status: 'part-paid')
    else
      update(status: 'unpaid')
    end
  end

  def discount_amount
    case discount_type
    when 'percentage'
      total * discount / 100
    when 'fixed'
      discount
    else
      0
    end
  end

  def total_discounted
    total - discount_amount
  end

  def amount_due
    total_discounted - payments_succeeded_sum + refunds_total
  end

  def refunds_total
    if payments.loaded?
      payments.select { |p| p.status == 'succeeded' }.sum do |payment|
        if payment.refunds.loaded?
          payment.refunds.sum(&:amount)
        else
          payment.refunds.sum(:amount)
        end
      end
    else
      payments.succeeded.sum { |payment| payment.refunds.sum(&:amount) }
    end
  end

  def total
    invoice_items.sum(&:total)
  end

  def paid?
    status == 'paid'
  end

  def overdue?
    !paid? && due_date.present? && due_date < Time.current
  end

  def trigger_automations
    if saved_changes[:id] # creation
      Automations::TriggerProcessor.call(model: self, event_type: 'invoice_created')
    elsif saved_changes[:status] && saved_changes[:status].first != 'paid' && saved_changes[:status].last == 'paid'
      Automations::TriggerProcessor.call(model: self, event_type: 'invoice_paid')
    end
  end

  private

  def generate_invoice_key
    self.invoice_key ||= SecureRandom.urlsafe_base64(32)
    save
  end

  def payments_succeeded_sum
    if payments.loaded?
      payments.select { |p| p.status == 'succeeded' }.sum(&:amount)
    else
      payments.succeeded.sum(:amount)
    end
  end

  def icon_state_observable_records
    patient.icon_state_related_records
  end
end
