# frozen_string_literal: true

# == Schema Information
#
# Table name: icon_states
#
#  id                      :bigint           not null, primary key
#  calendar_booking_id     :bigint
#  charting_appointment_id :bigint
#  lab_work_state          :integer          default("blank"), not null
#  payments_state          :integer          default("blank"), not null
#  documents_state         :integer          default("blank"), not null
#  complaints_state        :integer          default("blank"), not null
#  medical_history_state   :integer          default("blank"), not null
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#
class IconState < ApplicationRecord
  STATES = {
    blank: 0,
    pending: 1,
    positive: 2,
    negative: 3
  }.freeze

  belongs_to :calendar_booking, optional: true
  belongs_to :charting_appointment, optional: true

  enum :lab_work_state, STATES, prefix: true
  enum :payments_state, STATES, prefix: true
  enum :documents_state, STATES, prefix: true
  enum :complaints_state, STATES, prefix: true
  enum :medical_history_state, STATES, prefix: true

  validates :calendar_booking_id, presence: true, if: -> { charting_appointment_id.blank? }
  validates :charting_appointment_id, presence: true, if: -> { calendar_booking_id.blank? }
end
