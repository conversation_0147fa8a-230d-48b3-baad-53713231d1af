# frozen_string_literal: true

# == Schema Information
#
# Table name: users
#
#  id                            :bigint           not null, primary key
#  email                         :string           default(""), not null
#  encrypted_password            :string           default(""), not null
#  reset_password_token          :string
#  reset_password_sent_at        :datetime
#  remember_created_at           :datetime
#  provider                      :string           default("email"), not null
#  uid                           :string           default(""), not null
#  tokens                        :text
#  first_name                    :string
#  last_name                     :string
#  created_at                    :datetime         not null
#  updated_at                    :datetime         not null
#  archived                      :boolean          default(FALSE)
#  mobile_phone                  :string
#  title                         :string
#  mfa_method                    :string           default("SMS")
#  pin                           :string
#  initial_holiday_allowance     :integer
#  holiday_year_start_month      :integer
#  breaks                        :boolean          default(FALSE)
#  break_duration                :integer
#  break_interval                :integer
#  breaks_paid                   :boolean          default(FALSE)
#  diagnocat_email               :string
#  diagnocat_password            :string
#  diagnocat_password_ciphertext :text
#  login_restrictions            :jsonb
#  accrue_holidays               :boolean          default(FALSE)
#  gdc_number                    :string
#  show_treatment_plans          :boolean          default(TRUE)
#  override_pin                  :string
#  auto_privacy_enabled          :boolean          default(TRUE)
#  auto_logout_enabled           :boolean          default(TRUE)
#  privacy_timeout               :integer          default(15)
#  logout_timeout                :integer          default(30)
#  address_line_1                :string
#  address_line_2                :string
#  import_id                     :string
#  import_source                 :integer
#  import_data                   :jsonb
#  online_booking_description    :string
#  lab_user                      :boolean          default(FALSE), not null
#
class User < ApplicationRecord
  CLINICIAN_PERMISSION_ID = 16 # NOTE: ChartedTreatment creation permission

  include DeviseTokenAuth::Concerns::User
  include Importable

  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable, :omniauthable,
         omniauth_providers: %i[google_oauth2 microsoft_office365 facebook]

  has_one_attached :image
  has_many :roles_users, inverse_of: :user, dependent: :destroy
  has_many :roles, through: :roles_users, dependent: :destroy

  validates :first_name, presence: true
  validates :last_name, presence: true
  validates :email, presence: true, uniqueness: { case_sensitive: false }
  validates :pin, confirmation: true, if: -> { mfa_method == 'pin' && pin.present? }
  validates :mobile_phone, presence: true, if: -> { %w[sms SMS].include?(mfa_method) }

  # Privacy screen settings
  attribute :auto_privacy_enabled, :boolean, default: true
  attribute :auto_logout_enabled, :boolean, default: true
  attribute :privacy_timeout, :integer, default: 15
  attribute :logout_timeout, :integer, default: 30

  attr_accessor :skip_password_validation, :pin_confirmation

  has_many :practices_users, dependent: :destroy
  has_many :practices, through: :practices_users
  has_many :notifications, as: :recipient

  has_many :patients_users, dependent: :destroy
  has_many :assigned_patients, through: :patients_users, source: :patient

  has_many :recalls # NOTE: created by
  has_and_belongs_to_many :recalls, join_table: 'recall_dentists', foreign_key: :dentist_id

  has_many :recall_schedules # NOTE: created by
  has_and_belongs_to_many :recall_schedules, join_table: 'recall_schedules_dentists', foreign_key: :dentist_id

  has_many :practitioner_bookings, class_name: 'CalendarBooking', foreign_key: 'practitioner_id'
  has_many :booked_bookings, as: :booked_by, class_name: 'CalendarBooking'
  has_many :calendar_day_notes, dependent: :destroy
  has_many :calendar_reserved_blocks

  has_many :manager_group_meetings, class_name: 'GroupMeeting', foreign_key: 'manager_id'
  has_and_belongs_to_many :group_meetings,
                          class_name: 'GroupMeeting',
                          foreign_key: 'group_meeting_id',
                          join_table: 'group_meeting_participants'

  has_many :shifts
  has_many :documents
  has_many :holidays
  has_many :tasks
  belongs_to :group, optional: true

  has_many :treatment_plan_templates, dependent: :destroy

  has_many :created_treatment_plan_options,
           class_name: 'TreatmentPlanOption',
           foreign_key: 'created_by_id',
           inverse_of: :created_by,
           dependent: :nullify
  has_many :coordinated_treatment_plan_options,
           class_name: 'TreatmentPlanOption',
           foreign_key: 'treatment_coordinator_id',
           inverse_of: :treatment_coordinator,
           dependent: :nullify
  has_many :clinician_treatment_plan_options,
           class_name: 'TreatmentPlanOption',
           foreign_key: 'main_clinician_id',
           inverse_of: :main_clinician,
           dependent: :nullify

  has_many :disinfected_lab_docket_items, class_name: 'LabDocketItem', foreign_key: 'disinfected_by_id'
  has_many :practitioner_lab_works, class_name: 'LabWork', foreign_key: 'practitioner_id', inverse_of: :practitioner
  has_many :checked_in_lab_works, class_name: 'LabWork', foreign_key: 'checked_in_by_id', inverse_of: :checked_in_by
  has_many :quality_checked_lab_works, class_name: 'LabWork', foreign_key: 'quality_checked_by_id',
                                       inverse_of: :quality_checked_by
  has_many :lab_work_files, foreign_key: :created_by_id, dependent: :destroy
  has_many :lab_work_logs, class_name: 'LabWorkLog', foreign_key: :created_by_id, dependent: :destroy

  has_many :developed_xrays, class_name: 'Xray', foreign_key: :developed_by_id, dependent: :destroy
  has_many :taken_xrays, class_name: 'Xray', foreign_key: :taken_by_id, dependent: :destroy
  has_many :developed_cbcts, class_name: 'Cbct', foreign_key: :developed_by_id, dependent: :destroy
  has_many :taken_cbcts, class_name: 'Cbct', foreign_key: :taken_by_id, dependent: :destroy
  has_many :developed_opgs, class_name: 'Opg', foreign_key: :developed_by_id, dependent: :destroy
  has_many :taken_opgs, class_name: 'Opg', foreign_key: :taken_by_id, dependent: :destroy
  has_many :developed_clinical_images, class_name: 'ClinicalImage', foreign_key: :developed_by_id, dependent: :destroy
  has_many :taken_clinical_images, class_name: 'ClinicalImage', foreign_key: :taken_by_id, dependent: :destroy

  scope :for_practice, ->(practice) { joins(:practices).where(practices: { id: practice.id }) }
  scope :without_archived, -> { where(archived: false) }
  scope :archived, -> { where(archived: true) }
  scope :clinicians, -> { joins(:roles).where('roles.admin = TRUE OR ? = ANY(roles.permissions)', CLINICIAN_PERMISSION_ID).distinct }
  scope :nurses, -> { joins(:roles).where(roles: { name: 'Nurse' }).distinct }
  scope :fuzzy_search, lambda { |query|
    where("similarity(CONCAT_WS(' ', first_name, last_name), :q) > 0.2", q: query)
  }

  has_many :communication_account_users
  has_many :communication_accounts, through: :communication_account_users

  has_many :created_hr_templates, class_name: 'HrTemplate', foreign_key: 'created_by_id', inverse_of: :created_by
  has_and_belongs_to_many :hr_templates, join_table: 'hr_templates_users' # HR Template Assignees
  has_many :user_favorite_reports, dependent: :destroy
  has_many :user_favorite_treatments, dependent: :destroy
  has_many :favorite_treatments, through: :user_favorite_treatments, source: :treatment

  has_many :patient_notes, dependent: :destroy

  has_many :dashboards, dependent: :destroy
  has_many :dashboard_widgets, through: :dashboards

  has_many :letters

  def full_name
    [first_name, last_name].join(' ')
  end

  def full_name_with_email
    "#{full_name} (#{email})"
  end

  def full_name_with_title
    "#{title} #{full_name}"
  end

  def initials
    [first_name.to_s[0], last_name.to_s[0]].join.upcase
  end

  def confirmation_required?
    false
  end

  def has_permission?(resource, activity)
    roles.any? do |role|
      role.admin? || role.permissions.include?(PERMISSIONS[resource.to_s][:activities][activity.to_s][:id])
    end
  end

  def admin?
    roles.exists?(admin: true)
  end

  def clinician?
    roles.exists?(['admin = TRUE OR ? = ANY(roles.permissions)', CLINICIAN_PERMISSION_ID])
  end

  def lab_user?
    lab_user
  end

  def favorite_report_paths
    user_favorite_reports.pluck(:report_path)
  end

  def self.ransackable_scopes(_auth_object = nil)
    super + [:fuzzy_search]
  end

  def self.ransackable_associations(_auth_object = nil)
    []
  end

  def self.ransackable_attributes(_auth_object = nil)
    %w[first_name last_name email archived] + _ransackers.keys
  end

  # TODO: refactor this method
  def holiday_allowance(date = Time.zone.today)
    UserHolidayAllowanceCalculator.new(self, date).call
  end

  def current_shift
    time_now = Time.zone.now

    Shift.today.where(user_id: id).where('event_start <= ?', time_now).order(event_start: :desc).first ||
      Shift.today.where(user_id: id).where('event_start > ?', time_now).order(event_start: :asc).first
  end

  protected

  def password_required?
    return false if skip_password_validation

    super
  end
end
