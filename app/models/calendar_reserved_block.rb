# frozen_string_literal: true

# == Schema Information
#
# Table name: calendar_reserved_blocks
#
#  id                              :bigint           not null, primary key
#  calendar_reserved_block_type_id :bigint           not null
#  practitioner_id                 :bigint           not null
#  start_time                      :datetime         not null
#  end_time                        :datetime         not null
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  recurrent_identifier            :string
#
class CalendarReservedBlock < ApplicationRecord
  belongs_to :calendar_reserved_block_type, optional: false
  belongs_to :practitioner, class_name: 'User', optional: false

  scope :between_dates, lambda { |start_date, end_date|
                          where('DATE(start_time) >= ? AND DATE(end_time) <= ?', start_date, end_date)
                        }

  def self.find_by_times(start_time, end_time, practitioner_id)
    where(
      'start_time < ? AND end_time > ? AND practitioner_id = ?',
      end_time,
      start_time,
      practitioner_id
    ).first
  end

  def start_time_formatted
    start_time.strftime('%H:%M')
  end

  def end_time_formatted
    end_time.strftime('%H:%M')
  end

  def start_time
    read_attribute(:start_time).in_time_zone
  end

  def end_time
    read_attribute(:end_time).in_time_zone
  end

  def duration_in_minutes
    ((end_time - start_time) / 60).to_i
  end

  def times_formatted
    "#{start_time_formatted}-#{end_time_formatted}"
  end

  def times_formatted_p
    "#{start_time.strftime('%I:%M %P')} - #{end_time.strftime('%I:%M %P')}"
  end
end
