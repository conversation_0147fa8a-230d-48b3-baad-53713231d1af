# frozen_string_literal: true

# == Schema Information
#
# Table name: lab_works
#
#  id                      :bigint           not null, primary key
#  patient_id              :bigint           not null
#  lab_id                  :bigint           not null
#  status                  :string           default("Sent To Lab")
#  practitioner_id         :bigint           not null
#  charting_appointment_id :bigint
#  invoice_data            :json
#  code                    :string
#  code_key                :string
#  checked_in_by_id        :bigint
#  quality_checked_by_id   :bigint
#  practice_id             :bigint           not null
#  total                   :decimal(10, 2)
#  arrived_at              :datetime
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#
class LabWork < ApplicationRecord
  include EventLoggable
  include IconStateObservable

  has_many :lab_dockets, dependent: :destroy
  has_many :lab_work_files, dependent: :destroy
  has_many :lab_work_logs, dependent: :destroy

  belongs_to :patient
  belongs_to :lab
  belongs_to :practitioner, class_name: 'User'
  belongs_to :practice
  belongs_to :charting_appointment, optional: true
  belongs_to :checked_in_by, class_name: 'User', optional: true
  belongs_to :quality_checked_by, class_name: 'User', optional: true

  has_one_attached :invoice

  after_initialize :set_code_key

  scope :sent_to_lab, lambda {
    where(status: 'Sent To Lab')
      .includes(:lab_dockets, :patient, :practitioner, :charting_appointment, :lab).select do |lab_work|
      latest = lab_work.lab_dockets.last
      next false unless latest

      info = latest.docket_info
      info = JSON.parse(info) if info.is_a?(String)

      date_return = begin
        Time.use_zone('Europe/London') { Time.zone.parse(info['date_return'].to_s) }.to_date
      rescue StandardError
        nil
      end

      date_return.present? && date_return > Date.current
    end
  }

  scope :dentist_check_required, lambda {
    where(status: 'Arrived At Practice - Dentist Check Required')
      .includes(:lab_dockets, :patient, :practitioner, :charting_appointment, :lab)
      .select do |lab_work|
      latest = lab_work.lab_dockets.last
      next false unless latest

      info = latest.docket_info
      info = JSON.parse(info) if info.is_a?(String)

      date_return = begin
        Time.use_zone('Europe/London') { Time.zone.parse(info['date_return'].to_s) }.to_date
      rescue StandardError
        nil
      end

      date_return.present? && date_return > Date.current
    end
  }

  scope :ready_for_appointment, lambda {
    where(status: 'Checked By Dentist - Ready For Patient')
      .includes(:lab_dockets, :patient, :practitioner, :charting_appointment, :lab)
      .select do |lab_work|
      latest = lab_work.lab_dockets.last
      next false unless latest

      info = latest.docket_info
      info = JSON.parse(info) if info.is_a?(String)

      date_return = begin
        Time.use_zone('Europe/London') { Time.zone.parse(info['date_return'].to_s) }.to_date
      rescue StandardError
        nil
      end

      date_return.present? && date_return > Date.current
    end
  }

  scope :adjustment_required, lambda {
    where(status: ['Adjustment Required (Amend or Create New Lab Docket)', 'Lab Error - Adjustment or Remake Required'])
      .includes(:lab_dockets, :patient, :practitioner, :charting_appointment, :lab)
      .select do |lab_work|
      latest = lab_work.lab_dockets.last
      next false unless latest

      info = latest.docket_info
      info = JSON.parse(info) if info.is_a?(String)

      date_return = begin
        Time.use_zone('Europe/London') { Time.zone.parse(info['date_return'].to_s) }.to_date
      rescue StandardError
        nil
      end

      date_return.present? && date_return > Date.current
    end
  }

  scope :due_today, lambda {
    where.not(status: ['Archive Case - Not proceeding', 'Fitted Successfully - Auto Archive'])
         .includes(:lab_dockets, :patient, :practitioner, :charting_appointment, :lab)
         .select do |lab_work|
      latest = lab_work.lab_dockets.last
      next false unless latest

      info = latest.docket_info
      info = JSON.parse(info) if info.is_a?(String)

      date_return = begin
        Time.use_zone('Europe/London') { Time.zone.parse(info['date_return'].to_s) }.to_date
      rescue StandardError
        nil
      end

      date_return.present? && date_return == Date.current
    end
  }

  scope :late, lambda {
    where.not(status: ['Archive Case - Not proceeding', 'Fitted Successfully - Auto Archive'])
         .includes(:lab_dockets, :patient, :practitioner, :charting_appointment, :lab)
         .select do |lab_work|
      latest = lab_work.lab_dockets.last
      next false unless latest

      info = latest.docket_info
      info = JSON.parse(info) if info.is_a?(String)

      date_return = begin
        Time.use_zone('Europe/London') { Time.zone.parse(info['date_return'].to_s) }.to_date
      rescue StandardError
        nil
      end

      date_return.present? && date_return < Date.current
    end
  }

  def all_related_users
    (patient.assigned_staff + [practitioner]).uniq
  end

  def set_code_key
    self.code_key = SecureRandom.hex(64) if id.nil? && code_key.nil?
  end

  def self.ransackable_attributes(_auth_object = nil)
    %w[
      id
      status
      practice_id
      lab_id
      practitioner_id
      patient_id
      created_at
      updated_at
    ]
  end

  def self.ransackable_associations(_auth_object = nil)
    %w[
      lab
      practitioner
      patient
    ]
  end

  private

  def icon_state_observable_records
    patient.icon_state_related_records
  end
end
