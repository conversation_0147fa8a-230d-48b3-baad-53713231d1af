# frozen_string_literal: true

# == Schema Information
#
# Table name: conversation_messages
#
#  id              :bigint           not null, primary key
#  conversation_id :bigint           not null
#  content         :text
#  message_type    :string
#  from            :string
#  to              :string
#  source          :string
#  author_id       :integer
#  category        :string
#  delivery_status :string
#  scheduled       :datetime
#  read_at         :datetime
#  email_subject   :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  label           :string
#  external_id     :string
#  lab_work_id     :bigint
#  reader_id       :integer
#  import_id       :string
#  import_source   :integer
#  import_data     :jsonb
#  failure_reason  :text
#
class ConversationMessage < ApplicationRecord
  include Importable

  belongs_to :conversation
  has_many_attached :attachments
  belongs_to :reader, class_name: 'User', optional: true
  belongs_to :author, class_name: 'User', optional: true
  has_many :read_receipts, class_name: 'MessageReadReceipt', dependent: :destroy
  has_many :readers, through: :read_receipts, source: :user

  # Ransack configuration
  def self.ransackable_associations(_auth_object = nil)
    %w[conversation reader author read_receipts readers]
  end

  scope :unread, -> { where(read_at: nil).includes([:read_receipts]) }
  scope :unread_inbound, -> { unread.where(message_type: 'inbound') }

  after_create :create_notifications
  after_update :send_delivery_status_update, if: -> { saved_change_to_delivery_status? }

  # TODO: sources enum: 'WHATSAPP', 'SMS', 'RCS', 'email'

  def create_notifications
    return if outbound?

    users = if internal?
              conversation.internal_recipients.where.not(id: author_id)
            elsif inbound? && conversation.patient.present?
              conversation.patient.assigned_staff
            else
              []
            end

    users.each do |user|
      Notification.create(
        recipient: user,
        title: "New #{source&.downcase} from #{conversation.name}",
        description: content,
        icon: 'envelope',
        actions: [
          { text: 'Reply', primary: true, action: 'redirect', href: "/admin/conversations/#{conversation.id}" },
          { text: 'Mark Read', action: 'mark_as_read' }
        ],
        data: {
          type: source&.downcase,
          sender: conversation.name,
          subject: email_subject,
          avatar_url: conversation.patient&.image&.url
        }
      )
    end
  end

  def inbound?
    message_type == 'inbound'
  end

  def internal?
    message_type == 'internal'
  end

  def outbound?
    message_type == 'outbound'
  end

  def formatted_timestamp
    created_at.strftime('%d %b %Y %H:%M')
  end

  def sender_name
    from || User.find_by(id: author_id)&.full_name || conversation.name || 'Unknown Sender'
  end

  # Method to access author as user for avatar compatibility
  # This is used by the user_avatar partial
  def user
    author
  end

  def mark_as_read!(user = nil)
    return false unless user

    # Create a read receipt for this user
    read_receipts.find_or_create_by(user: user) do |receipt|
      receipt.read_at = Time.current
    end

    # For backwards compatibility, also update the message's read_at if it's nil
    update(read_at: Time.current, reader_id: user&.id) if read_at.nil?

    true
  end

  def mark_as_unread!
    # Remove all read receipts for this message
    read_receipts.destroy_all

    # For backwards compatibility
    update(read_at: nil, reader_id: nil) unless read_at.nil?
  end

  # Returns a formatted list of readers
  def readers_list
    MessageReadReceipt.readers_text(id)
  end

  def send_pusher_update
    pusher = PusherService.new
    pusher.trigger(
      "private-conversation-#{conversation_id}",
      'new_message',
      {
        message_id: id,
        conversation_id: conversation_id
      }
    )
  end

  def send_delivery_status_update
    pusher = PusherService.new
    pusher.trigger(
      "private-conversation-#{conversation_id}",
      'delivery_status_update',
      {
        message_id: id,
        conversation_id: conversation_id,
        delivery_status: delivery_status,
        failure_reason: failure_reason
      }
    )
  end

  def self.ransackable_attributes(_auth)
    %w[content]
  end

  def self.ransortable_attributes(_auth)
    %w[created_at]
  end

  # Avatar display logic methods
  def system_generated?
    # System messages are those without an author (author_id is nil)
    # or sent by the system user
    author_id.nil?
  end

  def person_sent?
    !system_generated?
  end

  def system_user_message?
    return false if author_id.blank?

    # Check if the author is the system user
    author&.email == '<EMAIL>'
  end

  def display_avatar_type
    system_generated? ? :practice_logo : :user_avatar
  end
end
