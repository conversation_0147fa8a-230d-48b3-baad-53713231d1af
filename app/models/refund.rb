# frozen_string_literal: true

# == Schema Information
#
# Table name: refunds
#
#  id               :bigint           not null, primary key
#  amount           :decimal(, )
#  payment_id       :bigint           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  stripe_refund_id :string
#
class Refund < ApplicationRecord
  include IconStateObservable

  belongs_to :payment

  after_create do
    # r = Stripe::Refund.create({
    #                             payment_intent: payment.stripe_payment_intent_id,
    #                             amount: amount.to_i
    #                           }, { stripe_account: payment.practice.stripe_account_id })
    # update(stripe_refund_id: r.id)

    payment.update(status: 'refunded') if payment.refunds.sum(:amount) == payment.amount
  end

  private

  def icon_state_observable_records
    payment.patient.icon_state_related_records
  end
end
