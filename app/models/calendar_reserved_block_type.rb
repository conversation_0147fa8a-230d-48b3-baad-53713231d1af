# frozen_string_literal: true

# == Schema Information
#
# Table name: calendar_reserved_block_types
#
#  id                                  :bigint           not null, primary key
#  calendar_setting_id                 :bigint           not null
#  treatment_id                        :bigint           not null
#  practice_id                         :bigint           not null
#  no_patient_required                 :boolean          default(FALSE)
#  indicates_new                       :boolean          default(FALSE)
#  color                               :string           not null
#  created_at                          :datetime         not null
#  updated_at                          :datetime         not null
#  import_id                           :string
#  import_source                       :integer
#  import_data                         :jsonb
#  online_bookable                     :boolean
#  deposit_required                    :boolean
#  only_bookable_within_reserved_slots :boolean
#  new_patient_deposit_size            :decimal(, )
#  user_ids                            :integer          is an Array
#  old_patient_deposit_size            :decimal(, )
#  treatment_description               :text
#
class CalendarReservedBlockType < ApplicationRecord
  include Importable

  belongs_to :calendar_setting
  belongs_to :treatment
  belongs_to :practice

  has_many :calendar_reserved_blocks, dependent: :destroy

  accepts_nested_attributes_for :calendar_reserved_blocks, allow_destroy: true

  validate :practice_consistency

  before_save :set_practice_from_calendar_setting

  def treatment_name
    treatment.patient_friendly_name
  end

  def deposit_amount_for_patient(patient)
    return 0 unless deposit_required?

    # NOTE: different deposit amounts for new and old patients
    if !patient.course_of_treatments.exists?
      new_patient_deposit_size || 0
    else
      old_patient_deposit_size || 0
    end
  end

  private

  def set_practice_from_calendar_setting
    self.practice_id = calendar_setting.practice_id if calendar_setting && practice_id.blank?
  end

  def practice_consistency
    return unless calendar_setting && practice

    errors.add(:practice_id, "must match the calendar setting's practice") if calendar_setting.practice_id != practice_id
  end
end
