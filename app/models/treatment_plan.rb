# frozen_string_literal: true

# == Schema Information
#
# Table name: treatment_plans
#
#  id             :bigint           not null, primary key
#  name           :string
#  practice_id    :bigint
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  import_id      :string
#  import_source  :integer
#  import_data    :jsonb
#  status         :integer          default("pending")
#  signature_date :datetime
#
class TreatmentPlan < ApplicationRecord
  include EventLoggable
  include Importable

  belongs_to :practice
  has_many :treatment_plan_options, -> { order(created_at: :asc) }, inverse_of: :treatment_plan, dependent: :destroy
  has_many :patients, through: :treatment_plan_options

  scope :ready_to_send, lambda {
    joins(:treatment_plan_options)
      .where(status: :pending)
      .where(treatment_plan_options: { status: 'New' })
      .distinct
  }

  scope :sent_for_viewing, lambda {
    joins(:treatment_plan_options)
      .where(status: :pending)
      .where(treatment_plan_options: { status: 'Sent for Viewing' })
      .distinct
  }

  scope :signed, lambda {
    joins(:treatment_plan_options)
      .where(status: :pending)
      .where(treatment_plan_options: { status: 'Accepted' })
      .distinct
  }

  enum status: {
    pending: 0,
    completed: 1,
    declined: 2,
    archived: 3,
    information_required: 4
  }

  def patient
    patients.first
  end

  def patient_id
    patients.first&.id
  end

  def accepted_options
    treatment_plan_options.where(status: 'Accepted')
  end

  def self.ransackable_attributes(_auth_object = nil)
    %w[name date_range]
  end

  def self.ransackable_associations(_auth_object = nil)
    ['treatment_plan_options']
  end

  ransacker :date_range do
    Arel.sql('DATE(treatment_plans.created_at)')
  end

  def send_to_patient(sent_by, type)
    conversation = patient.conversation
    practice = patient.practices.first # TODO: Use active practice
    sender = practice.communication_accounts.where(account_type: type).first

    template = NotificationTemplate.find_by(
      practice: conversation.practice,
      default: true,
      delivery_method: type,
      name: 'Send Treatment Plan Link'
    )

    subject = ::Notifications::PlaceholderConverter.convert(self, template.subject)
    text    = ::Notifications::PlaceholderConverter.convert(self, template.text)
    title   = ::Notifications::PlaceholderConverter.convert(self, template.title)

    service = Conversations::MessageCreationService.new(
      conversation,
      sent_by.id,
      text,
      {
        subject: subject,
        title: title,
        sender_id: sender.id
      }
    )
    service.execute
  end
end
