<!DOCTYPE html>
<html>
<head>
  <title>PDF Testing - SecureSend</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 40px 20px;
      line-height: 1.6;
      color: #333;
    }
    
    .header {
      text-align: center;
      margin-bottom: 40px;
      padding-bottom: 20px;
      border-bottom: 2px solid #e0e0e0;
    }
    
    .header h1 {
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    .header p {
      color: #7f8c8d;
      font-size: 18px;
    }
    
    .test-section {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 30px;
      margin-bottom: 30px;
      border-left: 4px solid #3498db;
    }
    
    .test-section h2 {
      color: #2c3e50;
      margin-top: 0;
      margin-bottom: 15px;
    }
    
    .test-section p {
      margin-bottom: 20px;
      color: #555;
    }
    
    .button-group {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
    }
    
    .btn {
      display: inline-block;
      padding: 12px 24px;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.2s ease;
      border: none;
      cursor: pointer;
      font-size: 14px;
    }
    
    .btn-primary {
      background-color: #3498db;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #2980b9;
      transform: translateY(-1px);
    }
    
    .btn-secondary {
      background-color: #95a5a6;
      color: white;
    }
    
    .btn-secondary:hover {
      background-color: #7f8c8d;
      transform: translateY(-1px);
    }
    
    .btn-success {
      background-color: #27ae60;
      color: white;
    }
    
    .btn-success:hover {
      background-color: #229954;
      transform: translateY(-1px);
    }
    
    .note {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 6px;
      padding: 15px;
      margin-top: 30px;
    }
    
    .note strong {
      color: #856404;
    }
    
    .note p {
      margin: 5px 0 0 0;
      color: #856404;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🔒 SecureSend PDF Testing</h1>
    <p>Test the new WickedPDF implementation for signature and audit pages</p>
  </div>

  <div class="test-section">
    <h2>📝 Signature Page</h2>
    <p>Test the signature certificate page that shows patient information, signature, and document data.</p>
    <div class="button-group">
      <%= link_to "View HTML", "/test_pdf/signature_page", class: "btn btn-secondary", target: "_blank" %>
      <%= link_to "Download PDF", "/test_pdf/signature_page.pdf", class: "btn btn-primary" %>
    </div>
  </div>

  <div class="test-section">
    <h2>📋 Audit Trail</h2>
    <p>Test the audit trail page that shows signing information, verification methods, and device details.</p>
    <div class="button-group">
      <%= link_to "View HTML", "/test_pdf/audit_trail", class: "btn btn-secondary", target: "_blank" %>
      <%= link_to "Download PDF", "/test_pdf/audit_trail.pdf", class: "btn btn-primary" %>
    </div>
  </div>

  <div class="test-section">
    <h2>📄 Full Document</h2>
    <p>Test the complete PDF generation including cover page, content, signature page, and audit trail.</p>
    <div class="button-group">
      <%= link_to "Download Full PDF", "/test_pdf/full_document", class: "btn btn-success" %>
    </div>
  </div>

  <div class="note">
    <strong>Note:</strong>
    <p>These test routes are only available in development mode. The PDFs use sample data for testing the layout and styling.</p>
  </div>
</body>
</html>
