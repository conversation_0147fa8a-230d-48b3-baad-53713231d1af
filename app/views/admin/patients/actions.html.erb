<%= render "admin/patients/header", tab: "actions" %>
<div class="min-h-screen bg-gray-50/30 p-4">

  <div class="mx-auto">
    <div class="mb-8 flex items-center justify-between">
      <h1 class="text-3xl font-semibold tracking-tight text-gray-900"></h1>
      <div class="flex items-center gap-3">
        <button id="open-new-action-modal" type="button"
          class="inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 bg-orange-200 hover:bg-orange-300 text-orange-800 font-medium px-6 py-2.5 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md text-sm">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus w-4 h-4 mr-2">
            <path d="M5 12h14"></path>
            <path d="M12 5v14"></path>
          </svg>
          New Action
        </button>
      </div>
    </div>

    <!-- Top Level Filter Tabs -->
    <div class="mb-6">
      <div class="inline-flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
              <button id="patient-all-actions-tab"
                class="patient-top-filter-btn active-filter min-w-[36px] h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden pl-3 pr-4 text-purple-800"
                style="background: linear-gradient(to right, #e9d5ff, #d8b4fe);"
                data-filter="all"
                data-color="purple">
          <i class="fa-light fa-list h-4 w-4 text-purple-700"></i>
          <span class="patient-tab-text text-xs font-medium tracking-wide ml-1.5 transition-all duration-300">All Actions</span>
        </button>
        <button id="patient-today-tab"
                class="patient-top-filter-btn min-w-[36px] h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden px-3 bg-white text-gray-600 hover:bg-gray-50"
                data-filter="today"
                data-color="blue">
          <i class="fa-light fa-calendar-day h-4 w-4"></i>
          <span class="patient-tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Today</span>
        </button>

        <button id="patient-completed-tab"
                class="patient-top-filter-btn min-w-[36px] h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden px-3 bg-white text-gray-600 hover:bg-gray-50"
                data-filter="completed"
                data-color="green">
          <i class="fa-light fa-check-circle h-4 w-4"></i>
          <span class="patient-tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Completed</span>
        </button>
      </div>
    </div>

    <!-- Filters Section -->
    <div id="actions-container" class="">
      <div class="flex justify-between items-center mb-4">
        <div class="flex-1 flex items-center space-x-3">
          <div class="relative flex-1 max-w-md">
            <input placeholder="Search actions..." id="action-search-input"
              class="w-full px-3 py-2.5 pl-9 border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm bg-white/80 backdrop-blur-sm shadow-sm"
              type="text" />
            <svg xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none"
              viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
            <button id="patient-all-action-tab" class="patient-filter-btn active-filter min-w-[36px] pl-3 pr-4 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden text-indigo-800 filter-btn-indigo-active" style="background: linear-gradient(to right, #c7d2fe, #a5b4fc);" data-filter="all" data-color="indigo">
              <i class="fa-light fa-list h-4 w-4 text-indigo-700"></i>
              <span class="patient-tab-text text-xs font-medium tracking-wide ml-1.5">All</span>
            </button>

            <button id="patient-task-action-tab" class="patient-filter-btn bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden" data-filter="task" data-color="blue">
              <i class="fa-light fa-check-circle h-4 w-4"></i>
              <span class="patient-tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Tasks</span>
            </button>

            <button id="patient-reminder-action-tab" class="patient-filter-btn bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden" data-filter="reminder" data-color="amber">
              <i class="fa-light fa-clock h-4 w-4"></i>
              <span class="patient-tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Reminders</span>
            </button>

            <button id="patient-alerts-action-tab" class="patient-filter-btn bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden" data-filter="alerts" data-color="red">
              <i class="fa-light fa-bell h-4 w-4"></i>
              <span class="patient-tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Alerts</span>
            </button>

            <button id="patient-callback-action-tab" class="patient-filter-btn bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden" data-filter="callback" data-color="green">
              <i class="fa-light fa-phone h-4 w-4"></i>
              <span class="patient-tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Callbacks</span>
            </button>

            <button id="patient-complaint-action-tab" class="patient-filter-btn bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden" data-filter="complaint" data-color="orange">
              <i class="fa-light fa-comment-exclamation h-4 w-4"></i>
              <span class="patient-tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Complaints</span>
            </button>
          </div>
        </div>
      </div>
    </div>



    <%= admin_table(@actions, actions_table_columns, {
      empty_message: 'No actions found',
      show_actions: true,
      table_class: 'w-full',
      header_class: 'text-xs font-medium text-gray-500 uppercase tracking-wider',
      cell_class: 'text-sm text-gray-600',
      actions_class: 'flex justify-end space-x-1.5 items-center',
      row_class: 'group bg-white shadow-sm rounded-xl hover:shadow-lg transition-all duration-300 ease-in-out border border-gray-200/75 hover:border-gray-300 action-row',
      row_data: ->(action) { { "data-action-id": action.id, "data-action-type": action.action_type, "data-action-date": action.date_due&.to_date, "data-action-completed": action.completed } }
    }) do |action| %>
      <%= action_action_buttons(action) %>
    <% end %>
  </div>
</div>

<!-- Comments Modal/Expandable Section -->
<div id="comments-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Comments</h3>
          <button id="close-comments-modal" class="text-gray-400 hover:text-gray-600">
            <i class="fas fa-times h-5 w-5"></i>
          </button>
        </div>
        <div id="comments-content">
          <!-- Comments will be loaded here -->
        </div>
      </div>
    </div>
  </div>
</div>


<!-- Action Modal -->
<%= render partial: "admin/shared/modals/new_action_modal", locals: {
  modal_id: "patientNewActionModal",
  actionable_type: "Patient",
  actionable_id: @patient.id,
  patient_id: @patient.id
} %>







<!-- Reminder Modal -->
<div id="reminder-modal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <div class="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
    <span class="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">&#8203;</span>
    <div class="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
      <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-amber-100 sm:mx-0 sm:h-10 sm:w-10">
            <i class="fas fa-bell text-amber-500"></i>
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
            <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">Manage Reminders</h3>
            <div class="mt-4">
              <input type="hidden" id="reminder-action-id" value="">
              <div class="mb-4">
                <label for="reminder-type" class="block text-sm font-medium text-gray-700 mb-1">Reminder Type</label>
                <select id="reminder-type" class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm">
                  <option value="email">Email</option>
                  <option value="sms">SMS</option>
                  <option value="in-app">In-app</option>
                  <option value="follow-up">Follow-up</option>
                </select>
              </div>
              <div class="mb-4">
                <label for="reminder-date" class="block text-sm font-medium text-gray-700 mb-1">Reminder Date</label>
                <input type="date" id="reminder-date" class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm">
              </div>
              <div class="mb-4">
                <label for="reminder-time" class="block text-sm font-medium text-gray-700 mb-1">Reminder Time</label>
                <input type="time" id="reminder-time" class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm">
              </div>
              <div class="mb-4">
                <label for="reminder-note" class="block text-sm font-medium text-gray-700 mb-1">Note (Optional)</label>
                <textarea id="reminder-note" rows="3" class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"></textarea>
              </div>
              <div id="current-reminders" class="mt-4">
                <h4 class="text-sm font-medium text-gray-700 mb-2">Current Reminders</h4>
                <div id="reminder-list" class="space-y-2 max-h-40 overflow-y-auto">
                  <!-- Reminders will be loaded here via JavaScript -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
        <button type="button" id="save-reminder-btn" class="inline-flex w-full justify-center rounded-md border border-transparent bg-amber-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm">Save Reminder</button>
        <button type="button" id="close-reminder-modal-btn" class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">Cancel</button>
      </div>
    </div>
  </div>
</div>


