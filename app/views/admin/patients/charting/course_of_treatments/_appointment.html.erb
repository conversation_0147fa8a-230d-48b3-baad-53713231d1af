<%# app/views/admin/patients/charting/course_of_treatments/_appointment.html.erb %>
<% appt_counter ||= appt.position %>
<% patient = appt.course_of_treatment.patient %>

<div class="appointment-block" data-appointment-id="<%= appt.id %>">
  <div class="flex">
    <div class="appointment-toolbar bg-green-50 rounded-lg border border-green-200 shadow-sm overflow-hidden flex items-center mr-3 mb-1.5 p-1.5 w-12 flex-shrink-0">
      <div class="flex flex-col items-center py-2 px-1.5 bg-white rounded-md shadow-sm h-full w-full justify-between">
        <%= render 'admin/calendar_bookings/staff_calendar/icons_group',
                    record: appt,
                    icon_klass: 'charting-appointment-icon w-8 h-8 mx-1 p-1 rounded-full text-gray-800 cursor-pointer bg-white/40 border border-gray-300 hover:border-gray-400 transition-colors',
                    layout: 'vertical' %>
      </div>
    </div>

    <!-- ──────────────────── APPOINTMENT CARD ──────────────────── -->
    <div class="appointment-container text-card-foreground border border-green-200 bg-green-50 shadow-sm rounded-lg overflow-visible mb-1.5 flex-1 flex flex-col min-h-[300px]"
         style="flex-grow:1"
         data-appointment-id="<%= appt.id %>"
         data-locked="<%= appt.locked %>"
         <% if appt.locked_at %>data-locked-at="<%= appt.locked_at.iso8601 %>"<% end %>
         data-appt-dentist-id="<%= appt.dentist_id %>"
         data-appt-dentist-name="<%= appt.dentist.full_name if appt.dentist_id.present? %>">

      <!-- HEADER STRIP (index / dentist / menu) -->
      <div class="flex px-2 pt-2 pb-1.5">
        <!-- left (index + lock) -->
        <div class="flex items-center gap-1 mr-2">
          <span class="apptid text-3xl font-bold text-green-700"><%= appt_counter + 1 %></span>
          <% if appt.locked %>
            <div class="flex flex-col items-center">
              <%
                time_since_locked = appt.locked_at ? Time.current - appt.locked_at : 0
                can_unlock = appt.locked_at && time_since_locked < 24.hours
              %>
              <span class="material-symbols-outlined text-[#8999B0] text-[22px] <%= 'cursor-pointer' if can_unlock %>"
                    <% if can_unlock %>data-appointment-id="<%= appt.id %>"<% end %>>
                lock
              </span>
              <% if appt.locked_at %>
                <% time_left = 24.hours - time_since_locked %>
                <% if time_left > 0 %>
                  <div class="countdown-timer text-[12px] text-[#8999B0] mt-1"
                       data-appointment-id="<%= appt.id %>"
                       data-locked-at="<%= appt.locked_at.iso8601 %>">
                    <%= Time.at(time_left).utc.strftime('%H:%M:%S') %>
                  </div>
                <% end %>
              <% else %>
                <span class="text-[12px] text-[#8999B0] mt-1">Locked</span>
              <% end %>
            </div>
          <% else %>
            <span class="material-symbols-outlined lock-icon text-[#8999B0] text-[22px] cursor-pointer"
                  data-appointment-id="<%= appt.id %>">
              lock_open
            </span>
          <% end %>
        </div>

        <!-- right (dentist / booking pill / menu) -->
        <div class="flex-1 ms-auto">
          <div class="appointment-row flex items-center justify-between bg-white rounded-md p-2 border border-green-100 shadow-sm flex-1"
               data-appointment-id="<%= appt.id %>"
               data-id="<%= appt_counter %>"
               data-appt-dentist-id="<%= appt.dentist_id %>"
               data-appt-dentist-name="<%= appt.dentist.full_name if appt.dentist_id.present? %>">

            <%= render 'layouts/shared/user_avatar', user: appt.dentist, width: 24, height: 24 %>
            <span class="appt-employee-name cursor-pointer inline-flex items-center rounded-full px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-blue-50 text-blue-600 border-0 hover:bg-blue-50 text-xs">
              <%= appt.dentist.full_name if appt.dentist_id.present? %>
            </span>

            <!-- pill + dropdown menu -->
            <div class="ml-auto flex items-center">
              <% if appt.calendar_booking.present? %>
                <!-- Calendar booking is associated -->
                <button type="button" class="bg-green-50 text-green-600 border-0 flex items-center gap-1 py-0.5 px-2 rounded-full text-xs font-medium"
                        data-appointment-id="<%= appt.id %>"
                        onclick="openCalendarBookingModal(<%= appt.id %>)">
                  <span class="material-symbols-outlined mb-[3px]">calendar_month</span>
                  <span><%= appt.calendar_booking.start_time.strftime('%d %b %Y at %I:%M %p') rescue 'Invalid Date' %></span>
                </button>
              <% else %>
                <!-- No calendar booking associated -->
                <button type="button"
                        class="bg-blue-50 text-blue-600 border-0 hover:bg-blue-100 flex items-center gap-1 py-0.5 px-2 rounded-full text-xs font-medium hover:bg-blue-200 transition-colors cursor-pointer"
                        data-appointment-id="<%= appt.id %>"
                        onclick="openCalendarBookingModal(<%= appt.id %>)">
                  <span class="material-symbols-outlined mb-[3px]">calendar_month</span>
                  <span>Not yet booked</span>
                </button>
              <% end %>
            </div>

            <div class="relative circledropdown">
              <!-- trigger ------------------------------------------------------------- -->
              <button
                id="circledropdownbtn-<%= appt_counter %>"
                class="js-dropdown-toggle inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-6 w-6 rounded-full text-neutral-500 hover:text-neutral-700 hover:bg-green-100"
                type="button"
                aria-expanded="false">
                <span class="material-symbols-outlined">more_horiz</span>
              </button>

              <!-- menu ---------------------------------------------------------------- -->
              <div
                id="circledropdown-menu-<%= appt_counter %>"
                class="js-dropdown-menu hidden absolute shadow-lg z-50 mt-2 w-56 rounded-lg bg-gray-200 shadow divide-y divide-gray-100 text-xs">
                <ul class="py-2 text-sm text-gray-700" aria-labelledby="circledropdownbtn-<%= appt_counter %>">
                  <li>
                    <a class="dropdown-item block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       onclick="event.preventDefault();"
                       data-appointment-id="<%= appt.id %>"
                       data-course-of-treatment-id="<%= appt.course_of_treatment.id %>">Upload Xray</a>
                  </li>
                  <li>
                    <a class="dropdown-item block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       onclick="event.preventDefault();"
                       data-appointment-id="<%= appt.id %>"
                       data-course-of-treatment-id="<%= appt.course_of_treatment.id %>">Upload CBCT</a>
                  </li>
                  <li>
                    <a class="dropdown-item block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       onclick="event.preventDefault();"
                       data-appointment-id="<%= appt.id %>"
                       data-course-of-treatment-id="<%= appt.course_of_treatment.id %>">Upload OPG</a>
                  </li>
                  <li>
                    <a class="dropdown-item block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       onclick="event.preventDefault();"
                       data-appointment-id="<%= appt.id %>"
                       data-course-of-treatment-id="<%= appt.course_of_treatment.id %>">Upload photo</a>
                  </li>
                </ul>

                <ul class="py-2 text-sm text-gray-700 locked-appointment-action" data-appointment-id="<%= appt.id %>">
                  <li>
                    <a class="dropdown-item change-course-of-treatment block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       data-appointment-id="<%= appt.id %>">Change Course of Treatment</a>
                  </li>
                </ul>

                <ul class="py-2 text-sm text-gray-700 locked-appointment-action" data-appointment-id="<%= appt.id %>">
                  <li>
                    <a class="dropdown-item delete-appointment block px-4 py-2 hover:bg-gray-100 text-red-600 text-xs"
                       href="#"
                       data-appointment-id="<%= appt.id %>">Delete</a>
                  </li>
                </ul>

                <ul class="py-2 text-sm text-gray-700 locked-appointment-action" data-appointment-id="<%= appt.id %>">
                  <li class="change-dentist-item">
                    <a class="dropdown-item change-dentist block px-4 py-2 hover:bg-gray-100 text-xs"
                       href="#"
                       data-appointment-id="<%= appt.id %>">Change Dentist</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="accordion sortableAppointments sortable px-2"
           data-appointment-id="<%= appt.id %>">
        <%= render partial: 'admin/patients/charting/course_of_treatments/charted_treatment',
                   collection: appt.charted_treatments.sort_by(&:position_order),
                   as: :charted_treatment,
                   locals: { appt: appt } %>
      </div>

      <!-- SUMMARY BAR -->
      <div class="flex justify-end justify-center px-2 pt-1 pb-1.5 gap-2">
        <!-- Appointment Type Section -->
        <div class="bg-white rounded-md shadow-sm border border-green-100 mb-1 overflow-hidden">
          <div class="flex items-center gap-2 p-1.5">
            <div class="flex items-center gap-1">
              <%= select_tag "appointment_type_#{appt_counter}",
                             options_from_collection_for_select(@treatments, :id, :patient_friendly_name, appt.appointment_type),
                             {
                               class: "appointment-type-select text-xs",
                               data: {
                                 appointment_id: appt.id,
                                 placeholder: "Search for a treatment"
                               },
                               style: "width: 200px;",
                               include_blank: "Select treatment"
                             } %>
            </div>
          </div>
        </div>
        <!-- Book After Section -->
        <% unless appt_counter == 0 %>
          <div class="bg-white rounded-md shadow-sm border border-green-100 mb-1 overflow-hidden">
            <div class="flex items-center gap-2 p-1.5">
              <div class="h-5 w-5 flex items-center justify-center rounded-full bg-neutral-50 border border-neutral-200 text-neutral-500">
                <span class="material-symbols-outlined text-xs" style="font-size:14px;">
                  schedule
                </span>
              </div>
              <div class="text-xs text-nowrap">
                Book After:
              </div>
              <div class="flex items-center gap-1">
                <input type="number"
                       class="form-control time-until-booking-count text-xs border border-gray-300 rounded px-1 py-0.5 w-12 text-center"
                       name="time_until_booking_count"
                       value="<%= appt.time_until_booking_count || 0 %>"
                       min="0"
                       max="99"
                       data-appointment-id="<%= appt.id %>"
                       onblur="updateAppointmentBookingTime(this);"
                       onkeydown="if(event.key === 'Enter') { updateAppointmentBookingTime(this); }"/>

                <select class="form-control time-until-booking-unit text-xs border border-gray-300 rounded px-1 py-0.5 w-16"
                        name="time_until_booking_unit"
                        data-appointment-id="<%= appt.id %>"
                        onchange="updateAppointmentBookingTime(this);">
                  <option value="days" <%= 'selected' if appt.time_until_booking_unit == 'days' %>>days</option>
                  <option value="weeks" <%= 'selected' if appt.time_until_booking_unit == 'weeks' %>>weeks</option>
                  <option value="months" <%= 'selected' if appt.time_until_booking_unit == 'months' %>>months</option>
                  <option value="years" <%= 'selected' if appt.time_until_booking_unit == 'years' %>>years</option>
                </select>
              </div>
            </div>
          </div>
        <% end %>

        <div class="bg-white rounded-md shadow-sm border border-green-100 mb-1 overflow-hidden w-1/3">
          <div class="grid grid-cols-[auto_1fr_80px_80px_80px] gap-2 items-center p-1.5">
            <div class="h-5 w-5 flex items-center justify-center rounded-full bg-neutral-50 border border-neutral-200 text-neutral-500">
              <span class="text-xs font-medium">Σ</span>
            </div>

            <div class="ml-2">
              <div class="text-xs font-medium text-neutral-800">
                Total
              </div>
            </div>

            <div class="flex items-center justify-center">
              <div class="flex items-center gap-1 text-xs text-neutral-600">
                <span class="material-symbols-outlined">
                  timer
                </span>

                <div class="appointment-total-duration-time cursor-pointer"
                     onclick="$('.appt-duration').hide();$('.appt-duration').prev().show();$(this).next().show();$(this).hide();$(this).next().focus().select();">
                  0h&nbsp;00m
                </div>

                <input type="number"
                       class="form-control appt-duration"
                       step="0.01"
                       data-id="<%= appt.id %>"
                       style="font-size:14px; display:none; max-width:75px;"
                       onblur="updateAppointmentTotalDuration(this);"
                       onkeydown="if(event.key === 'Enter') { updateAppointmentTotalDuration(this); }"/>
              </div>
            </div>

            <div class="appointment-total-price text-xs font-medium text-neutral-800 text-center">
              £0.00
            </div>

            <div class="flex items-center justify-center gap-1"></div>
          </div>
        </div>
      </div>

      <!-- APPOINTMENT NOTES LIST -->
      <div class="appointment-notes-list pt-1.5 border-t border-green-100 px-2 pt-1 pb-1.5 mt-auto"
           data-appointment-id="<%= appt.id %>"
           id="notes-section-<%= appt.id %>">
        <div class="flex items-center">
          <div class="text-xs font-medium text-neutral-700">Appointment Notes</div>
          <button class="edit-note inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border-input hover:text-accent-foreground ml-2 h-5 px-2 text-xs bg-green-500 text-white hover:bg-green-600 rounded-full border-0 shadow-sm"
                  data-note-toggle="#note-collapse-<%= appt.id %>"
                  data-appointment-id="<%= appt.id %>">
            <span class="material-symbols-outlined">
              add
            </span>
            <span>Add Note</span>
          </button>
        </div>

        <% appt.appointment_notes.select(&:persisted?).each do |note| %>
          <%= render 'admin/patients/charting/course_of_treatments/appointment_note',
                     note: note, source: 'appt', appt: appt %>
        <% end %>
      </div>

      <!-- FOOTER: add / edit note -->
      <div class="appointment-footer flex flex-wrap">
        <div class="flex gap-2 ms-2 w-full items-center justify-between">
          <!-- collapse toggle -->
          <!-- hidden buttons -->
          <button class="btn appt-note-template hidden"
                  style="height:35px; border:1px solid #2A4558; margin-left:auto; margin-right:10px; background:linear-gradient(90deg,#BDCFDB 0%,#BDCFDB 100%);"
                  data-bs-target="#templateNotesModal"
                  data-bs-toggle="modal"
                  data-appt-id="<%= appt.id %>"
                  data-cot-id="<%= appt.course_of_treatment.id %>"
                  data-type="appointment">
            Import Note Template
          </button>
          <a class="btn yellowbtn hidden"
             href="charting/ai_note/appointment/<%= appt.id %>"
             style="height:35px; border:1px solid #2A4558; margin-top:2px;">
            Generate AI Clinical Note
          </a>
        </div>

        <!-- COLLAPSIBLE NOTE FORM -->
        <div class="flex flex-col gap-2 ms-2 w-full">
          <div id="noteCollapse<%= appt.id %>"
               class="accordion-item">
            <div id="note-collapse-<%= appt.id %>" class="hidden accordion-body mt-[10px]">
              <%= form_with model: appt.appointment_notes.new,
                            url: create_charting_appointment_note_admin_patient_charting_path(appt),
                            id: 'add-appointment-note-form' do |f| %>
                <%= f.hidden_field :charting_appointment_id, value: appt.id %>

                <div class="border-t border-neutral-100">


                  <div class="flex bg-green-50/50">
                    <div class="w-1/2 p-4 border-r border-green-100">
                      <div class="space-y-4">
                        <div class="flex items-center justify-between">
                          <%= f.label :completed_at, 'Completed on', class: 'text-sm font-medium text-neutral-700' %>

                          <div class="w-3/5">
                            <%= f.date_field :completed_at,
                                             value: Date.today.strftime('%Y-%m-%d'),
                                             class: 'appointment_note_completed_at_input flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-neutral-200 focus:border-blue-300 focus:ring-blue-200' %>
                          </div>
                        </div>

                        <div class="flex items-center justify-between">
                          <%= f.label :practitioner_id, 'Practitioner', class: 'text-sm font-medium text-neutral-700' %>

                          <div class="w-3/5">
                            <%= f.select :practitioner_id,
                                         options_from_collection_for_select(
                                           f.object.practitioner ? [f.object.practitioner] : [current_user],
                                           :id, :full_name, f.object.practitioner_id || current_user.id),
                                         {},
                                         id: "practitioner-#{appt.id}",
                                         class: "practitioner-#{appt.id} note-practitioner-select flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-neutral-200 focus:border-blue-300 focus:ring-blue-200",
                                         data: { placeholder: 'Select practitioner' } %>
                          </div>
                        </div>

                        <div class="flex items-center justify-between">
                          <%= f.label :nurse_id, 'Nurse', class: 'text-sm font-medium text-neutral-700' %>

                          <div class="w-3/5">
                            <%= f.select :nurse_id,
                                         options_from_collection_for_select(
                                           f.object.nurse ? [f.object.nurse] : [],
                                           :id, :full_name, f.object.nurse_id),
                                         {},
                                         id: "nurse-#{appt.id}",
                                         class: "nurse-#{appt.id} note-practitioner-select flex h-10 w-full rounded-md border px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 bg-white border-neutral-200 focus:border-blue-300 focus:ring-blue-200",
                                         data: { placeholder: 'Select nurse' } %>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="ai-notes w-1/2 p-4">
                      <div class="mic-div flex flex-col h-full">
                        <div style="height:10px"></div>

                        <div class="relative ai-notes">
                          <%= f.text_area :content,
                                          id: "notes_#{appt.id}",
                                          class: "form-control notes-textarea notes-textarea tinymce_editor ai-textarea ai-textarea",
                                          rows: 10,
                                          placeholder: 'Notes',
                                          style: 'height:470px;margin:0px 10px;' %>
                          <%= render 'admin/treatment_plan_options/option/ai_tools', unique_id: 'reason-ai' %>

                          <!-- (spinner overlay left intact) -->
                          <div class="spinner-overlay" style="display:none;">…</div>
                        </div>

                        <!-- action buttons -->
                        <div class="flex items-center justify-end" style="margin:0 11px 11px 11px;background-color:rgb(255,255,255);width:97%;border-radius:0px 0px 14px 14px;">
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- SAVE BUTTON STRIP -->
                  <div class="flex items-center justify-between p-3 bg-white border-t border-neutral-100 justify-end">
                    <div class="flex items-center gap-2">
                      <button type="button"
                              class="cancel-app-note px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors flex items-center gap-1"
                              data-note-toggle="#note-collapse-<%= appt.id %>"
                              data-appointment-id="<%= appt.id %>">
                        Cancel
                      </button>
                      <%= f.submit 'Save',
                                   class: 'save-app-note px-4 py-2 bg-blue-100 text-blue-600 rounded-full text-sm font-medium hover:bg-blue-200 transition-colors flex items-center gap-1',
                                   data: { disable_with: nil, 'note-toggle' => "#note-collapse-#{appt.id}", 'appointment-id' => appt.id } %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div id="newappt"
       class="flex items-center my-4 group"
       data-course-of-treatment-id="<%= appt.course_of_treatment.id %>"
       data-patient-id="<%= patient.id %>"
       data-current-position="<%= appt.position %>">

    <div class="flex-grow h-px bg-gradient-to-r from-transparent via-green-200 to-transparent"></div>

    <button class="mx-3 px-4 py-1 text-xs font-medium text-green-600 bg-green-50 hover:bg-green-100 rounded-full border border-green-200 shadow-sm transition-all duration-200 hover:shadow flex items-center group-hover:scale-105">
      <span class="material-symbols-outlined calendar-add-on">calendar_add_on</span>
      <span>Add Appointment</span>
    </button>

    <div class="flex-grow h-px bg-gradient-to-r from-transparent via-green-200 to-transparent"></div>
  </div>
</div>
