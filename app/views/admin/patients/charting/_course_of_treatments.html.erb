<!-- app/views/admin/patients/charting/_course_of_treatments.html.erb -->

<div class="w-11/12 mt-8 mb-20 charting-bottom-container">

  <!-- ───────── TOP-CONTROL ROW ───────── -->
  <div
    class="flex items-center gap-3 pb-4 border-b border-neutral-300">

    <!-- ▸ Add course-of-treatment (round “+” pill) -->
    <button
      type="button"
      class="add-course-of-treatment add_course_of_treatment inline-flex bg-white items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-10 w-10 rounded-full"
      data-tippy-content="Add Treatment Plan">
      <%= image_tag "ui/plus.svg", class: "h-4 w-4" %>
      <span class="sr-only">Add course of treatment</span>
    </button>

    <!-- ▸ Treatment-plan pills (scrollable if overflowing) -->
    <div id="cot-scroll-wrapper"
         class="tpwrapper flex gap-2 overflow-x-auto scrollbar-thin scrollbar-thumb-neutral-300 scrollbar-track-transparent">
      <% @course_of_treatments.each do |cot| %>
        <%
          # Format appointment data for JavaScript: [id, date, docId, docName]
          appointments_data = cot.charting_appointments.order(:position).map do |appt|
            # Use actual calendar booking date if available, otherwise show position
            appointment_date = if appt.calendar_booking&.start_time.present?
                                appt.calendar_booking.start_time.strftime('%a, %d %b %Y at %I:%M %p')
                              else
                                "Appointment #{appt.position}"
                              end
            [
              appt.id,
              appointment_date,
              appt.dentist_id,
              appt.dentist&.full_name || 'Unknown'
            ]
          end.to_json
        %>
        <%= link_to(
              admin_patient_charting_path(@patient, course_of_treatment_id: cot.id),
              class: "
                course-of-treatment-item
                #{cot == @course_of_treatment ? 'bg-blue-100 sleected-cot' : 'bg-white hover:bg-sky-100'}
                flex items-center gap-2 px-4 py-1.5 rounded-full text-sm font-medium text-neutral-800
              ",
              data: {
                course_of_treatment_id:      cot.id,
                treatment_plan_appointments: appointments_data,
                charge_date:                 cot.charged_date,
                course_of_treatment_completed: cot.completed_at.present?
              }) do %>
          <%= "#{cot.id} #{cot.name}".html_safe %>

          <%= image_tag 'ui/signature.svg',
                        class: "inline h-4 w-4",
                        style: cot.accepted? ? 'display: block' : 'display: none',
                        alt: 'signed' %>
        <% end %>
      <% end %>
    </div>

    <!-- ▸ Manual scroll controls (only if you still need them) -->
    <button id="scrollLeft"
            class="ml-1 text-neutral-500 hover:text-neutral-800 transition">
      <i class="fa-regular fa-arrow-left"></i>
      <span class="sr-only">scroll left</span>
    </button>
    <button id="scrollRight"
            class="text-neutral-500 hover:text-neutral-800 transition">
      <i class="fa-regular fa-arrow-right"></i>
      <span class="sr-only">scroll right</span>
    </button>

    <!-- Spacer -->
    <div class="flex-1"></div>

    <!-- ▸ Search box -->
    <div class="search-container-charting">
      <button class="search-button-icon" id="search-icon" style="z-index: 1;">
        <i class="fa fa-search"></i>
      </button>
      <input type="text" class="search-input" placeholder="Search" id="appt_search">
    </div>

    <!-- ▸ Three round action buttons -->
    <div class="flex items-center gap-3">

      <!-- history -->
      <button
        type="button"
        class="history-link inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-10 w-10 rounded-full bg-white"
        data-tippy-content="History">
        <%= image_tag "ui/refresh.svg", class: "h-5 w-5" %>
      </button>

      <!-- base chart -->
      <button
        id="toggletobasechart"
        type="button"
        class="relative inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-10 w-10 rounded-full bg-white"
        data-tippy-content="Base Chart">
        <%= image_tag "ui/clipboardcheck.svg", class: "h-5 w-5" %>
      </button>

      <!-- archived plans -->
      <button
        id="toggletoarchived"
        type="button"
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-10 w-10 rounded-full bg-white"
        data-tippy-content="Archived Treatment Plans">
        <%= image_tag "ui/archive_plan.svg", class: "h-5 w-5" %>
      </button>
    </div>
  </div>
  <!-- /top-control row -->

  <!-- ───────── APPOINTMENT STACK ───────── -->
  <div id="course-of-treatment-container" class="space-y-4">
    <% if @course_of_treatment.present? %>
      <%= render 'admin/patients/charting/course_of_treatments/appointments',
                 course_of_treatment: @course_of_treatment %>
    <% else %>
      <div class="text-sm text-neutral-500">
        No active course of treatment selected.
      </div>
    <% end %>
  </div>
</div>
