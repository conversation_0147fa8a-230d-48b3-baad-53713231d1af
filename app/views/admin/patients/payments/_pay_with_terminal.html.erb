<div class="pay-terminal-container">
  <%= form_with model: [:admin, @patient, @payment], id: 'payment-form-terminal', class: "space-y-4" do |form| %>
    <%= form.hidden_field :payment_mean, value: 'terminal' %>
    <%= form.hidden_field :invoice_id, class: 'invoice-id-field' %>

    <div class="grid grid-cols-3 gap-4">
      <div class="flex-shrink-0">
        <%= form.label :currency, class: "block text-xs text-gray-500 mb-1" %>
        <%= form.select :currency, Money::Currency.all.map { |c| ["#{c.iso_code} #{c.symbol}", c.iso_code.downcase] },
                        { selected: "gbp" },
                        class: 'w-full border border-gray-200 rounded-lg p-2.5 text-xs bg-white text-gray-900' %>
      </div>
      <div class="col-span-2">
        <%= form.label :amount, class: "block text-xs text-gray-500 mb-1" %>
        <%= form.number_field :amount, step: 0.01, class: 'w-full border border-gray-200 rounded-lg p-2.5 text-xs bg-white text-gray-900' %>
      </div>
    </div>

    <div>
      <%= form.label :terminal, class: "block text-xs text-gray-500 mb-1" %>
      <%= form.select :terminal, options_for_select(@terminals), { prompt: "Select a terminal" }, class: "w-full border border-gray-200 rounded-lg p-2.5 text-xs bg-white text-gray-900", data: { terminal_select: true } %>
    </div>

    <div class="grid grid-cols-3 gap-3 pt-4">
      <button type="button" class="w-full bg-[#FFF4E6] hover:bg-[#FFEDD5] text-amber-700 py-2.5 px-4 rounded-full text-xs font-medium transition-colors show-pay-manual">
        Add Payment Manually
      </button>
      <button type="button" class="w-full bg-[#E0E7FF] hover:bg-[#C7D2FE] text-indigo-700 py-2.5 px-4 rounded-full text-xs font-medium transition-colors show-enter-card-details">
        Enter Card Details
      </button>
      <%= form.submit 'Take Payment', class: 'w-full bg-gradient-to-r from-[#F9E2D2] to-[#F6D8C0] hover:from-[#F6D8C0] hover:to-[#F3CEB0] text-amber-800 py-2.5 px-4 rounded-full text-xs font-medium transition-all duration-200 border-0 shadow-sm', data: { terminal_payment_btn: true } %>
    </div>
  <% end %>
</div>
