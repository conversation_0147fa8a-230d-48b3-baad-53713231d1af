<%= render 'admin/patients/header', patient: @patient %>

<div id="patient-appointments-page" class="mx-[32px] p-[24px] mb-4">
  <div class="w-full">
    <div class="flex flex-wrap gap-4">
      <div class="flex items-center justify-end w-full mb-4">
        <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm overflow-x-auto whitespace-nowrap">
          <% filters = [
            {key: :all,       label: 'All',       url: admin_patient_charting_appointments_path(@patient),                      icon: 'list',                 color: 'indigo', gradient: 'linear-gradient(to right,#c7d2fe,#a5b4fc)'},
            {key: :upcoming,  label: 'Upcoming',  url: admin_patient_charting_appointments_path(@patient, filter: 'upcoming'),  icon: 'calendar',             color: 'orange', gradient: 'linear-gradient(to right,#fed7aa,#fdba74)'},
            {key: :unbooked,  label: 'Unbooked',  url: admin_patient_charting_appointments_path(@patient, filter: 'unbooked'),  icon: 'calendar-circle-plus', color: 'amber',  gradient: 'linear-gradient(to right,#fde68a,#fcd34d)'},
            {key: :completed, label: 'Completed', url: admin_patient_charting_appointments_path(@patient, filter: 'completed'), icon: 'circle-check',         color: 'green',  gradient: 'linear-gradient(to right,#bbf7d0,#86efac)'},
            {key: :cancelled, label: 'Cancelled', url: admin_patient_charting_appointments_path(@patient, filter: 'cancelled'), icon: 'xmark',                color: 'red',    gradient: 'linear-gradient(to right,#fecaca,#fca5a5)'},
            {key: :missed,    label: 'Missed',    url: admin_patient_charting_appointments_path(@patient, filter: 'missed'),    icon: 'face-sad-sweat',       color: 'purple', gradient: 'linear-gradient(to right,#e9d5ff,#d8b4fe)'}
          ] %>
          <% current = (params[:filter] || 'all').to_sym %>

          <% filters.each do |f| %>
            <%= link_to f[:url], data: { tippy_content: "#{f[:label]} Appointments" } do %>
              <% active = current == f[:key] %>
              <button
                class="min-w-[36px] h-9 rounded-full transition-all duration-300 flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden px-3 <%= active ? "text-#{f[:color]}-800" : 'text-gray-600 bg-white hover:bg-gray-50' %>"
                data-filter="<%= f[:key] %>"
                data-color="<%= f[:color] %>"
                style="<%= active ? "background: #{f[:gradient]};" : '' %>"
              >
                <i class="fa-light fa-<%= f[:icon] %> h-4 w-4"></i>
                <span class="tab-text text-xs font-medium tracking-wide transition-all duration-300 <%= active ? 'ml-1.5 opacity-100 w-auto' : 'opacity-0 w-0' %>"><%= f[:label] %></span>
              </button>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <% if current == :all %>
      <div class="flex flex-wrap gap-4">
        <% @status_groups.each do |group| %>
          <div class="w-full mb-4">
            <div class="flex items-center justify-between cursor-pointer section-toggle" data-section="<%= group[:status].downcase %>">
              <h4 class="text-[16px] font-semibold text-gray-600 mb-2">
                <%= group[:status] %> Appointments
              </h4>
              <i class="fa-light fa-chevron-down text-gray-400 transition-transform duration-200 toggle-arrow"></i>
            </div>
            <div class="section-content" data-section="<%= group[:status].downcase %>">
              <%= admin_table(
                group[:appointments],
                charting_appointments_table_columns(@patient),
                empty_message: "No #{group[:status].downcase} appointments found"
              ) do |row|
                if row[:type] == :unbooked
                  appt = row[:record]
                  content_tag(:div, class: 'relative') do
                    content_tag(:button, class: 'p-1 rounded-full hover:bg-gray-100 transition-colors appointment-actions-toggle', 'data-appointment-id': "charting-appointment-#{appt.id}") do
                      content_tag(:svg, '', xmlns: 'http://www.w3.org/2000/svg', class: 'h-5 w-5 text-gray-400', viewBox: '0 0 20 20', fill: 'currentColor') do
                        content_tag(:path, '', d: 'M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z')
                      end
                    end +
                    content_tag(:div, class: 'appointment-actions-menu hidden absolute right-0 top-8 z-50 w-48 rounded-xl overflow-hidden border border-gray-100 bg-white shadow-lg', 'data-appointment-id': "charting-appointment-#{appt.id}") do
                      content_tag(:span, 'Find Slots', class: 'slot-finder-button cursor-pointer flex w-full items-center gap-2 px-4 py-3 text-xs text-gray-800 hover:bg-gray-50', 'data-id': appt.id, style: 'cursor: pointer;')
                    end
                  end
                else
                  booking = row[:record]
                  content_tag(:div, class: 'relative') do
                    content_tag(:button, class: 'p-1 rounded-full hover:bg-gray-100 transition-colors appointment-actions-toggle', 'data-appointment-id': booking.id) do
                      content_tag(:svg, '', xmlns: 'http://www.w3.org/2000/svg', class: 'h-5 w-5 text-gray-400', viewBox: '0 0 20 20', fill: 'currentColor') do
                        content_tag(:path, '', d: 'M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z')
                      end
                    end +
                    content_tag(:div, class: 'appointment-actions-menu hidden absolute right-0 top-8 z-50 w-48 rounded-xl overflow-hidden border border-gray-100 bg-white shadow-lg', 'data-appointment-id': booking.id) do
                      link_to('View', staff_calendar_admin_calendar_bookings_path(date: booking.start_time.strftime('%Y-%m-%d'), highlight_id: booking.id), class: 'cursor-pointer flex w-full items-center gap-2 px-4 py-3 text-xs text-gray-800 hover:bg-gray-50')
                    end
                  end
                end
              end %>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="flex flex-wrap gap-4">
        <div class="w-full mb-4">
          <%= admin_table(
            (@unbooked_charting_appointments.map { |a| { type: :unbooked, record: a } } +
             @calendar_bookings.map { |b| { type: :booked, record: b } }),
            charting_appointments_table_columns(@patient),
            empty_message: 'No appointments found'
          ) do |row|
            if row[:type] == :unbooked
              appt = row[:record]
              content_tag(:div, class: 'relative') do
                content_tag(:button, class: 'p-1 rounded-full hover:bg-gray-100 transition-colors appointment-actions-toggle', 'data-appointment-id': "charting-appointment-#{appt.id}") do
                  content_tag(:svg, '', xmlns: 'http://www.w3.org/2000/svg', class: 'h-5 w-5 text-gray-400', viewBox: '0 0 20 20', fill: 'currentColor') do
                    content_tag(:path, '', d: 'M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z')
                  end
                end +
                content_tag(:div, class: 'appointment-actions-menu hidden absolute right-0 top-8 z-50 w-48 rounded-xl overflow-hidden border border-gray-100 bg-white shadow-lg', 'data-appointment-id': "charting-appointment-#{appt.id}") do
                  content_tag(:span, 'Find Slots', class: 'slot-finder-button cursor-pointer flex w-full items-center gap-2 px-4 py-3 text-xs text-gray-800 hover:bg-gray-50', 'data-id': appt.id, style: 'cursor: pointer;')
                end
              end
            else
              booking = row[:record]
              content_tag(:div, class: 'relative') do
                content_tag(:button, class: 'p-1 rounded-full hover:bg-gray-100 transition-colors appointment-actions-toggle', 'data-appointment-id': booking.id) do
                  content_tag(:svg, '', xmlns: 'http://www.w3.org/2000/svg', class: 'h-5 w-5 text-gray-400', viewBox: '0 0 20 20', fill: 'currentColor') do
                    content_tag(:path, '', d: 'M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z')
                  end
                end +
                content_tag(:div, class: 'appointment-actions-menu hidden absolute right-0 top-8 z-50 w-48 rounded-xl overflow-hidden border border-gray-100 bg-white shadow-lg', 'data-appointment-id': booking.id) do
                  link_to('View', staff_calendar_admin_calendar_bookings_path(date: booking.start_time.strftime('%Y-%m-%d'), highlight_id: booking.id), class: 'cursor-pointer flex w-full items-center gap-2 px-4 py-3 text-xs text-gray-800 hover:bg-gray-50')
                end
              end
            end
          end %>
        </div>
      </div>
    <% end %>
  </div>
</div>

<%= render 'admin/calendar_bookings/offcanvas', id: 'slot-finder-offcanvas', title: 'Slot Finder' %>
