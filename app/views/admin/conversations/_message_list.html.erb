<div class="bg-white border-r border-gray-100 flex flex-col h-full mr-3" style="width: 22rem;">
  <!-- Search bar section -->
  <div class="p-4 border-b border-gray-100 flex items-center space-x-2">
    <div class="relative flex-1">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-2.5 h-4 w-4 text-gray-400">
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.3-4.3"></path>
      </svg>
      <input id="conversation-search" type="search" class="flex w-full rounded-md border-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 pl-9 h-9 bg-gray-50 border-0 focus-visible:ring-0 focus-visible:ring-offset-0" placeholder="Search conversations">
    </div>
    <button id="new-conversation-button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 w-9 p-0 rounded-full bg-gray-50" onclick="console.log('Button clicked directly from HTML')">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus h-4 w-4">
        <path d="M5 12h14"></path>
        <path d="M12 5v14"></path>
      </svg>
    </button>
  </div>

  <!-- Messages container with tab navigation -->
  <div class="flex flex-col flex-1 overflow-hidden" id="conversation-tabs">
    <!-- Tab navigation -->
    <div class="px-3 mb-4">
      <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
        <button data-tab="attention" onclick="handleTabClick('attention', this); return false;" class="tab-button tab-btn-attention active-tab bg-gradient-to-r from-rose-200 to-rose-300 text-rose-800 min-w-[36px] pl-3 pr-4 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle-alert h-4 w-4 text-rose-700">
            <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path>
            <path d="M12 9v4"></path>
            <path d="M12 17h.01"></path>
          </svg>
          <span class="tab-text text-xs font-medium tracking-wide">Attention</span>
        </button>
        <button data-tab="recent" onclick="handleTabClick('recent', this); return false;" class="tab-button tab-btn-recent bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-4 w-4">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
          <span class="tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Last 30 Days</span>
        </button>
        <button data-tab="older" onclick="handleTabClick('older', this); return false;" class="tab-button tab-btn-older bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar-clock h-4 w-4">
            <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5"></path>
            <path d="M16 2v4"></path>
            <path d="M8 2v4"></path>
            <path d="M3 10h5"></path>
            <path d="M17.5 17.5 16 16.3V14"></path>
            <circle cx="16" cy="16" r="6"></circle>
          </svg>
          <span class="tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">Older</span>
        </button>
        <button data-tab="all" onclick="handleTabClick('all', this); return false;" class="tab-button tab-btn-all bg-white text-gray-600 hover:bg-gray-50 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 px-3 min-w-[36px] overflow-hidden">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-inbox h-4 w-4">
            <polyline points="22 12 16 12 14 15 10 15 8 12 2 12"></polyline>
            <path d="M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path>
          </svg>
          <span class="tab-text text-xs font-medium tracking-wide opacity-0 w-0 transition-all duration-300">All</span>
        </button>
      </div>
    </div>

    <!-- Attention Required Tab Content -->
    <div class="overflow-y-auto flex-1 px-3 py-2 space-y-1.5 tab-content h-[calc(100%-56px)]" id="attention-conversations-container" data-controller="infinite-scroll" data-infinite-scroll-url-value="<%= admin_conversations_path(filter: 'attention') %>" data-infinite-scroll-container-value="#attention-conversations-container" data-infinite-scroll-page-value="1">
      <!-- No search results message -->
      <div class="no-search-results hidden py-8 text-center">
        <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search-x h-6 w-6 text-gray-500">
            <path d="m13.5 8.5-5 5"/>
            <path d="m8.5 8.5 5 5"/>
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.3-4.3"/>
          </svg>
        </div>
        <h3 class="text-sm font-medium text-gray-900 mb-1">No conversations found</h3>
        <p class="text-xs text-gray-500">Try adjusting your search terms</p>
      </div>

      <div class="conversation-cards-container">
        <% if @filter == 'attention' %>
          <% conversations.each do |conversation| %>
            <%= render 'admin/conversations/conversation_card', conversation: conversation, highlight: true %>
          <% end %>
        <% else %>
          <% # Only filter if we're not already on the attention tab %>
          <% attention_required_conversations = conversations.joins(:conversation_messages).includes(:lab).where(conversation_messages: { read_at: nil, message_type: 'inbound' }).group('conversations.id') %>
          <% attention_required_conversations.each do |conversation| %>
            <%= render 'admin/conversations/conversation_card', conversation: conversation, highlight: true %>
          <% end %>
        <% end %>
      </div>

      <!-- Pagination loader -->
      <div class="pagination-loader text-center py-3 hidden">
        <div class="inline-block h-5 w-5 animate-spin rounded-full border-2 border-solid border-current border-r-transparent motion-reduce:animate-[spin_1.5s_linear_infinite] text-blue-500" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
    </div>

    <!-- Recent (Last 30 Days) Tab Content -->
    <div class="overflow-y-auto flex-1 px-3 py-2 space-y-1.5 hidden tab-content h-[calc(100%-56px)]" id="recent-conversations-container" data-controller="infinite-scroll" data-infinite-scroll-url-value="<%= admin_conversations_path(filter: 'recent') %>" data-infinite-scroll-container-value="#recent-conversations-container" data-infinite-scroll-page-value="1">
      <!-- No search results message -->
      <div class="no-search-results hidden py-8 text-center">
        <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search-x h-6 w-6 text-gray-500">
            <path d="m13.5 8.5-5 5"/>
            <path d="m8.5 8.5 5 5"/>
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.3-4.3"/>
          </svg>
        </div>
        <h3 class="text-sm font-medium text-gray-900 mb-1">No conversations found</h3>
        <p class="text-xs text-gray-500">Try adjusting your search terms</p>
      </div>

      <div class="conversation-cards-container">
        <% if @filter == 'recent' %>
          <% conversations.each do |conversation| %>
            <%= render 'admin/conversations/conversation_card', conversation: conversation, highlight: false %>
          <% end %>
        <% else %>
          <% # Only filter if we're not already on the recent tab %>
          <% thirty_days_ago = 30.days.ago %>
          <% recent_conversations = conversations.joins(:conversation_messages).where('conversation_messages.created_at >= ?', thirty_days_ago).where.not(conversation_messages: { read_at: nil, message_type: 'inbound' }).group('conversations.id') %>
          <% recent_conversations.each do |conversation| %>
            <%= render 'admin/conversations/conversation_card', conversation: conversation, highlight: false %>
          <% end %>
        <% end %>
      </div>

      <!-- Pagination loader -->
      <div class="pagination-loader text-center py-3 hidden">
        <div class="inline-block h-5 w-5 animate-spin rounded-full border-2 border-solid border-current border-r-transparent motion-reduce:animate-[spin_1.5s_linear_infinite] text-blue-500" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
    </div>

    <!-- Older Tab Content -->
    <div class="overflow-y-auto flex-1 px-3 py-2 space-y-1.5 hidden tab-content h-[calc(100%-56px)]" id="older-conversations-container" data-controller="infinite-scroll" data-infinite-scroll-url-value="<%= admin_conversations_path(filter: 'older') %>" data-infinite-scroll-container-value="#older-conversations-container" data-infinite-scroll-page-value="1">
      <!-- No search results message -->
      <div class="no-search-results hidden py-8 text-center">
        <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search-x h-6 w-6 text-gray-500">
            <path d="m13.5 8.5-5 5"/>
            <path d="m8.5 8.5 5 5"/>
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.3-4.3"/>
          </svg>
        </div>
        <h3 class="text-sm font-medium text-gray-900 mb-1">No conversations found</h3>
        <p class="text-xs text-gray-500">Try adjusting your search terms</p>
      </div>

      <div class="conversation-cards-container">
        <% if @filter == 'older' %>
          <% conversations.each do |conversation| %>
            <%= render 'admin/conversations/conversation_card', conversation: conversation, highlight: false %>
          <% end %>
        <% else %>
          <% # Only filter if we're not already on the older tab %>
          <% thirty_days_ago = 30.days.ago %>
          <% older_conversations = conversations.joins(:conversation_messages).includes(:lab).where('conversation_messages.created_at < ?', thirty_days_ago).where.not(conversation_messages: { read_at: nil, message_type: 'inbound' }).group('conversations.id') %>
          <% older_conversations.each do |conversation| %>
            <%= render 'admin/conversations/conversation_card', conversation: conversation, highlight: false %>
          <% end %>
        <% end %>
      </div>

      <!-- Pagination loader -->
      <div class="pagination-loader text-center py-3 hidden">
        <div class="inline-block h-5 w-5 animate-spin rounded-full border-2 border-solid border-current border-r-transparent motion-reduce:animate-[spin_1.5s_linear_infinite] text-blue-500" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
    </div>

    <!-- All Tab Content -->
    <div class="overflow-y-auto flex-1 px-3 py-2 space-y-1.5 hidden tab-content h-[calc(100%-56px)] scrollbar-hide" id="all-conversations-container" data-controller="infinite-scroll" data-infinite-scroll-url-value="<%= admin_conversations_path(filter: 'all') %>" data-infinite-scroll-container-value="#all-conversations-container" data-infinite-scroll-page-value="1">
      <!-- No search results message -->
      <div class="no-search-results hidden py-8 text-center">
        <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search-x h-6 w-6 text-gray-500">
            <path d="m13.5 8.5-5 5"/>
            <path d="m8.5 8.5 5 5"/>
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.3-4.3"/>
          </svg>
        </div>
        <h3 class="text-sm font-medium text-gray-900 mb-1">No conversations found</h3>
        <p class="text-xs text-gray-500">Try adjusting your search terms</p>
      </div>

      <div class="conversation-cards-container">
        <% conversations.includes(:lab).each do |conversation| %>
          <% # For the all tab, we need to check if each conversation has unread messages %>
          <% has_unread = @filter == 'attention' ? true : conversation_has_unread_inbound?(conversation) %>
          <%= render 'admin/conversations/conversation_card', conversation: conversation, highlight: has_unread %>
        <% end %>
      </div>

      <!-- Pagination loader -->
      <div class="pagination-loader text-center py-3 hidden">
        <div class="inline-block h-5 w-5 animate-spin rounded-full border-2 border-solid border-current border-r-transparent motion-reduce:animate-[spin_1.5s_linear_infinite] text-blue-500" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  #conversation-tabs {
      .tab-button {
          transition: all 0.3s ease-in-out;
          border-radius: 9999px !important; /* Force rounded-full */
      }

      .tab-button .tab-text {
          transition: opacity 0.3s ease-in-out, width 0.3s ease-in-out, margin 0.3s ease-in-out;
          white-space: nowrap;
          margin-left: 0;
      }

      .tab-button:not(.active-tab) .tab-text {
          width: 0;
          overflow: hidden;
          opacity: 0;
          margin-left: 0;
      }

      .tab-button.active-tab .tab-text {
          width: auto;
          opacity: 1;
          margin-left: 0.5rem;
      }

      /* Ensure tab buttons always stay rounded-full, even when active */
      .tab-button.active-tab {
          border-radius: 9999px !important; /* Force rounded-full for active state */
      }
  }
</style>


<!-- New Conversation Modal -->
<!-- New Conversation Dropdown Menu -->
<div id="new-conversation-dropdown" class="fixed z-50 hidden overflow-hidden bg-white text-gray-800 min-w-[220px] p-1.5 rounded-xl border border-gray-200 shadow-lg" style="position: absolute;">
  <!-- Patient Communication Option -->
  <div id="patient-communication-option" class="relative select-none outline-none flex items-center gap-2.5 px-3 py-2.5 text-sm rounded-lg cursor-pointer transition-colors hover:bg-gray-50 focus:bg-gray-50">
    <div class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-50">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-4 w-4 text-blue-600">
        <rect width="20" height="16" x="2" y="4" rx="2"></rect>
        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
      </svg>
    </div>
    <div>
      <p class="font-medium">New Patient Communication</p>
      <p class="text-xs text-gray-500 mt-0.5">Send a message to a patient</p>
    </div>
  </div>

  <!-- Internal Team Message Option -->
  <div id="team-message-option" class="relative select-none outline-none flex items-center gap-2.5 px-3 py-2.5 text-sm rounded-lg cursor-pointer transition-colors hover:bg-gray-50 focus:bg-gray-50 mt-1">
    <div class="flex items-center justify-center w-8 h-8 rounded-full bg-amber-50">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-4 w-4 text-amber-600">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
      </svg>
    </div>
    <div>
      <p class="font-medium">New Internal Team Message</p>
      <p class="text-xs text-gray-500 mt-0.5">Communicate with multiple team members</p>
    </div>
  </div>
</div>

<!-- Patient Selection Modal -->
<div id="patient-selection-modal" class="fixed inset-0 z-50 hidden" onclick="closePatientModal(event)">
  <div class="absolute inset-0 bg-black/20 backdrop-blur-sm cursor-pointer"></div>
  <div class="flex items-center justify-center min-h-screen p-4 relative">
    <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md p-6 animate-in fade-in zoom-in duration-200">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">Select Patient</h3>
        <button class="patient-modal-close inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Search input -->
      <div class="relative mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-3 h-4 w-4 text-gray-400">
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.3-4.3"></path>
        </svg>
        <input
          type="search"
          id="patient-search"
          placeholder="Search patients by name or email..."
          class="w-full pl-10 pr-4 py-2.5 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
          autocomplete="off"
        >
        <div id="search-loading" class="absolute right-3 top-3 hidden">
          <div class="h-4 w-4 border-2 border-gray-300 border-t-amber-500 rounded-full animate-spin"></div>
        </div>
      </div>

      <div id="patient-list" class="space-y-3 max-h-[400px] overflow-y-auto pr-1">
        <div class="text-center py-6 text-gray-500">
          Start typing to search for patients...
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Team Message Modal -->
<div id="team-message-modal" class="fixed inset-0 z-50 hidden" onclick="closeTeamModal(event)">
  <div class="absolute inset-0 bg-black/20 backdrop-blur-sm cursor-pointer"></div>
  <div class="flex items-center justify-center min-h-screen p-4 relative">
    <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md p-6 animate-in fade-in zoom-in duration-200">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">Select Team Members</h3>
        <button class="team-modal-close inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-8 w-8 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>

      <%= form_with(url: admin_conversations_path, method: :post) do |f| %>
          <div class="mb-4">
            <label for="conversation_name" class="block text-sm font-medium text-gray-700 mb-1.5">Group Name</label>
            <div class="relative">
              <%= text_field_tag 'conversation[name]', '', id: "group-name", placeholder: "Enter a name for this group", class: "w-full h-10 px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent text-sm" %>
              <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
              </div>
            </div>
          </div>

        <div class="mb-4">
          <p class="text-sm text-gray-500 mb-3 flex items-center">
            <span id="selected-count" class="bg-gray-100 rounded-full w-5 h-5 inline-flex items-center justify-center text-xs font-medium text-gray-700 mr-1.5">0</span>Selected team members
          </p>
          <div id="selected-team-members" class="flex flex-wrap gap-1.5">
            <!-- Selected team members will be added here dynamically -->
          </div>
        </div>

        <div class="relative mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-2.5 h-4 w-4 text-gray-400">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.3-4.3"></path>
          </svg>
          <input id="team-member-search" class="flex w-full px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pl-9 h-10 bg-gray-50 border border-gray-200 focus-visible:ring-blue-500 rounded-lg" placeholder="Search team members...">
        </div>

        <div id="team-members-list" class="space-y-2 max-h-[250px] overflow-y-auto pr-1">
          <% User.includes(:roles, image_attachment: :blob).each do |user| %>
            <div class="team-member-card flex items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer border border-gray-100" data-user-id="<%= user.id %>">
              <div class="flex-shrink-0 mr-3">
                <div class="w-10 h-10 rounded-full bg-gradient-to-br from-amber-50 to-amber-100 flex items-center justify-center">
                  <div class="text-amber-600 font-medium"><%= user.first_name[0] %><%= user.last_name[0] %></div>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex justify-between items-center">
                  <h4 class="font-medium text-sm"><%= user.first_name %> <%= user.last_name %></h4>
                  <div class="team-member-checkbox w-5 h-5 rounded-full border-2 border-gray-300"></div>
                </div>
                <p class="text-xs text-gray-500"><%= user.roles.first&.name || 'Staff' %> • <%= user.title.presence || 'General' %></p>
              </div>
              <%= hidden_field_tag 'conversation[internal_recipients_json][]', user.id, id: "user-#{user.id}", disabled: true %>
            </div>
          <% end %>
        </div>

        <div class="flex justify-end mt-4 pt-3 border-t border-gray-100">
          <button type="button" class="team-modal-close inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 mr-2">Cancel</button>
          <%= f.submit "Create Message", class: "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-9 rounded-md px-3 bg-amber-400 hover:bg-amber-500 text-white" %>
        </div>
      <% end %>
    </div>
  </div>
</div>
