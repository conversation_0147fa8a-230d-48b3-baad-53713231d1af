<% is_outbound = (message.author_id == user_id || message.message_type == 'outbound') && message.message_type != 'inbound' %>
<% is_internal = message.message_type == 'internal' %>

<div class="<%= is_outbound ? 'flex justify-end' : 'flex' %> mb-6">
  <div class="max-w-[75%] relative <%= is_outbound ? 'group' : 'message-container' %>">
    <div class="flex <%= is_outbound ? 'flex-row-reverse' : '' %> items-end">
      <% if !is_outbound %>
        <div class="relative">
          <span class="relative flex shrink-0 h-8 w-8 <%= is_outbound ? 'ml-2' : 'mr-2' %> rounded-full overflow-hidden" data-state="closed">
            <% if message.system_generated? %>
              <%= render 'layouts/shared/practice_avatar', practice: message.conversation.practice, width: 32, height: 32 %>
            <% elsif message.author && message.author.respond_to?(:full_name) %>
              <%= render 'layouts/shared/user_avatar', user: message.author, width: 32, height: 32 %>
            <% else %>
              <div class="h-full w-full flex items-center justify-center bg-gray-200 text-gray-700 text-xs font-medium">
                <%= message.author&.respond_to?(:full_name) ? message.author.full_name.split.map(&:first).join : 'PT' %>
              </div>
            <% end %>
          </span>

          <% if message.source.present? && message.message_type != 'internal' %>
            <div class="absolute bottom-0 right-0 h-5 w-5 rounded-full flex items-center justify-center shadow-sm border border-white
                        <%= message.source.downcase == 'email' ? 'bg-purple-500' :
                           message.source.downcase == 'whatsapp' ? 'bg-green-500' :
                           message.source.downcase == 'sms' ? 'bg-blue-500' :
                           message.source.downcase == 'stannp' ? 'bg-emerald-500' :
                           'bg-blue-500' %>">
              <% if message.source.downcase == 'email' %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-at-sign h-3 w-3 text-white">
                  <circle cx="12" cy="12" r="4"></circle>
                  <path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8"></path>
                </svg>
              <% elsif message.source.downcase == 'whatsapp' %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-3 w-3 text-white">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
              <% elsif message.source.downcase == 'stannp' %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-3 w-3 text-white">
                  <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                  <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                  <path d="M10 9H8"></path>
                  <path d="M16 13H8"></path>
                  <path d="M16 17H8"></path>
                </svg>
              <% elsif message.source.downcase == 'sms' %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-3 w-3 text-white">
                  <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                  <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                </svg>
              <% end %>
            </div>
          <% end %>

          <% if message.message_type == 'internal' %>
            <div class="absolute bottom-0 right-0 h-5 w-5 rounded-full flex items-center justify-center shadow-sm border border-white bg-amber-500">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-3 w-3 text-amber-700">
                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                <path d="M10 9H8"></path>
                <path d="M16 13H8"></path>
                <path d="M16 17H8"></path>
              </svg>
            </div>
          <% end %>
        </div>

        <% if message.read_at.nil? %>
          <div class="read-status-container absolute right-0 top-1/2 -translate-y-1/2 translate-x-[calc(100%+8px)] flex flex-col items-center" data-message-id="<%= message.id %>" data-read-status="unread">
            <button type="button" class="read-status-toggle h-8 w-8 rounded-full bg-white/80 border border-gray-100 shadow-md flex items-center justify-center" data-url="<%= mark_as_read_admin_conversation_path(message.conversation_id, message_id: message.id) %>" data-method="post">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="single-tick text-gray-400">
                <polyline points="20 6 9 17 4 12"></polyline>
              </svg>
            </button>
            <span class="read-status-label text-xs mt-1.5 font-medium text-gray-500 tracking-tight">Unread</span>
          </div>
        <% else %>
          <div class="read-status-container absolute right-0 top-1/2 -translate-y-1/2 translate-x-[calc(100%+8px)] flex flex-col items-center" data-message-id="<%= message.id %>" data-read-status="read">
            <button type="button" class="read-status-toggle h-8 w-8 rounded-full bg-white/80 border border-gray-100 shadow-md flex items-center justify-center" data-url="<%= mark_as_unread_admin_conversation_path(message.conversation_id, message_id: message.id) %>" data-method="post">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="double-tick text-blue-500">
                <path d="M18 6 L7 17 L2 12"></path>
                <path d="M22 10 L13 19 L11 17"></path>
              </svg>
            </button>
            <span class="read-status-label text-xs mt-1.5 font-medium text-blue-500 tracking-tight">Read</span>
          </div>
        <% end %>
      <% else %>
        <div class="relative">
          <span class="relative flex shrink-0 h-8 w-8 ml-2 rounded-full overflow-hidden" data-state="closed">
            <% if message.system_generated? %>
              <%= render 'layouts/shared/practice_avatar', practice: message.conversation.practice, width: 32, height: 32 %>
            <% elsif message.author && message.author.respond_to?(:full_name) %>
              <%= render 'layouts/shared/user_avatar', user: message.author, width: 32, height: 32 %>
            <% else %>
              <div class="h-full w-full flex items-center justify-center bg-gray-200 text-gray-700 text-xs font-medium">
                <%= message.author&.respond_to?(:full_name) ? message.author.full_name.split.map(&:first).join : 'PR' %>
              </div>
            <% end %>
          </span>

          <% if message.source.present? && message.message_type != 'internal' %>
            <div class="absolute bottom-0 left-0 h-5 w-5 rounded-full flex items-center justify-center shadow-sm border border-white
                        <%= message.source.downcase == 'email' ? 'bg-purple-500' :
                           message.source.downcase == 'whatsapp' ? 'bg-green-500' :
                           message.source.downcase == 'sms' ? 'bg-blue-500' :
                           message.source.downcase == 'stannp' ? 'bg-emerald-500' :
                           'bg-blue-500' %>">
              <% if message.source.downcase == 'email' %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-at-sign h-3 w-3 text-white">
                  <circle cx="12" cy="12" r="4"></circle>
                  <path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8"></path>
                </svg>
              <% elsif message.source.downcase == 'whatsapp' %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-3 w-3 text-white">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
              <% elsif message.source.downcase == 'stannp' %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-3 w-3 text-white">
                  <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                  <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                  <path d="M10 9H8"></path>
                  <path d="M16 13H8"></path>
                  <path d="M16 17H8"></path>
                </svg>
              <% elsif message.source.downcase == 'sms' %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-3 w-3 text-white">
                  <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                  <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                </svg>
              <% end %>
            </div>
          <% end %>

          <% if message.message_type == 'internal' %>
            <div class="absolute bottom-0 left-0 h-5 w-5 rounded-full flex items-center justify-center shadow-sm border border-white bg-amber-500">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-3 w-3 text-amber-700">
                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                <path d="M10 9H8"></path>
                <path d="M16 13H8"></path>
                <path d="M16 17H8"></path>
              </svg>
            </div>
          <% end %>
        </div>
      <% end %>

      <div class="<%= is_outbound ? '' : 'relative' %>">
        <div class="relative group">
          <% if is_internal %>
            <div class="bg-amber-100 p-3 rounded-2xl <%= is_outbound ? 'rounded-br-none' : 'rounded-bl-none' %> text-sm message-bubble" data-message-type="internal">
              <div class="message-content">
                <%= simple_format message.content %>
              </div>
            </div>
          <% elsif message.source&.downcase == 'stannp' %>
            <div class="bg-gradient-to-br from-amber-50 to-amber-50/70 p-4 rounded-2xl <%= is_outbound ? 'rounded-br-none' : 'rounded-bl-none' %> text-sm border border-amber-100/50 shadow-sm backdrop-blur-sm">
              <div class="flex items-center mb-3">
                <div class="bg-amber-500/10 p-2 rounded-full mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-5 w-5 text-amber-600">
                    <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                    <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                    <path d="M10 9H8"></path>
                    <path d="M16 13H8"></path>
                    <path d="M16 17H8"></path>
                  </svg>
                </div>
                <span class="font-medium text-amber-800">Letter</span>
              </div>
              <div class="bg-white/80 rounded-xl p-4 border border-gray-100 mb-3 shadow-sm">
                <%= simple_format message.content, class: 'text-xs text-gray-600 mb-2 leading-relaxed' %>
              </div>
            </div>
          <% elsif is_outbound %>
            <div class="relative <%= message.source&.downcase == 'email' ? 'bg-purple-100' :
                            message.source&.downcase == 'whatsapp' ? 'bg-green-100' :
                            message.source&.downcase == 'sms' ? 'bg-blue-100' :
                            'bg-purple-100' %> p-3 rounded-2xl rounded-br-none text-sm message-bubble"
                  data-message-type="<%= message.source.present? ? message.source.downcase == 'email' ? 'email' :
                                                                     message.source.downcase == 'whatsapp' || message.source.downcase == 'sms' ? 'messaging' :
                                                                       'email' : 'email' %>">

              <!-- Delivery Status for outbound messages - positioned on left side -->
              <% if message.delivery_status.present? %>
                <div class="delivery-status-container absolute left-0 top-1/2 -translate-y-1/2 translate-x-[calc(-100%-8px)]" data-message-id="<%= message.id %>">
                  <% case message.delivery_status %>
                  <% when 'delivered' %>
                    <div class="h-8 w-8 rounded-full bg-white/80 border border-gray-100 shadow-md flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400">
                        <polyline points="20 6 9 17 4 12"></polyline>
                      </svg>
                    </div>
                    <span class="absolute top-full left-1/2 -translate-x-1/2 text-xs mt-1.5 font-medium text-gray-500 tracking-tight whitespace-nowrap">Delivered</span>
                  <% when 'read' %>
                    <div class="h-8 w-8 rounded-full bg-white/80 border border-gray-100 shadow-md flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500">
                        <path d="M18 6 L7 17 L2 12"></path>
                        <path d="M22 10 L13 19 L11 17"></path>
                      </svg>
                    </div>
                    <span class="absolute top-full left-1/2 -translate-x-1/2 text-xs mt-1.5 font-medium text-blue-500 tracking-tight whitespace-nowrap">Read</span>
                  <% when 'failed' %>
                    <div class="relative delivery-status-failed" data-tippy-content="<%= message.failure_reason.present? ? message.failure_reason : 'Message delivery failed' %>">
                      <div class="h-8 w-8 rounded-full bg-white/80 border border-gray-100 shadow-md flex items-center justify-center cursor-help">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="text-red-500">
                          <circle cx="12" cy="12" r="10"></circle>
                          <line x1="12" y1="8" x2="12" y2="12"></line>
                          <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                      </div>
                      <span class="absolute top-full left-1/2 -translate-x-1/2 text-xs mt-1.5 font-medium text-red-500 tracking-tight whitespace-nowrap">Failed</span>
                    </div>
                  <% end %>
                </div>
              <% end %>

              <%= simple_format message.content %>
            </div>
          <% else %>
            <div class="group relative <%= message.label.present? ?
                            message.label == 'complaint' ? 'border-red-400 bg-red-50' :
                            message.label == 'urgent' ? 'border-amber-400 bg-amber-50' :
                            message.label == 'follow_up' ? 'border-blue-400 bg-blue-50' :
                            message.label == 'spam' ? 'border-gray-400 bg-gray-50' :
                            message.label == 'attention' ? 'border-purple-400 bg-purple-50' :
                            'border-gray-200/50 bg-gray-100' :
                            'border-gray-200/50 bg-gray-100' %>  p-3 rounded-2xl rounded-bl-none text-sm shadow-sm border transition-transform duration-300 ease-in-out message-bubble"
                 data-message-type="<%= message.source.present? ? message.source.downcase == 'email' ? 'email' :
                                                          message.source.downcase == 'whatsapp' || message.source.downcase == 'sms' ? 'messaging' :
                                                          'email' : 'email' %>">
              <% if message.label.present? %>
                <div class="flex items-center mb-2 pb-2 border-b border-gray-200/50">
                  <% if message.label == 'complaint' %>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle-alert h-3.5 w-3.5 text-red-500">
                      <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path>
                      <path d="M12 9v4"></path>
                      <path d="M12 17h.01"></path>
                    </svg>
                    <span class="ml-1.5 text-xs font-medium">Complaint</span>
                  <% elsif message.label == 'urgent' %>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-3.5 w-3.5 text-amber-500">
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                    <span class="ml-1.5 text-xs font-medium">Urgent</span>
                  <% elsif message.label == 'follow_up' %>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar-clock h-3.5 w-3.5 text-blue-500">
                      <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5"></path>
                      <path d="M16 2v4"></path>
                      <path d="M8 2v4"></path>
                      <path d="M3 10h5"></path>
                      <path d="M17.5 17.5 16 16.3V14"></path>
                      <circle cx="16" cy="16" r="6"></circle>
                    </svg>
                    <span class="ml-1.5 text-xs font-medium">Follow-up Required</span>
                  <% elsif message.label == 'spam' %>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-alert h-3.5 w-3.5 text-gray-500">
                      <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path>
                      <path d="M12 8v4"></path>
                      <path d="M12 16h.01"></path>
                    </svg>
                    <span class="ml-1.5 text-xs font-medium">Spam</span>
                  <% elsif message.label == 'attention' %>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flag h-3.5 w-3.5 text-purple-500">
                      <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                      <line x1="4" x2="4" y1="22" y2="15"></line>
                    </svg>
                    <span class="ml-1.5 text-xs font-medium">Attention Required</span>
                  <% end %>
                </div>
              <% end %>
              <div class="absolute -left-10 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <button class="h-8 w-8 rounded-full bg-white/80 backdrop-blur-sm border border-gray-100 shadow-md flex items-center justify-center hover:bg-white transition-all duration-200" type="button" id="message-menu-button-<%= message.id %>" aria-haspopup="true" aria-expanded="false" onclick="toggleDropdown('<%= message.id %>')">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-500">
                    <circle cx="12" cy="12" r="1"></circle>
                    <circle cx="12" cy="5" r="1"></circle>
                    <circle cx="12" cy="19" r="1"></circle>
                  </svg>
                </button>

                <ul id="message-menu-<%= message.id %>" class="hidden w-56 bg-gray-100/95 backdrop-blur-md border border-gray-200 p-1 rounded-xl shadow-lg absolute left-0 top-full mt-2" style="z-index: 1000;">
                  <li>
                    <%= link_to update_label_admin_conversation_path(message.conversation_id, message_id: message.id, label: 'complaint'), method: :post, class: "dropdown-item flex items-center px-3 py-2 text-sm hover:bg-red-100 rounded-lg" do %>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle-alert h-4 w-4 text-red-500 mr-2">
                        <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path>
                        <path d="M12 9v4"></path>
                        <path d="M12 17h.01"></path>
                      </svg>
                      Mark as Complaint
                    <% end %>
                  </li>

                  <li>
                    <%= link_to update_label_admin_conversation_path(message.conversation_id, message_id: message.id, label: 'urgent'), method: :post, class: "dropdown-item flex items-center px-3 py-2 text-sm hover:bg-amber-100 rounded-lg" do %>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-4 w-4 text-amber-500 mr-2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                      </svg>
                      Mark as Urgent
                    <% end %>
                  </li>

                  <li>
                    <%= link_to update_label_admin_conversation_path(message.conversation_id, message_id: message.id, label: 'follow_up'), method: :post, class: "dropdown-item flex items-center px-3 py-2 text-sm hover:bg-blue-100 rounded-lg" do %>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar-clock h-4 w-4 text-blue-500 mr-2">
                        <path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5"></path>
                        <path d="M16 2v4"></path>
                        <path d="M8 2v4"></path>
                        <path d="M3 10h5"></path>
                        <path d="M17.5 17.5 16 16.3V14"></path>
                        <circle cx="16" cy="16" r="6"></circle>
                      </svg>
                      Mark for Follow-up
                    <% end %>
                  </li>

                  <li>
                    <%= link_to update_label_admin_conversation_path(message.conversation_id, message_id: message.id, label: 'spam'), method: :post, class: "dropdown-item flex items-center px-3 py-2 text-sm hover:bg-gray-100 rounded-lg" do %>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shield-alert h-4 w-4 text-gray-500 mr-2">
                        <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path>
                        <path d="M12 8v4"></path>
                        <path d="M12 16h.01"></path>
                      </svg>
                      Mark as Spam
                    <% end %>
                  </li>

                  <li>
                    <%= link_to update_label_admin_conversation_path(message.conversation_id, message_id: message.id, label: 'attention'), method: :post, class: "dropdown-item flex items-center px-3 py-2 text-sm hover:bg-purple-100 rounded-lg" do %>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flag h-4 w-4 text-purple-500 mr-2">
                        <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
                        <line x1="4" x2="4" y1="22" y2="15"></line>
                      </svg>
                      Mark for Attention
                    <% end %>
                  </li>
                </ul>
              </div>

              <div class="message-content">
                <%= simple_format message.content %>
              </div>
            </div>
          <% end %>
        </div>

        <div class="flex items-center gap-2 mt-1.5 ml-3 read-receipt-container">
          <div class="flex items-center gap-2">
            <div class="text-xs text-gray-500 font-medium"><%= message.formatted_timestamp %></div>

            <% if (message.read_receipts.any? || message.read_at.present?) && !is_outbound %>
              <div class="h-1 w-1 bg-gray-300 rounded-full dot-separator"></div>
              <div class="text-xs text-blue-500 read-by-text">
                <% if message.read_receipts.any? %>
                  <%= message.readers_list %>
                <% else %>
                  Read by <%= message.reader&.full_name || 'User' %>
                  <% if message.reader && message.reader.respond_to?(:role) && message.reader.role.present? %>
                    and <%= message.reader.role %>
                  <% end %>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
