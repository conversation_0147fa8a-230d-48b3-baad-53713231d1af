<%
  is_show_page = defined?(@conversation)
  current_conversation_id = @conversation.id if is_show_page
%>

<div class="w-[5rem] bg-white border-r border-gray-100 flex flex-col items-center py-6 space-y-4 mr-3 rounded-2xl">
  <!-- Inbox Button -->
  <div class="flex flex-col items-center gap-0.5">
    <%= link_to(is_show_page && current_conversation_id ? conversation_nav_link(controller, current_conversation_id, nil, params[:message_filter], defined?(resource) ? resource : nil) : admin_conversations_path, class: "contents", data: { state: "closed" }) do %>
      <div class="bg-amber-200 h-12 w-12 rounded-full flex items-center justify-center mb-0.5 <%= params[:filter].blank? ? 'ring-2 ring-blue-400 ring-offset-2' : '' %>">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-5 w-5 text-gray-700">
          <rect width="20" height="16" x="2" y="4" rx="2"></rect>
          <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
        </svg>
      </div>
      <span class="text-xs font-medium text-gray-600 text-center w-full">Inbox</span>
    <% end %>
  </div>

  <div class="w-full h-px bg-gray-200 my-2"></div>

  <!-- Communication Accounts -->
  <% current_user.communication_accounts.each do |acct| %>
    <div class="flex flex-col items-center gap-0.5">
      <%= link_to(is_show_page && current_conversation_id ? conversation_nav_link(controller, current_conversation_id, acct.identifier, params[:message_filter], defined?(resource) ? resource : nil) : admin_conversations_path(filter: acct.identifier, message_filter: params[:message_filter]), class: "contents", data: { state: "closed" }) do %>
        <% communication_method_klass = case acct.account_type
           when 'email' then 'bg-purple-200'
           when 'sms' then 'bg-blue-200'
           when 'whatsapp' then 'bg-green-200'
           else 'bg-gray-200'
        %>

      <% end %>
        <div class="<%= communication_method_klass %> h-12 w-12 rounded-full flex items-center justify-center mb-0.5 <%= params[:filter] == acct.identifier ? 'ring-2 ring-blue-400 ring-offset-2' : '' %>">
          <% if acct.account_type == 'email' %>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-at-sign h-5 w-5 text-gray-700">
              <circle cx="12" cy="12" r="4"></circle>
              <path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8"></path>
            </svg>
          <% elsif acct.account_type == 'sms' %>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-square h-5 w-5 text-gray-700">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
          <% else %>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-at-sign h-5 w-5 text-gray-700">
              <circle cx="12" cy="12" r="4"></circle>
              <path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8"></path>
            </svg>
          <% end %>
        </div>
        <span class="text-xs font-medium text-gray-600 text-center w-full"><%= acct.display_name %></span>
      <% end %>
    </div>
  <% end %>

  <div class="w-full h-px bg-gray-200 my-2"></div>

  <!-- Flagged Button -->
  <div class="flex flex-col items-center gap-0.5">
    <%= link_to(is_show_page && current_conversation_id ? conversation_nav_link(controller, current_conversation_id, 'flag', params[:message_filter], defined?(resource) ? resource : nil) : admin_conversations_path(filter: 'flag', message_filter: params[:message_filter]), class: "contents", data: { state: "closed" }) do %>
      <div class="bg-red-200 h-12 w-12 rounded-full flex items-center justify-center mb-0.5 <%= params[:filter] == 'flag' ? 'ring-2 ring-blue-400 ring-offset-2' : '' %>">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flag h-5 w-5 text-gray-700">
          <path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path>
          <line x1="4" x2="4" y1="22" y2="15"></line>
        </svg>
      </div>
      <span class="text-xs font-medium text-gray-600 text-center w-full">Flagged</span>
    <% end %>
  </div>

  <!-- Archived Button -->
  <div class="flex flex-col items-center gap-0.5">
    <%= link_to(is_show_page && current_conversation_id ? conversation_nav_link(controller, current_conversation_id, 'archived', params[:message_filter], defined?(resource) ? resource : nil) : admin_conversations_path(filter: 'archived', message_filter: params[:message_filter]), class: "contents", data: { state: "closed" }) do %>
      <div class="bg-red-200 h-12 w-12 rounded-full flex items-center justify-center mb-0.5 <%= params[:filter] == 'archived' ? 'ring-2 ring-blue-400 ring-offset-2' : '' %>">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-archive h-5 w-5 text-gray-700">
          <rect width="20" height="5" x="2" y="3" rx="1"></rect>
          <path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"></path>
          <path d="M10 12h4"></path>
        </svg>
      </div>
      <span class="text-xs font-medium text-gray-600 text-center w-full">Archived</span>
    <% end %>
  </div>

  <div class="w-full h-px bg-gray-200 my-2"></div>

  <!-- Internal Button -->
  <div class="flex flex-col items-center gap-0.5">
    <%= link_to(is_show_page && current_conversation_id ? conversation_nav_link(controller, current_conversation_id, 'internal', params[:message_filter], defined?(resource) ? resource : nil) : admin_conversations_path(filter: 'internal', message_filter: params[:message_filter]), class: "contents", data: { state: "closed" }) do %>
      <div class="bg-purple-100 h-12 w-12 rounded-full flex items-center justify-center mb-0.5 <%= params[:filter] == 'internal' ? 'ring-2 ring-blue-400 ring-offset-2' : '' %>">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-inbox h-5 w-5 text-gray-700">
          <polyline points="22 12 16 12 14 15 10 15 8 12 2 12"></polyline>
          <path d="M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path>
        </svg>
      </div>
      <span class="text-xs font-medium text-gray-600 text-center w-full">Internal</span>
    <% end %>
  </div>

  <!-- Lab Work Button -->
  <div class="flex flex-col items-center gap-0.5">
    <%= link_to(is_show_page && current_conversation_id ? conversation_nav_link(controller, current_conversation_id, 'lab', params[:message_filter], defined?(resource) ? resource : nil) : admin_conversations_path(filter: 'lab', message_filter: params[:message_filter]), class: "contents", data: { state: "closed" }) do %>
      <div class="bg-blue-100 h-12 w-12 rounded-full flex items-center justify-center mb-0.5 <%= params[:filter] == 'lab' ? 'ring-2 ring-blue-400 ring-offset-2' : '' %>">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-beaker h-5 w-5 text-gray-700">
          <path d="M4.5 3h15"></path>
          <path d="M6 3v16a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V3"></path>
          <path d="M6 14h12"></path>
        </svg>
      </div>
      <span class="text-xs font-medium text-gray-600 text-center w-full">Lab Work</span>
    <% end %>
  </div>
</div>
