<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Audit Trail</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
    
    body {
      font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
      margin: 0;
      padding: 20px;
      line-height: 1.4;
    }
    
    .header {
      margin-bottom: 20px;
    }
    
    .header h1 {
      font-size: 16px;
      font-weight: bold;
      margin: 0 0 10px 0;
    }
    
    .section {
      margin-bottom: 20px;
    }
    
    .section h2 {
      font-size: 14px;
      font-weight: bold;
      margin: 0 0 5px 0;
      color: #333;
    }
    
    .section p {
      font-size: 12px;
      margin: 2px 0;
      color: #555;
    }
    
    .verification-item {
      margin: 5px 0;
      padding: 5px 0;
    }
    
    .compliance-statement {
      margin-top: 40px;
      padding: 15px;
      background-color: #f8f9fa;
      border-left: 4px solid #28a745;
      font-size: 10px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>Audit Trail</h1>
  </div>
  
  <div class="section">
    <h2>Document Information</h2>
    <p>Document: <%= @document.title %></p>
    <p>Document ID: <%= @document.id %></p>
  </div>
  
  <div class="section">
    <h2>Signing Information</h2>
    <p>Signed by: <%= @signature_request.patient.full_name %></p>
    <p>Signature type: <%= @signature_request.signature_type&.titleize || 'Electronic' %></p>
    <p>Signed on: <%= @signature_request.completed_at ? @signature_request.completed_at.strftime('%d/%m/%Y at %H:%M') : 'N/A' %></p>
  </div>
  
  <div class="section">
    <h2>Verification Methods</h2>
    <% @signature_request.signature_verifications.each do |verification| %>
      <div class="verification-item">
        <% case verification.verification_type %>
        <% when 'patient_pin' %>
          <p>Patient PIN: <%= verification.successful ? 'Verified' : 'Failed' %></p>
        <% when '2fa' %>
          <p>Two-Factor Authentication: <%= verification.successful ? 'Verified' : 'Failed' %></p>
        <% when 'staff_override' %>
          <% witness_name = verification.signature_request.witnessed_by&.full_name %>
          <p>Staff Override: By <%= witness_name || 'Unknown' %></p>
        <% end %>
      </div>
    <% end %>
  </div>
  
  <div class="section">
    <h2>Device Information</h2>
    <p>IP Address: <%= @signature_request.ip_address %></p>
    <p>Device Type: <%= @signature_request.device_type %></p>
    <p>Operating System: <%= @signature_request.os_name %> <%= @signature_request.os_version %></p>
    <p>Browser: <%= @signature_request.browser_name %> <%= @signature_request.browser_version %></p>
    
    <% if @signature_request.sinch_fingerprint.present? %>
      <p>Sinch Verification ID: <%= @signature_request.sinch_fingerprint %></p>
    <% end %>
  </div>
  
  <div class="compliance-statement">
    This electronic signature complies with eIDAS Regulation (EU) No 910/2014 and UK Electronic Communications Act 2000.
  </div>
</body>
</html>
