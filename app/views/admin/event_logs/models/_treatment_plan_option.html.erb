<% if log.create_or_delete? %>
  <% if changes.has_key?('treatment_coordinator_id') %>
    Responsible Team member: <%= User.find_by(id: changes['treatment_coordinator_id']).full_name %><br />
  <% end %>

  <% if changes.has_key?('treatment_plan_id') %>
    Treatment Plan: <%= TreatmentPlan.find_by(id: changes['treatment_plan_id']).name %><br />
  <% end %>
<% elsif log.object_update? %>
  <% changes.each_pair do |attribute, change| %>
    <%= render partial: 'admin/event_logs/common/generic_attributes', locals: {attribute: attribute, before: change[0], after: change[1]} %>
  <% end %>
<% end %>
