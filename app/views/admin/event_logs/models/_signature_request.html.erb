<% if log.create_or_delete? %>
  <% if changes.has_key?('created_by_id') %>
    Created By: <%= User.find_by(id: changes['created_by_id']).full_name %><br />
  <% end %>

  <% if changes.has_key?('due_by') %>
    Due Date: <%= Time.parse(changes['due_by']).strftime('%d %B %Y') %><br />
  <% end %>

  <% if changes.has_key?('charting_appointment_id') %>
    Charted Appointment: #<%= changes['charting_appointment_id'] %><br />
  <% end %>

  <% if changes.has_key?('sent_at') %>
    Sent At: <%= Time.parse(changes['sent_at']).strftime('%d %B %Y at %H:%M') %><br />
  <% end %>
<% elsif log.object_update? %>
  <% changes.each_pair do |attribute, change| %>
    <%= render partial: 'admin/event_logs/common/generic_attributes', locals: {attribute: attribute, before: change[0], after: change[1]} %>
  <% end %>
<% end %>
