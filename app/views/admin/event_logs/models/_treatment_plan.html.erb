<% if log.create_or_delete? %>
  <% if changes.has_key?('name') %>
    Treatment Plan Name: <%= changes['name'] %><br />
  <% end %>

  <% if changes.has_key?('signature_date') %>
    Estimated Signature Date: <%= Time.parse(changes['signature_date']).strftime('%d %B %Y') %><br />
  <% end %>
<% elsif log.object_update? %>
  <% changes.each_pair do |attribute, change| %>
    <%= render partial: 'admin/event_logs/common/generic_attributes', locals: {attribute: attribute, before: change[0], after: change[1]} %>
  <% end %>
<% end %>
