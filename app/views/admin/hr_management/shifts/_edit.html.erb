<%= form_with(model: @shift, url: admin_hr_management_shift_path(@shift), method: :patch, local: true, id: 'editEventForm') do |form| %>
  <div class="mb-4">
    <div class="flex items-center">
      <%= form.label :shift_type, "Type", class: "block text-sm font-medium text-gray-700" %>
      <i class="fa-solid fa-circle-info text-gray-500 hover:text-gray-700 cursor-pointer"
        data-tippy-content="Clinical shift will open the calendar for booking, while Operational shift will not.">
      </i>
    </div>
    <%= form.select :shift_type,
                 options_for_select(shift_type_options_for_select, @shift.shift_type),
                 { include_blank: false },
                 { name: 'shift[shift_type]',
                   class: "form-select mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",
                   id: "editShiftType" } %>
  </div>
  <div class="mb-4">
    <label for="editDate" class="block text-sm font-medium text-gray-700 mb-1">
      Date
    </label>
    <%= form.date_field :date,
      class: "datepicker w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",
      id: "editDate",
      name: "shift[date]",
      value: @shift.date,
      required: true %>
  </div>

  <div class="mb-3" id="surgerySelector">
    <%= form.label :surgery_id, "Surgery", class: "form-label" %>
    <%= form.select :surgery_id,
          options_for_select(
            [["Select Surgery", ""]] + (@practice&.num_surgeries.to_i > 0 ?
              (1..@practice.num_surgeries).map { |n| ["Surgery #{n}", n] } : []),
            @shift.surgery_id
          ),
          {},
          { class: "form-select mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm", id: "editSurgery", name: "shift[surgery_id]" } %>
  </div>

  <div class="mb-3">
    <%= form.label :working_location, "Working Location", class: "form-label" %>
    <%= form.select :working_location,
      [["Practice", "practice"], ["Remote", "remote"], ["Other", "other"]], {},
      {
        class: "form-select form-select mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm",
        id: "editLocation", name: "shift[working_location]"
      }
    %>
  </div>

  <div class="mb-4">
    <label for="editEventStart" class="block text-sm font-medium text-gray-700 mb-1">
      Shift Start
    </label>
    <%= form.time_field :event_start,
      class: "timepicker w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",
      id: "editEventStart",
      name: "shift[event_start]",
      value: @shift.event_start.in_time_zone,
      required: true %>
  </div>

  <div class="mb-4">
    <label for="editEventEnd" class="block text-sm font-medium text-gray-700 mb-1">
      Shift End
    </label>
    <%= form.time_field :event_end,
      class: "timepicker w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",
      id: "editEventEnd",
      name: "shift[event_end]",
      value: @shift.event_end.in_time_zone,
      required: true %>
  </div>

  <div class="pt-4 mb-2 flex justify-center">
    <%= form.submit 'Update Shift', name: 'commit', class: 'cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-8 py-3 bg-[#a8d8ea] hover:bg-[#8fcbdf] text-black rounded-full font-medium transition-all text-[14px]' %>
  </div>
<% end %>
<div class="pt-4 mb-2 flex justify-center">
  <%= link_to 'Delete Shift', admin_hr_management_shift_path(@shift), method: :delete,
        data: { confirm: "Are you sure you want to delete this event?" },
        class: "bg-red-100 text-red-700 hover:bg-red-200 font-semibold h-10 py-3 px-8 rounded-full",
        role: "button" %>
</div>
<div class="pt-4 mb-2 flex justify-center">
  <%= link_to 'Delete All Occurrences', admin_hr_management_shift_path(@shift, delete_recurrent: true), method: :delete,
              data: { confirm: "Are you sure you want to delete this event and all future occurrences of this event?" },
              class: "bg-red-100 text-red-700 hover:bg-red-200 font-semibold h-10 py-3 px-8 rounded-full",
              role: "button" %>
</div>
