<div class="grid-cell grid-day-cell grid-day-cell-<%= user_id %> bg-white p-3 border border-gray-100" __v0_r="1,6053,6184">
  <% user_events.each do |shift| %>
    <% if !shift.fetch(:is_holiday) && date == shift.fetch(:date) %>
      <button
        class="active:scale-95 offcanvas-trigger bg-[#f3e8fc] border border-[#e8d4f0] rounded-lg py-1 px-3 text-center text-sm shadow-sm flex items-center justify-center space-x-1.5"
        data-trigger-offcanvas-id="shifts-edit-offcanvas"
        data-trigger-url="<%= edit_admin_hr_management_shift_path(shift) %>"
        data-trigger-data="<%= JSON.generate({}) %>"
        data-trigger-close-id="shifts-edit-offcanvas-close"
      >
        <span class="text-xs text-[#9b7aa8] font-medium">SHIFT</span><span class="text-[#6b5a75] font-medium text-xs">
          <%= "#{shift.fetch(:event_start).in_time_zone.strftime('%H:%M')} - #{shift.fetch(:event_end).in_time_zone.strftime('%H:%M')}" %>
        </span>
      </button>
    <% end %>
  <% end %>
</div>
