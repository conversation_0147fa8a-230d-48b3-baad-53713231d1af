<div class="min-h-[calc(100vh-56px)] bg-[#f5f5f7]">
  <div class="flex-1 container max-w-5xl mx-auto py-8 px-4 pb-24 bg-[#f5f5f7]">
    <%= render partial: 'layouts/treatment_plan_option', locals: { type: @type } %>
    <div class="space-y-8 text-sm tp-information-step">
      <div class="space-y-8">
        <h1 class="text-3xl font-semibold text-[#1d1d1f]">Your Treatment Plan</h1>
        <%= form_for([:admin, @treatment_plan_option], url: wizard_path, method: :put) do |form| %>
          <div class="mt-3">
            <%= form.label :patient_id, "Customer", class: "text-sm font-medium text-[#1d1d1f]" %>
            <div class="mt-3 mb-5">
              <%= form.select :patient_id, [],
                              {},
                              class: "form-control mb-3",
                              disabled: !@treatment_plan_option.patient_id.blank?,
                              data: {
                                patient: (@treatment_plan_option.patient.present?) ? {
                                  id: @treatment_plan_option.patient_id,
                                  text: patient_information(@treatment_plan_option.patient)
                                } : {},
                                practice_id: @treatment_plan_option.treatment_plan.practice_id
                              }
              %>
            </div>
          </div>
          <div class="mb-5 transition-all duration-300 ease-in-out ai-notes">
            <div class="flex items-center justify-between mb-3">
              <%= form.label :reason, "Reason for plan", class: "text-sm font-medium text-[#1d1d1f] flex items-center gap-2" %>
              <div class="relative inline-block">
                <button class="toggle-reason-template-dropdown justify-center whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2
                  focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50
                  [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border bg-background h-9 px-3
                  rounded-full border-[#e5e5e5] text-[#1d1d1f] hover:bg-[#f5f5f7] hover:text-[#1d1d1f] flex items-center gap-1
                  transition-all duration-200 hover:shadow-sm bg-white" type="button">
                  <span>Reasons Templates</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3.5 w-3.5 opacity-70">
                    <path d="m6 9 6 6 6-6"></path>
                  </svg>
                </button>
                <%= render 'treatment_reason_templates' %>
              </div>
            </div>
            <div class="relative group">
              <%= form.text_field :reason, class: 'tinymce_editor ai-textarea' %>
              <%= render 'admin/treatment_plan_options/option/ai_tools', unique_id: 'reason-ai' %>
            </div>
          </div>
          <div class="mb-5 transition-all duration-300 ease-in-out ai-notes">
            <div class="flex items-center justify-between mb-3">
              <%= form.label :services_provided, "Services Provided", class: "text-sm font-medium text-[#1d1d1f] flex items-center gap-2" %>
              <div class="relative inline-block">
                <button class="toggle-service-template-dropdown justify-center whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2
                  focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4
                  [&amp;_svg]:shrink-0 border bg-background h-9 px-3 rounded-full border-[#e5e5e5] text-[#1d1d1f] hover:bg-[#f5f5f7] hover:text-[#1d1d1f]
                  flex items-center gap-1 transition-all duration-200 hover:shadow-sm bg-white" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«rf»" data-state="closed">
                  <span>Services Templates</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3.5 w-3.5 opacity-70">
                    <path d="m6 9 6 6 6-6"></path>
                  </svg>
                </button>
                <%= render 'treatment_services_templates' %>
              </div>
            </div>
            <div class="relative group">
              <%= form.text_field :services_provided, class: 'tinymce_editor ai-textarea' %>
              <%= render 'admin/treatment_plan_options/option/ai_tools', unique_id: 'services-provided-ai' %>
            </div>
          </div>
          <div class="mb-5 transition-all duration-300 ease-in-out ai-notes">
            <div class="flex items-center justify-between mb-3">
              <%= form.label :outcome, "Conclusion", class: "text-sm font-medium text-[#1d1d1f] flex items-center gap-2" %>
              <div class="relative inline-block">
                <button class="toggle-conclusion-template-dropdown justify-center whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2
                  focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4
                  [&amp;_svg]:shrink-0 border bg-background h-9 px-3 rounded-full border-[#e5e5e5] text-[#1d1d1f] hover:bg-[#f5f5f7] hover:text-[#1d1d1f]
                  flex items-center gap-1 transition-all duration-200 hover:shadow-sm bg-white" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«rf»" data-state="closed">
                  <span>Conclusion Templates</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-3.5 w-3.5 opacity-70">
                    <path d="m6 9 6 6 6-6"></path>
                  </svg>
                </button>

                <%= render 'treatment_conclusions_templates' %>
              </div>
            </div>
            <div class="relative group">
              <%= form.text_field :outcome, class: 'tinymce_editor ai-textarea' %>
              <%= render 'admin/treatment_plan_options/option/ai_tools', unique_id: 'conslusion-ai' %>
            </div>
          </div>

          <div class="fixed bottom-0 left-0 w-full py-4 px-6 border-t border-[#e5e5e5] bg-white shadow-md z-10">
            <div class="container max-w-5xl mx-auto flex justify-end gap-3">
              <% if wicked_step > 1 %>
                <%= link_to 'Previous', previous_wizard_path, class: 'inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm
              font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2
              focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50
              [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background hover:bg-accent
              hover:text-accent-foreground h-10 px-4 py-2 rounded-full border-[#e5e5e5] text-[#1d1d1f]' %>
              <% end %>
              <%= form.submit 'Next', id: 'next-button', class: 'inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm
            font-medium ring-offset-background transition-colors focus-visible:outline-none
            focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none
            disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 py-2 rounded-full
            bg-[#ff9f66] hover:bg-[#ff8c4d] text-white px-8 cursor-pointer', disabled: @treatment_plan_option.patient_id.blank? %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
