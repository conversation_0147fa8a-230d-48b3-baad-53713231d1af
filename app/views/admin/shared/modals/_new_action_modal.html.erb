<div class="fixed inset-0 bg-black/50 z-[3000] backdrop-blur-sm hidden" id="<%= modal_id %>-overlay"></div>
<div role="dialog" id="<%= modal_id %>" aria-labelledby="<%= modal_id %>-title" data-state="closed"
     class="fixed left-[50%] top-[50%] z-[3000] translate-x-[-50%] translate-y-[-50%] duration-200
            data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0
            data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95
            data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%]
            data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]
            hidden"
     tabindex="-1" class="modal-pointer-events">

  <div class="w-[700px] bg-white rounded-2xl shadow-lg mx-auto my-auto">
    <%= form_for [:admin, Action.new], as: :action_data, html: { id: "#{modal_id}-form" } do |form| %>
      <%= form.hidden_field :actionable_type, value: actionable_type %>
      <%= form.hidden_field :actionable_id, value: actionable_id %>
      <%= form.hidden_field :action_type, value: "task" %>
      <%= form.hidden_field :id, value: nil, id: "#{modal_id}-action-id" %>

      <div class="p-6 flex items-center justify-between">
        <h2 class="text-xl font-medium text-gray-800 modal-title-font" id="<%= modal_id %>-title">New action</h2>
        <button type="button" class="modal-close h-8 w-8 rounded-full flex items-center justify-center text-gray-500 hover:text-gray-700 hover:bg-gray-100/80 transition-colors border border-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x h-4 w-4">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
      </div>

      <div class="px-6 pb-6">
        <!-- Practice/Patient Toggle (only show when explicitly requested from actions sidebar) -->
        <% if defined?(show_practice_patient_toggle) && show_practice_patient_toggle %>
          <div class="flex justify-start mb-6">
            <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
              <button type="button" id="practice-filter-toggle" class="practice-patient-toggle min-w-[36px] pl-3 pr-4 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden focus:outline-none text-blue-800" data-filter-type="practice" data-bg-gradient="linear-gradient(135deg, #DBEAFE 0%, #BFDBFE 100%)" data-text-color="text-blue-800" data-icon-color="text-blue-700">
                <i class="fa-solid fa-building h-4 w-4 text-blue-700"></i>
                <span class="tab-text ml-2 whitespace-nowrap font-medium">Practice</span>
              </button>
              <button type="button" id="patient-filter-toggle" class="practice-patient-toggle min-w-[36px] px-3 h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden focus:outline-none bg-white text-gray-600 hover:bg-gray-50" data-filter-type="patient" data-bg-gradient="linear-gradient(135deg, #D1FAE5 0%, #A7F3D0 100%)" data-text-color="text-green-800" data-icon-color="text-green-700">
                <i class="fa-solid fa-user h-4 w-4 text-gray-500"></i>
                <span class="tab-text ml-2 whitespace-nowrap font-medium opacity-0 w-0 overflow-hidden">Patient</span>
              </button>
            </div>
          </div>
        <% end %>

        <!-- Patient Selection Field (hidden by default) -->
        <div id="patient-selection-container" class="mb-6 hidden">
          <label class="block font-medium text-gray-700 mb-1.5 modal-label-font">Select Patient</label>
          <div class="relative">
            <button type="button" class="flex items-center gap-2 bg-white border border-gray-300 rounded-lg px-3 py-2 hover:bg-gray-50 transition-colors shadow-sm w-full" id="patient-filter-toggle-btn" aria-haspopup="menu" aria-expanded="false">
              <div class="flex items-center gap-2 flex-1">
                <div class="relative">
                  <div class="flex-shrink-0 h-7 w-7 bg-gray-200 rounded-full flex items-center justify-center" id="selected-patient-avatar-container">
                    <i class="fa-solid fa-user text-gray-400 text-xs"></i>
                  </div>
                </div>
                <span id="selected-patient-name" class="text-sm font-medium text-gray-500">Select a patient</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 text-gray-500">
                <path d="m6 9 6 6 6-6"></path>
              </svg>
            </button>

            <!-- Patient Filter Dropdown Menu -->
            <div id="patient-filter-menu" class="absolute left-0 mt-2 w-full max-h-80 overflow-y-auto rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 hidden" role="menu" aria-orientation="vertical" aria-labelledby="patient-filter-toggle-btn">
              <div class="p-2 sticky top-0 bg-white border-b border-gray-100">
                <div class="relative">
                  <input type="text" id="patient-search" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Search patients...">
                  <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              <div class="py-1" id="patient-list" role="none">
                <!-- Loading placeholder -->
                <div id="patient-loading" class="px-4 py-2 text-sm text-gray-500 text-center">
                  Type to search for patients...
                </div>
                <!-- Patient list will be populated here via AJAX -->
              </div>
            </div>
          </div>
        </div>

        <!-- Action Type Selector -->
        <div class="flex justify-end mb-6">
          <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
            <% action_types = {
                 "task" => { bg_gradient: "linear-gradient(to right, #fef3c7, #fcd34d)", icon: "fa-light fa-list-check", text: "text-amber-800", icon_color: "text-amber-700" },
                 "comment" => { bg_gradient: "linear-gradient(to right, #dbeafe, #93c5fd)", icon: "fa-light fa-comment", text: "text-blue-800", icon_color: "text-blue-700" },
                 "reminder" => { bg_gradient: "linear-gradient(to right, #f3e8ff, #d8b4fe)", icon: "fa-light fa-bell", text: "text-purple-800", icon_color: "text-purple-700" },
                 "alert" => { bg_gradient: "linear-gradient(to right, #fee2e2, #fca5a5)", icon: "fa-light fa-triangle-exclamation", text: "text-red-800", icon_color: "text-red-700" },
                 "callback" => { bg_gradient: "linear-gradient(to right, #dcfce7, #86efac)", icon: "fa-light fa-phone", text: "text-green-800", icon_color: "text-green-700" },
                 "complaint" => { bg_gradient: "linear-gradient(to right, #ffedd5, #fdba74)", icon: "fa-light fa-file-exclamation", text: "text-orange-800", icon_color: "text-orange-700" }
               } %>
            <% action_types.each_with_index do |(type, config), index| %>
              <button type="button"
                     class="actiontab min-w-[36px] <%= type == 'task' ? 'pl-3 pr-4' : 'px-3' %> h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden focus:outline-none <%= type == 'task' ? config[:text] : 'bg-white text-gray-600 hover:bg-gray-50' %>"
                     <%= type == 'task' ? "data-initial-active=\"true\"" : '' %>
                     data-action-type="<%= type %>"
                     data-bg-gradient="<%= config[:bg_gradient] %>"
                     data-text-color="<%= config[:text] %>"
                     data-icon-color="<%= config[:icon_color] %>"
                     data-icon="<%= config[:icon] %>">
                <i class="<%= config[:icon] %> h-4 w-4 <%= config[:icon_color] %>"></i>
                <span class="tab-text text-xs font-medium tracking-wide <%= type == 'task' ? 'ml-1.5' : 'opacity-0 w-0' %> transition-all duration-300"><%= type.capitalize %></span>
              </button>
            <% end %>
          </div>
        </div>

        <div class="space-y-5">
          <!-- Description -->
          <div class="ai-notes">
            <label for="action_data_description" class="block font-medium text-gray-700 mb-1.5 modal-label-font">Description</label>
            <%= form.text_area :description,
                id: "action_data_description",
                class: "ai-textarea w-full h-24 px-4 py-3 rounded-lg border border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200/50 focus:ring-opacity-50 modal-input-font",
                placeholder: "Description" %>

            <!-- AI Tools positioned below the textarea -->
            <div class="mt-2 ai-input-btns">
              <div class="flex items-center justify-end gap-2">
                <div class="animation-wave hidden relative flex items-center h-8 px-3 py-1 mr-1 animate-fadeIn overflow-hidden rounded-xl backdrop-blur-md bg-[rgba(239,68,68,0.08)] border border-[rgba(239,68,68,0.2)] shadow-[0_0_12px_rgba(239,68,68,0.15)]">
                  <div class="absolute inset-0 bg-gradient-to-r from-[rgba(239,68,68,0.02)] to-[rgba(239,68,68,0.08)]"></div>
                  <div class="relative flex items-end justify-center h-4 gap-[3px] z-10">
                    <div class="rounded-full" style="width: 2px; height: 35%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.1s infinite alternate none running soundWave;"></div>
                    <div class="rounded-full" style="width: 1.5px; height: 60%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.3s infinite alternate none running soundWave;"></div>
                    <div class="rounded-full" style="width: 2px; height: 90%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0s infinite alternate none running soundWave;"></div>
                    <div class="rounded-full" style="width: 1.5px; height: 50%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.2s infinite alternate none running soundWave;"></div>
                    <div class="rounded-full" style="width: 2px; height: 75%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.15s infinite alternate none running soundWave;"></div>
                    <div class="rounded-full" style="width: 1.5px; height: 100%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.05s infinite alternate none running soundWave;"></div>
                    <div class="rounded-full" style="width: 2px; height: 65%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.25s infinite alternate none running soundWave;"></div>
                    <div class="rounded-full" style="width: 1.5px; height: 85%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.1s infinite alternate none running soundWave;"></div>
                    <div class="rounded-full" style="width: 2px; height: 45%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.3s infinite alternate none running soundWave;"></div>
                    <div class="rounded-full" style="width: 1.5px; height: 70%; background: linear-gradient(to top, rgba(239, 68, 68, 0.7), rgb(239, 68, 68)); box-shadow: rgba(239, 68, 68, 0.5) 0px 0px 4px; animation: 0.7s ease-in-out 0.2s infinite alternate none running soundWave;"></div>
                  </div>
                  <div class="ml-2 text-[10px] font-medium text-[#ef4444] tracking-wide opacity-80">
                    RECORDING
                  </div>
                </div>
                <button type="button" class="ai-record inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background
                          focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none
                          disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground
                          rounded-full relative overflow-hidden transition-all duration-300 h-9 w-9 bg-gradient-to-b from-[#fef2f2] to-[#fee2e2] border border-[#fecaca]
                          shadow-sm text-[#ef4444] hover:shadow-md hover:from-[#fee2e2] hover:to-[#fecaca] active:scale-95 backdrop-blur-sm" aria-label="Start recording" title="Start recording">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mic h-4 w-4">
                    <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                    <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                    <line x1="12" x2="12" y1="19" y2="22"></line>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Two Column Layout for Priority and Assignment -->
          <div class="grid grid-cols-2 gap-6">
            <!-- Priority Column -->
            <div>
              <label class="block font-medium text-gray-700 mb-1.5 modal-label-font">Priority</label>
              <%= form.hidden_field :priority, value: 'low', id: "action_data_priority" %>
              <div class="flex justify-start">
                <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
                  <% priority_levels = {
                       "low" => { bg_gradient: "linear-gradient(to right, #f0f9ff, #bae6fd)", icon: "fa-light fa-signal-good", text: "text-sky-800", icon_color: "text-sky-700" },
                       "medium" => { bg_gradient: "linear-gradient(to right, #fef3c7, #fcd34d)", icon: "fa-light fa-signal-strong", text: "text-amber-800", icon_color: "text-amber-700" },
                       "high" => { bg_gradient: "linear-gradient(to right, #ffedd5, #fdba74)", icon: "fa-light fa-signal", text: "text-orange-800", icon_color: "text-orange-700" },
                       "urgent" => { bg_gradient: "linear-gradient(to right, #fee2e2, #fca5a5)", icon: "fa-light fa-triangle-exclamation", text: "text-red-800", icon_color: "text-red-700" }
                     } %>
                  <% priority_levels.each_with_index do |(level, config), index| %>
                    <button type="button"
                           class="priority-toggle min-w-[36px] <%= level == 'low' ? 'pl-3 pr-4' : 'px-3' %> h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden focus:outline-none <%= level == 'low' ? config[:text] : 'bg-white text-gray-600 hover:bg-gray-50' %>"
                           <%= level == 'low' ? "data-initial-active=\"true\"" : '' %>
                           data-priority-level="<%= level %>"
                           data-bg-gradient="<%= config[:bg_gradient] %>"
                           data-text-color="<%= config[:text] %>"
                           data-icon-color="<%= config[:icon_color] %>"
                           data-icon="<%= config[:icon] %>">
                      <i class="<%= config[:icon] %> h-4 w-4 <%= config[:icon_color] %>"></i>
                      <span class="tab-text text-xs font-medium tracking-wide <%= level == 'low' ? 'ml-1.5' : 'opacity-0 w-0' %> transition-all duration-300"><%= level.capitalize %></span>
                    </button>
                  <% end %>
                </div>
              </div>
            </div>

            <!-- Assignment Type Column -->
            <div id="assignment-type-container">
              <label class="block font-medium text-gray-700 mb-1.5 modal-label-font text-right">Assignment</label>
              <%= form.hidden_field :assignment_type, value: 'date_time', id: "action_data_assignment_type" %>
              <div class="flex justify-end">
                <div class="flex items-center gap-2 bg-gray-50/90 p-1.5 rounded-full backdrop-blur-md border border-gray-200/60 shadow-sm">
                  <% assignment_types = {
                       "date_time" => { bg_gradient: "linear-gradient(to right, #f0f9ff, #bae6fd)", icon: "fa-thin fa-calendar-clock", text: "text-sky-800", icon_color: "text-sky-700" },
                       "appointment" => { bg_gradient: "linear-gradient(to right, #f3e8ff, #d8b4fe)", icon: "fa-light fa-tooth", text: "text-purple-800", icon_color: "text-purple-700" }
                     } %>
                  <% assignment_types.each_with_index do |(type, config), index| %>
                    <button type="button"
                           class="assignment-toggle min-w-[36px] <%= type == 'date_time' ? 'pl-3 pr-4' : 'px-3' %> h-9 rounded-full transition-all duration-300 ease-in-out flex items-center justify-center shadow-sm hover:shadow-md border border-gray-200/40 overflow-hidden focus:outline-none <%= type == 'date_time' ? config[:text] : 'bg-white text-gray-600 hover:bg-gray-50' %>"
                           <%= type == 'date_time' ? "data-initial-active=\"true\"" : '' %>
                           data-assignment-type="<%= type %>"
                           data-bg-gradient="<%= config[:bg_gradient] %>"
                           data-text-color="<%= config[:text] %>"
                           data-icon-color="<%= config[:icon_color] %>"
                           data-icon="<%= config[:icon] %>">
                      <i class="<%= config[:icon] %> h-4 w-4 <%= config[:icon_color] %>"></i>
                      <span class="tab-text text-xs font-medium tracking-wide <%= type == 'date_time' ? 'ml-1.5' : 'opacity-0 w-0' %> transition-all duration-300">
                        <%= type == 'date_time' ? 'Date/Time' : 'Appointment' %>
                      </span>
                    </button>
                  <% end %>
                </div>
              </div>
            </div>
          </div>

          <!-- Due Date / Appointment Assignment -->
          <div id="due-date-container">
            <label class="block font-medium text-gray-700 mb-1.5 modal-label-font">
              <span id="due-date-label">Date due</span>
            </label>

            <!-- Date/Time Assignment Section -->
            <div id="datetime-assignment-section">
              <!-- Date and time inputs (for all action types) -->
              <div id="split-datetime-inputs">
                <div class="grid grid-cols-2 gap-3">
                  <div class="relative">
                    <input type="text"
                           name="action_data[action_date]"
                           id="action_data_action_date"
                           class="w-full px-4 py-3 rounded-lg border border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200/50 focus:ring-opacity-50 modal-input-font"
                           placeholder="dd/mm/yyyy" />
                    <div class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 cursor-pointer hover:text-gray-600" data-date-picker-trigger>
                      <i class="fa-light fa-calendar-days"></i>
                    </div>
                  </div>
                  <div class="relative">
                    <input type="text"
                           name="action_data[action_time]"
                           id="action_data_action_time"
                           class="w-full px-4 py-3 rounded-lg border border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200/50 focus:ring-opacity-50 modal-input-font"
                           placeholder="HH:MM" />
                    <div class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 cursor-pointer hover:text-gray-600" data-time-picker-trigger>
                      <i class="fa-light fa-clock"></i>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Hidden field for the combined date_due value -->
              <%= form.hidden_field :date_due, id: "action_data_date_due" %>
            </div>

            <!-- Appointment Assignment Section -->
            <div id="appointment-assignment-section" class="hidden">
              <div class="grid grid-cols-2 gap-3">
                <!-- Treatment Plan Selector -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Treatment Plan</label>
                  <select id="action_data_treatment_plan_id"
                          name="action_data[treatment_plan_id]"
                          class="w-full px-4 py-3 rounded-lg border border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200/50 focus:ring-opacity-50 modal-input-font bg-white">
                    <option value="">Select treatment plan</option>
                  </select>
                </div>

                <!-- Appointment Selector -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Appointment</label>
                  <select id="action_data_appointment_id"
                          name="action_data[appointment_id]"
                          class="w-full px-4 py-3 rounded-lg border border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200/50 focus:ring-opacity-50 modal-input-font bg-white"
                          disabled>
                    <option value="">Select appointment</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- Assign Users -->
          <div>
            <label class="block font-medium text-gray-700 mb-1.5 modal-label-font">Notify team</label>
            <%
              # Get the card's team members if available
              card_team_member_ids = @card_team_member_ids if defined?(@card_team_member_ids)

              # If we're in a card context, get the team members
              if defined?(@card) && @card.respond_to?(:member_users)
                card_team_member_ids = @card.member_users.pluck(:id)
              end

              # Ensure it's an array
              card_team_member_ids ||= []
              selected_users = User.where(id: card_team_member_ids)
            %>

            <!-- Team Selection Interface -->
            <div class="border border-gray-200 rounded-lg bg-white">
              <!-- Search Input -->
              <div class="flex items-center px-3 border-b border-gray-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4 shrink-0 opacity-50">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.3-4.3"></path>
                </svg>
                <input
                  class="flex h-11 w-full rounded-md bg-transparent py-3 outline-none placeholder:text-gray-500 disabled:cursor-not-allowed disabled:opacity-50 text-sm"
                  placeholder="Search for team members..."
                  id="<%= modal_id %>-team-search-input"
                  type="text"
                  autocomplete="off"
                  autocorrect="off"
                  spellcheck="false"
                >
              </div>

              <!-- Selected Team Members -->
              <div class="p-2 <%= selected_users.any? ? '' : 'hidden' %>" id="<%= modal_id %>-selected-team-section">
                <h4 class="px-2 py-1.5 text-xs font-medium text-gray-500">Selected</h4>
                <div class="flex flex-wrap gap-2 p-2" id="<%= modal_id %>-selected-team-container">
                  <% selected_users.each do |user| %>
                    <div class="flex items-center gap-2 p-1.5 pr-2 bg-gray-100 rounded-md text-sm" data-user-id="<%= user.id %>">
                      <span class="relative flex shrink-0 overflow-hidden rounded-full h-6 w-6">
                        <%= render 'layouts/shared/user_avatar', user: user, width: 24, height: 24 %>
                      </span>
                      <span class="font-medium text-gray-900 whitespace-nowrap"><%= user.full_name %></span>
                      <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors hover:bg-red-100 h-5 w-5 text-gray-500 hover:text-red-600"
                              data-remove-user="<%= user.id %>"
                              aria-label="Remove <%= user.full_name %>">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3.5 w-3.5">
                          <path d="M18 6 6 18"></path>
                          <path d="m6 6 12 12"></path>
                        </svg>
                      </button>
                    </div>
                  <% end %>
                </div>
              </div>

              <!-- Available Team Members -->
              <div class="overflow-y-auto overflow-x-hidden max-h-48">
                <div class="-mx-1 h-px bg-gray-200"></div>
                <div class="overflow-hidden text-gray-900">
                  <div class="px-3 pt-3 pb-2 text-[11px] font-semibold text-gray-500 tracking-wider uppercase">Available Team Members</div>
                  <div id="<%= modal_id %>-team-members-list" class="px-3 pb-3">
                    <% User.without_archived.includes(:image_attachment).order(:first_name).each do |user| %>
                      <div class="relative cursor-pointer select-none rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-gray-50 transition-colors duration-150 flex items-center justify-between gap-3"
                           data-user-id="<%= user.id %>"
                           data-user-name="<%= user.full_name %>"
                           data-user-search="<%= user.full_name.downcase %>"
                           data-selected="<%= selected_users.include?(user) ? 'true' : 'false' %>"
                           title="Click to select <%= user.full_name %>">
                        <div class="flex items-center gap-3 flex-grow">
                          <input
                            type="checkbox"
                            class="h-4 w-4 shrink-0 rounded-sm border border-gray-300 focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 team-member-checkbox"
                            id="action-user-checkbox-<%= user.id %>"
                            name="action_data[assigned_to_json][]"
                            value="<%= user.id %>"
                            <%= selected_users.include?(user) ? 'checked' : '' %>
                            aria-label="Select <%= user.full_name %>"
                          >
                          <span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8">
                            <%= render 'layouts/shared/user_avatar', user: user, width: 32, height: 32 %>
                          </span>
                          <span class="text-sm text-gray-700"><%= user.full_name %></span>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>

        <div class="mt-8 flex justify-end gap-3">
          <button type="button" class="modal-cancel px-5 py-2.5 rounded-lg bg-gray-500 hover:bg-gray-600 text-white font-medium transition-colors modal-button-font">Close</button>
          <button type="submit" id="<%= modal_id %>-submit-button" class="px-5 py-2.5 rounded-lg font-medium shadow-sm hover:shadow-md border border-gray-200/40 transition-all duration-300 text-amber-800 modal-button-font modal-submit-button-initial" data-create-text="Create task" data-update-text="Update task">Create task</button>
        </div>
      </div>
    <% end %>
  </div>
</div>
