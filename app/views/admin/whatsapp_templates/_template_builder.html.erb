<%= form_for [:admin, @template] do |form| %>
  <div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-visible mb-6">
    <div class="p-6 border-b border-gray-200">
      <div class="flex items-center space-x-3 mb-6">
        <div class="flex-shrink-0 h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
          <span class="material-symbols-outlined text-green-600">edit</span>
        </div>
        <div>
          <h2 class="text-lg font-semibold text-gray-900">Template Builder</h2>
          <p class="text-sm text-gray-600">Configure your WhatsApp message template</p>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <%= form.label :name, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :name, class: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500", required: true, placeholder: "Enter template name" %>
        </div>
        <div>
          <%= form.label :category, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.select :category, [["Marketing", "MARKETING"], ["Utility", "UTILITY"]], {}, class: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500", required: true %>
        </div>
      </div>

      <div class="flex items-center mt-4 align-center">
        <%= form.label :automations_only, class: "block text-sm font-medium text-gray-700" %>
        <%= form.check_box :automations_only, class: "ml-3 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" %>
      </div>
    </div>

    <div class="p-6">
      <div id="template_components" class="space-y-4">
        <%= form.fields_for :whatsapp_components do |f| %>
          <%= render "whatsapp_component_fields", f: f %>
        <% end %>
      </div>
    </div>

    <div class="bg-gray-50 p-6 border-t border-gray-200 overflow-visible">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span class="material-symbols-outlined text-gray-600">add_circle</span>
          <span class="text-sm font-medium text-gray-900">Add Component</span>
        </div>
        <div class="flex items-center space-x-3">
          <div class="relative inline-block text-left">
            <button type="button" class="inline-flex items-center px-4 py-2 bg-green-100 hover:bg-green-200 text-green-800 rounded-full text-sm font-medium transition-colors component-dropdown-toggle" data-component="header">
              <span class="material-symbols-outlined text-sm mr-2">format_h1</span>
              Header
              <span class="material-symbols-outlined text-sm ml-1">expand_more</span>
            </button>
            <div class="component-dropdown-menu absolute left-0 z-[100] mt-2 w-48 rounded-md shadow-lg bg-white border border-gray-200 focus:outline-none hidden" data-component="header">
              <div class="py-1">
                <%= link_to_add_association form, :whatsapp_components, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", "data-association-insertion-node": "#template_components", "data-association-insertion-method": "append", "data-component-type": "location_header" do %>
                  <span class="material-symbols-outlined text-sm mr-3">location_on</span>
                  Location Header
                <% end %>
                <%= link_to_add_association form, :whatsapp_components, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", "data-association-insertion-node": "#template_components", "data-association-insertion-method": "append", "data-component-type": "text_header" do %>
                  <span class="material-symbols-outlined text-sm mr-3">text_fields</span>
                  Text Header
                <% end %>
                <%= link_to_add_association form, :whatsapp_components, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", "data-association-insertion-node": "#template_components", "data-association-insertion-method": "append", "data-component-type": "media_header" do %>
                  <span class="material-symbols-outlined text-sm mr-3">image</span>
                  Media Header
                <% end %>
              </div>
            </div>
          </div>

          <% [["Body", "body", "article"], ["Footer", "footer", "view_agenda"], ["Buttons", "buttons", "smart_button"]].each do |component_type, data_type, icon| %>
            <%= link_to_add_association form, :whatsapp_components, class: "inline-flex items-center px-4 py-2 bg-green-100 hover:bg-green-200 text-green-800 rounded-full text-sm font-medium transition-colors", "data-association-insertion-node": "#template_components", "data-association-insertion-method": "append", "data-component-type": data_type do %>
              <span class="material-symbols-outlined text-sm mr-2"><%= icon %></span>
              <%= component_type %>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
    <div class="flex justify-between items-center">
      <%= link_to edit_admin_general_settings_practice_whatsapp_templates_path(@template.practice), class: "flex items-center space-x-2 px-4 py-2 text-gray-700 bg-white hover:bg-gray-100 rounded-full font-medium transition-colors border border-gray-300" do %>
        <span class="material-symbols-outlined text-sm">arrow_back</span>
        <span>Back to Templates</span>
      <% end %>
      <%= form.submit 'Save Template', class: 'px-6 py-2 bg-green-100 hover:bg-green-200 text-green-800 rounded-full font-medium transition-colors focus:ring-2 focus:ring-green-500 focus:ring-offset-2' %>
    </div>
  </div>
<% end %>
