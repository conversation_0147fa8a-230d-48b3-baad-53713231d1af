<% patient = lab_work.patient %>
<% ld = lab_work.lab_dockets&.last %>
<% names = ld&.lab_docket_items&.map do |ldi|
    first_item = ldi.lab_items.first
    "#{first_item.treatment_type} - #{first_item.name}" if first_item.present?
  end&.compact %>
<div tabindex="0"
     data-source-title="<%= title %>"
     class="lab-work-card cursor-grab bg-white rounded-xl p-4 pb-3 mb-3 relative transition-all shadow-md hover:shadow-lg"
     data-practitioner-id="<%= lab_work.practitioner_id %>"
     data-lab-work-id="<%= lab_work.id %>">
  <div class="member hidden"><%= lab_work.practitioner.id %></div>
  <div class="mb-2 flex items-center"></div>
  <div class="flex justify-between items-start">
    <div class="flex-1">
      <div class="text-[15px] font-medium flex items-center patient-name">
        <%= link_to patient.full_name, admin_patient_url(patient.id), class: 'hover:text-blue-600 transition-colors text-left' %>
      </div>
      <div class="flex flex-wrap items-center gap-2 text-[12px] text-gray-500 mt-1 mb-1">
        <% names&.each do |name| %>
          <% work_color = ['sky', 'emerald', 'pink', 'amber', 'teal', 'violet'].sample %>
          <a class="px-2 py-0.5 rounded-md bg-<%= work_color %>-100" href="<%= admin_lab_work_url(lab_work.id) %>">
            <span class="font-medium capitalize text-<%= work_color %>-700">
              <%= name %>
            </span>
          </a>
        <% end %>
      </div>
      <div class="flex gap-2 mb-2"></div>
    </div>
    <button class="p-1 rounded-full hover:bg-gray-100 text-gray-500 hover:text-gray-700 cursor-pointer lw-actions-toggle"
            data-dropdown-toggle="lab-work-dropdown-<%= lab_work.id %>" data-no-drag>
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
           stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis w-4 h-4">
        <circle cx="12" cy="12" r="1"></circle>
        <circle cx="19" cy="12" r="1"></circle>
        <circle cx="5" cy="12" r="1"></circle>
      </svg>
    </button>

    <div id="lab-work-dropdown-<%= lab_work.id %>" class="absolute right-2 top-8 bg-white rounded-lg shadow-lg z-30 w-55 overflow-hidden lw-actions-menu hidden" data-no-drag>
      <% if lab_work.status == "Draft" %>
        <div class="py-1">
          <button class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 cursor-pointer">
            <a href="<%= edit_admin_lab_docket_path(lab_work.lab_dockets.where(draft: true).last.id) %>">Edit Draft</a>
          </button>
        </div>
      <% elsif lab_work.status != "Archive Case - Not proceeding" %>
        <div class="py-1">
          <button class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 cursor-pointer">
            <a href="<%= admin_lab_work_url(lab_work.id) %>">View Lab Work</a>
          </button>
        </div>
        <% if (link = lab_work_status_update_link(lab_work)) %>
          <div class="py-1">
            <button class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 cursor-pointer">
              <%= link %>
            </button>
          </div>
        <% end %>
        <div class="py-1 border-t border-gray-100">
          <button class="w-full px-3 py-2 text-left flex items-center text-[13px] hover:bg-gray-50 text-red-600 cursor-pointer">
          <%= link_to update_status_admin_lab_work_path(lab_work.id, lab_work: { status: 'Archive Case - Not proceeding' }), method: :post do %>
            Archive Lab Work
          <% end %>
          </button>
        </div>
      <% end %>
    </div>
  </div>

  <div class="flex flex-col space-y-1 text-[13px] text-gray-600">
    <div class="flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
           stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail w-3.5 h-3.5 mr-2 text-gray-400">
        <rect width="20" height="16" x="2" y="4" rx="2"></rect>
        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
      </svg>
      <span><%= patient.email %></span>
    </div>
    <div class="flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
           stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-phone w-3.5 h-3.5 mr-2 text-gray-400">
        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
      </svg>
      <span><%= patient.mobile_phone %></span>
    </div>
  </div>

  <div class="mt-3 pt-2 border-t border-gray-100 <%= 'bg-red-50 p-3 -mx-4 border border-red-200' if title == 'Late' %>">
    <div class="flex justify-between items-center mb-2">
      <% if title == 'Due Today' %>
        <div class="text-[12px] font-medium text-gray-500 w-full text-center">Due today</div>
      <% elsif title == 'Late' || title == 'Waiting For Quality Check' || title == 'Ready For Appointment' %>
        <div class="text-[12px] font-medium text-<%= color %>-500 w-full text-center"><%= title %></div>
      <% else %>
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <span class="text-[12px] font-medium text-gray-700">Due: <%= lab_work_date_return_for_board(lab_work) %></span>
        </div>
        <div class="text-[12px] font-medium text-gray-700">ETA: <%= ld.total_days + 1 %> <%= ld.total_days == 0 ? 'Day' : 'Days' %></div>
      <% end %>
    </div>
    <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
      <% if title == 'Due Today' || title == 'Late' || title == 'Waiting For Quality Check' || title == 'Ready For Appointment' %>
        <div class="h-full bg-<%= color %>-500 rounded-full <%= 'animate-pulse' if title == 'Late' %>" style="width: 100%;"></div>
      <% else %>
        <% width = percentage_and_progress_color(ld).first %>
        <div class="h-full bg-red-500 rounded-full" style="width: <%= width %>%;"></div>
      <% end %>
    </div>
  </div>

  <div class="mt-2 pt-2 border-t border-gray-100 group relative z-10">
    <div class="flex justify-between items-center">
      <% if lab_work.charting_appointment&.calendar_booking.present? %>
        <% appt = lab_work.charting_appointment&.calendar_booking %>
        <button class="flex items-center group hover:bg-gray-50 rounded-lg px-2 py-1 transition-colors">
          <div class="w-6 h-6 rounded-full bg-gradient-to-br from-purple-50 to-purple-100 flex items-center justify-center shadow-sm border border-purple-200 mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-3 h-3 text-purple-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                 stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
          </div>
          <div class="flex items-center">
            <span class="text-[12px] font-medium text-gray-700 mr-1"><%= appt.start_time.strftime('%-d %B') %></span>
            <span class="text-[11px] text-gray-500"><%= appt.start_time.strftime('%I:%M %p') %></span>
          </div>
        </button>
        <div class="relative">
          <div class="w-7 h-7 rounded-full bg-gray-200 border border-[#d1d1d6] overflow-hidden flex-shrink-0 shadow-sm">
            <%= image_tag appt.practitioner.image.attached? ? appt.practitioner.image : "default-avatar.webp", class: "w-full h-full object-cover",
                          data: {"tippy-content": appt.practitioner.full_name} %>
          </div>
        </div>
      <% else %>
        <div class="w-6 h-6 rounded-full bg-gradient-to-br from-purple-50 to-purple-100 flex items-center justify-center shadow-sm border border-purple-200 mr-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-3 h-3 text-purple-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
               stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
        </div>
        <span class="text-[12px] font-medium text-gray-700 mr-1">Not booked yet</span>
      <% end %>
    </div>
  </div>

  <div class="mt-3 pt-2 border-t border-gray-100">
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center bg-white rounded-full px-3 py-1.5 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
        <div class="w-5 h-5 rounded-full bg-gradient-to-br from-gray-50 to-white border border-gray-200 flex items-center justify-center mr-2 flex-shrink-0 overflow-hidden shadow-sm">
          <%= image_tag lab_work.lab.image.attached? ? lab_work.lab.image : "default-avatar.webp", class: 'w-full h-full object-cover' %>
        </div>
        <span class="lab hidden"><%= lab_work.lab.id %></span>
        <span class="text-[13px] font-medium text-gray-700 whitespace-nowrap"><%= lab_work.lab.name %></span>
      </div>
<!--      <div class="flex items-center">-->
<!--        <span class="px-2.5 py-1 rounded-full text-[11px] font-medium whitespace-nowrap bg-gradient-to-r from-emerald-50 to-emerald-100 text-emerald-700 border border-emerald-200 shadow-sm"><%#= days_since_created(lab_work) %> Days Ago</span>-->
<!--      </div>-->
    </div>
    <div class="bg-gradient-to-br from-gray-50/80 to-white/90 backdrop-blur-sm rounded-lg px-4 py-2 flex items-center border border-gray-100/80 shadow-sm hover:shadow transition-all duration-200">
      <div class="w-6 h-6 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center shadow-sm border border-blue-200 mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
        </svg>
      </div>
      <div class="flex items-center">
        <span class="text-[12px] font-medium text-gray-700 mr-2">Date Sent:</span>
        <span class="text-[12px] text-gray-500"><%= lab_work.created_at.strftime("%d/%m/%Y") %></span>
      </div>
    </div>
  </div>
</div>
