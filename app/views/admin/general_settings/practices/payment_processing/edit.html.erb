<div class="flex-1 flex flex-col transition-all duration-300 ease-in-out ml-72 p-8 main-content-area bg-gray-50">
  <%= render 'admin/general_settings/side_panel' %>

  <div class="flex items-center justify-between mb-8">
    <div>
      <h1 class="text-2xl font-semibold text-slate-800">Payment Processing</h1>
      <p class="text-sm text-gray-600 mt-1">Manage Stripe integration, terminals, and payment settings</p>
    </div>
    <%= link_to admin_general_settings_practices_path, class: "flex items-center space-x-2 px-4 py-2 text-gray-700 bg-white hover:bg-gray-50 rounded-full border border-gray-300 font-medium transition-colors" do %>
      <span class="material-symbols-outlined text-sm">arrow_back</span>
      <span>Back to Practices</span>
    <% end %>
  </div>

  <%= render 'admin/general_settings/practices/edit_navigation', active_tab: 'payment_processing' %>

  <div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
    <%= form_for @practice,
                 url: admin_general_settings_practice_opening_hours_path(practice_id: @practice),
                 method: :patch do |form| %>
      <% if @practice.errors.any? %>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 m-6">
          <div class="flex items-center">
            <span class="material-symbols-outlined text-red-600 mr-3">error</span>
            <div>
              <h4 class="text-sm font-medium text-red-800">There were some problems with your submission</h4>
              <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                <% @practice.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      <% end %>

      <div class="p-6">
        <!-- Payment Processing Nested Tabs -->
        <div class="border-b border-gray-200 mb-6">
          <nav class="flex space-x-8">
            <button type="button"
                    class="flex items-center py-3 px-1 border-b-2 font-medium text-sm payment-subtab-btn <%= (params[:transactions_starting_after].blank? && params[:transactions_starting_before].blank?) ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' %>"
                    id="stripe-subtab"
                    data-subtab="stripeSection">
              <span class="material-symbols-outlined mr-2 text-lg">payment</span>
              Stripe
            </button>

            <% if @bank_account != nil %>
              <button type="button"
                      class="flex items-center py-3 px-1 border-b-2 font-medium text-sm payment-subtab-btn <%= (params[:transactions_starting_after].present? || params[:transactions_starting_before].present?) ? 'border-blue-600 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' %>"
                      id="transactions-subtab"
                      data-subtab="transactionsSection">
                <span class="material-symbols-outlined mr-2 text-lg">receipt_long</span>
                Transactions
              </button>

              <button type="button"
                      class="flex items-center py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm payment-subtab-btn"
                      id="payouts-subtab"
                      data-subtab="payoutsSection">
                <span class="material-symbols-outlined mr-2 text-lg">account_balance</span>
                Payouts
              </button>
            <% end %>

            <button type="button"
                    class="flex items-center py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium text-sm payment-subtab-btn"
                    id="manual-subtab"
                    data-subtab="manualSection">
              <span class="material-symbols-outlined mr-2 text-lg">credit_card</span>
              Manual Payment Processing
            </button>
          </nav>
        </div>

        <div class="tab-content">
          <!-- STRIPE TAB -->
          <div class="payment-subtab-pane <%= (params[:transactions_starting_after].blank? && params[:transactions_starting_before].blank?) ? 'active' : 'hidden' %>"
               id="stripeSection">

            <!-- Stripe Connection Status -->
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Stripe Connection Status</h3>
              <div class="flex flex-wrap items-center gap-3 mb-4">
                <% if @practice.stripe_account_id.present? %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    <span class="material-symbols-outlined text-sm mr-1">check_circle</span>
                    Connect account created
                  </span>
                  <% if @stripe_account.charges_enabled %>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                      <span class="material-symbols-outlined text-sm mr-1">check_circle</span>
                      Charges enabled
                    </span>
                  <% else %>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                      <span class="material-symbols-outlined text-sm mr-1">error</span>
                      Charges not enabled - information required
                    </span>
                  <% end %>
                <% else %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                    <span class="material-symbols-outlined text-sm mr-1">error</span>
                    No Connect account
                  </span>
                <% end %>
              </div>

              <% if @practice.stripe_account_id.present? %>
                <a href="<%= onboarding_admin_general_settings_stripe_path(practice_id: @practice) %>"
                   class="inline-flex items-center px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full font-medium transition-colors"
                   target="_blank">
                  <span class="material-symbols-outlined text-sm mr-2">edit</span>
                  <%= @stripe_account.details_submitted ? "Edit Stripe Details" : "Stripe Onboarding" %>
                </a>
              <% else %>
                <a href="<%= connect_admin_general_settings_stripe_path(practice_id: @practice) %>"
                   class="inline-flex items-center px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full font-medium transition-colors">
                  <span class="material-symbols-outlined text-sm mr-2">add</span>
                  Create Connect account
                </a>
              <% end %>
            </div>

            <% if @practice.stripe_account_id.present? %>
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Terminals Section -->
                <div class="bg-gray-50 rounded-lg p-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Terminals</h3>
                    <button type="button"
                            class="inline-flex items-center px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full font-medium transition-colors"
                            data-toggle="modal"
                            data-target="#connectTerminalModal">
                      <span class="material-symbols-outlined text-sm mr-2">add</span>
                      Connect Terminal
                    </button>
                  </div>

                  <div class="overflow-hidden bg-white rounded-lg border border-gray-200">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                          <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <% @readers.each do |reader| %>
                          <tr class="hover:bg-gray-50">
                            <td class="px-4 py-3 text-sm font-medium text-gray-900"><%= reader.label %></td>
                            <td class="px-4 py-3 text-right">
                              <%= link_to "Reset Terminal",
                                          '',
                                          method: :post,
                                          class: "inline-flex items-center px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-medium transition-colors",
                                          data: { confirm: "Are you sure you want to reset payments on this terminal?" } %>
                            </td>
                          </tr>
                        <% end %>
                      </tbody>
                    </table>
                  </div>

                  <!-- Terminal Logo Upload -->
                  <div class="mt-6">
                    <h4 class="text-md font-semibold text-gray-900 mb-4">Terminal Logo</h4>
                    <div class="text-center">
                      <div class="mb-4">
                        <img src="<%= @practice.stripe_terminal_logo.attached? ? @practice.stripe_terminal_logo.url : "" %>"
                             alt="Terminal Logo"
                             class="fd-preview mx-auto max-w-full h-32 object-contain rounded-lg border border-gray-200 bg-white"/>
                      </div>
                      <%= form.file_field :stripe_terminal_logo, class: "d-none" %>
                      <div class="file-drop border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Bank Accounts Section -->
                <div class="bg-gray-50 rounded-lg p-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Bank Accounts</h3>
                    <button type="button"
                            class="inline-flex items-center px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full font-medium transition-colors"
                            data-toggle="modal"
                            data-target="#bankAccountModal">
                      <span class="material-symbols-outlined text-sm mr-2">edit</span>
                      Edit Bank Account
                    </button>
                  </div>

                  <% if @bank_account != nil %>
                    <div class="space-y-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Bank name</label>
                        <input type="text"
                               disabled
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                               value="<%= @bank_account.bank_name %>"/>
                      </div>

                      <div class="grid grid-cols-2 gap-4">
                        <div>
                          <label class="block text-sm font-medium text-gray-700 mb-2">Sort code</label>
                          <input type="text"
                                 disabled
                                 class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                                 value="<%= @bank_account.routing_number %>"/>
                        </div>
                        <div>
                          <label class="block text-sm font-medium text-gray-700 mb-2">Account number</label>
                          <input type="text"
                                 disabled
                                 class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                                 value="****<%= @bank_account.last4 %>"/>
                        </div>
                      </div>

                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Instantly available</label>
                        <input type="text"
                               disabled
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500"
                               value="<%= number_to_currency(
                                             @balance.instant_available.filter { |b| b.currency == "gbp" }.map { |b| b.net_available[0].amount }.sum / 100.0,
                                             unit: "£"
                                           ) %>"/>
                      </div>
                    </div>
                  <% else %>
                    <div class="text-center py-8">
                      <span class="material-symbols-outlined text-gray-400 text-4xl mb-4 block">account_balance</span>
                      <p class="text-gray-600">No bank account connected</p>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
          <!-- END STRIPE TAB -->

          <!-- TRANSACTIONS TAB -->
          <div class="payment-subtab-pane <%= (params[:transactions_starting_after].present? && params[:transactions_starting_before].present?) ? 'active' : 'hidden' %>"
               id="transactionsSection">
            <div class="text-center py-12">
              <span class="material-symbols-outlined text-gray-400 text-6xl mb-4 block">receipt_long</span>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Transactions</h3>
              <p class="text-gray-600">Transaction management coming soon</p>
            </div>
          </div>

          <!-- PAYOUTS TAB -->
          <div class="payment-subtab-pane hidden" id="payoutsSection">
            <% if @bank_account != nil %>
              <div class="bg-gray-50 rounded-lg p-6 mb-6">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <label class="text-sm font-medium text-gray-700">Instantly available</label>
                    <input type="text"
                           disabled
                           class="px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 font-medium"
                           value="<%= number_to_currency(
                                     @balance.instant_available.filter { |b| b.currency == "gbp" }.map { |b| b.net_available[0].amount }.sum / 100.0,
                                     unit: "£"
                                   ) %>"/>
                  </div>
                  <button type="button"
                          class="inline-flex items-center px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full font-medium transition-colors"
                          data-toggle="modal"
                          data-target="#requestPayoutModal">
                    <span class="material-symbols-outlined text-sm mr-2">send</span>
                    Request Payout
                  </button>
                </div>
              </div>

              <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                  <h3 class="text-lg font-semibold text-gray-900">Recent Payouts</h3>
                </div>
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Arrival date</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <% @payouts.each do |payout| %>
                      <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 text-sm font-medium text-gray-900">
                          <%= number_to_currency(payout.amount / 100.0, unit: "£") %>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-600">
                          <%= Time.at(payout.arrival_date).strftime("%d/%m/%y %l:%M%p") %>
                        </td>
                        <td class="px-6 py-4">
                          <% payout_status = payout.status.split("_").join(" ").capitalize %>
                          <% if payout_status == "Paid" %>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                              <span class="material-symbols-outlined text-sm mr-1">check_circle</span>
                              Paid
                            </span>
                          <% else %>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                              <%= payout_status %>
                            </span>
                          <% end %>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% else %>
              <div class="text-center py-12">
                <span class="material-symbols-outlined text-gray-400 text-6xl mb-4 block">account_balance</span>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Bank Account</h3>
                <p class="text-gray-600">Connect a bank account to view payouts</p>
              </div>
            <% end %>
          </div>

          <!-- MANUAL PAYMENT PROCESSING TAB -->
          <div class="payment-subtab-pane hidden" id="manualSection">
            <div class="text-center py-12">
              <span class="material-symbols-outlined text-gray-400 text-6xl mb-4 block">credit_card</span>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Manual Payment Processing</h3>
              <p class="text-gray-600">Manual payment processing setup coming soon</p>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-between items-center">
        <%= link_to "Cancel", admin_general_settings_practices_path, class: "px-4 py-2 text-gray-700 bg-white hover:bg-gray-100 rounded-full font-medium transition-colors border border-gray-300" %>
        <%= form.submit 'Save Changes', class: 'px-6 py-2 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full font-medium transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2' %>
      </div>
    <% end %>
  </div>
</div>

<!-- Connect Terminal Modal -->
<div id="connectTerminalModal" class="modal-overlay hidden fixed inset-0 z-50 flex items-center justify-center" style="background-color:rgba(0, 0, 0, 0.5)">
  <div class="modal-container bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
    <div class="modal-header flex items-center justify-between p-6 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">Connect Terminal</h3>
      <button type="button" class="modal-close text-gray-400 hover:text-gray-600 text-xl font-bold">&times;</button>
    </div>
    <div class="modal-body p-6">
      <p class="text-gray-600 mb-4">Connect a new Stripe terminal to your practice for in-person payments.</p>
      <form id="connectTerminalForm" class="space-y-4" data-practice-id="<%= @practice.id %>">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Terminal Registration Code *</label>
          <input type="text"
                 name="registration_code"
                 data-field="registration_code"
                 class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                 placeholder="Enter registration code"
                 required>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Terminal Name *</label>
          <input type="text"
                 name="name"
                 data-field="name"
                 class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                 placeholder="Enter terminal name"
                 required>
        </div>

        <div class="border-t pt-4 mt-4">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Terminal Location Address</h4>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Address Line 1 *</label>
              <input type="text"
                     name="line1"
                     data-field="line1"
                     class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                     placeholder="Street address"
                     required>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Address Line 2</label>
              <input type="text"
                     name="line2"
                     data-field="line2"
                     class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                     placeholder="Apartment, suite, etc. (optional)">
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">City *</label>
                <input type="text"
                       name="city"
                       data-field="city"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="City"
                       required>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Postcode *</label>
                <input type="text"
                       name="postcode"
                       data-field="postcode"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Postcode"
                       required>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer flex justify-end space-x-3 p-6 border-t border-gray-200">
      <button type="button" class="modal-close px-4 py-2 text-gray-700 bg-white hover:bg-gray-100 rounded-full font-medium transition-colors border border-gray-300">Cancel</button>
      <button type="button"
              data-action="connect-terminal"
              class="px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full font-medium transition-colors">
        <span data-text="default">Connect Terminal</span>
        <span data-text="loading" class="hidden">
          <span class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-800 mr-2"></span>
          Connecting...
        </span>
      </button>
    </div>
  </div>
</div>

<!-- Bank Account Modal -->
<div id="bankAccountModal" class="modal-overlay hidden fixed inset-0 z-50 flex items-center justify-center" style="background-color:rgba(0, 0, 0, 0.5)">
  <div class="modal-container bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
    <div class="modal-header flex items-center justify-between p-6 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">Edit Bank Account</h3>
      <button type="button" class="modal-close text-gray-400 hover:text-gray-600 text-xl font-bold">&times;</button>
    </div>
    <div class="modal-body p-6">
      <p class="text-gray-600 mb-4">Update your bank account information for payouts.</p>
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Bank Name</label>
          <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter bank name">
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Sort Code</label>
            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="00-00-00">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Account Number</label>
            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="********">
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer flex justify-end space-x-3 p-6 border-t border-gray-200">
      <button type="button" class="modal-close px-4 py-2 text-gray-700 bg-white hover:bg-gray-100 rounded-full font-medium transition-colors border border-gray-300">Cancel</button>
      <button type="button" class="px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full font-medium transition-colors">Update Account</button>
    </div>
  </div>
</div>

<!-- Request Payout Modal -->
<div id="requestPayoutModal" class="modal-overlay hidden fixed inset-0 z-50 flex items-center justify-center" style="background-color:rgba(0, 0, 0, 0.5)">
  <div class="modal-container bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
    <div class="modal-header flex items-center justify-between p-6 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900">Request Payout</h3>
      <button type="button" class="modal-close text-gray-400 hover:text-gray-600 text-xl font-bold">&times;</button>
    </div>
    <div class="modal-body p-6">
      <p class="text-gray-600 mb-4">Request an instant payout to your connected bank account.</p>
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Amount</label>
          <div class="relative">
            <span class="absolute left-3 top-2 text-gray-500">£</span>
            <input type="number" class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00" step="0.01">
          </div>
        </div>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="flex">
            <span class="material-symbols-outlined text-yellow-600 mr-2">info</span>
            <div class="text-sm text-yellow-800">
              <p class="font-medium">Instant Payout Fees</p>
              <p>Standard payouts are free. Instant payouts incur a 1.5% fee with a minimum of £0.25.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer flex justify-end space-x-3 p-6 border-t border-gray-200">
      <button type="button" class="modal-close px-4 py-2 text-gray-700 bg-white hover:bg-gray-100 rounded-full font-medium transition-colors border border-gray-300">Cancel</button>
      <button type="button" class="px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-full font-medium transition-colors">Request Payout</button>
    </div>
  </div>
</div>
