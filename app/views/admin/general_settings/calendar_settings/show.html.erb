<main class="flex-1 flex flex-col transition-all duration-300 ease-in-out ml-72 p-8 main-content-area min-h-[calc(100vh-56px)] bg-gray-50 calendar-settings">
  <%= render 'admin/general_settings/side_panel' %>

  <div class="flex-grow">
    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-semibold text-slate-800">Calendar Settings</h1>
      </div>

      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <%= form_with model: @setting, url: admin_general_settings_calendar_setting_path(practice_id: @selected_practice&.id), method: :patch, local: true, class: "flex items-center gap-4" do |f| %>
            <div class="flex items-center gap-4">
              <%= f.label :late_cancellation_window_hours, "Late Cancellation Window (hours)", class: "text-sm font-medium text-gray-700 mb-0" %>
              <%= f.number_field :late_cancellation_window_hours,
                                min: 1,
                                step: 1,
                                class: "w-24 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" %>
              <%= f.submit "Save", class: "cursor-pointer px-6 inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-9 rounded-full bg-orange-200 hover:bg-orange-300 text-orange-700 text-sm font-medium shadow-sm" %>
            </div>
          <% end %>

          <div class="flex items-center gap-4">
            <%= hidden_field_tag :practice_id, @selected_practice&.id, id: 'calendar-reserved-block-type-practice-id' %>

            <div class="relative w-64 items-center flex">
              <i class="far fa-search absolute left-3 pointer-events-none" style="color: gray;"></i>
              <input id="calendar-reserved-block-types-search-field" class="bg-white flex h-10 bg-background px-8 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 w-full pl-10 pr-4 py-2 rounded-full border border-gray-200 focus:ring-2 focus:ring-gray-100 focus:border-gray-300 shadow-sm" placeholder="Search types..." type="search">
            </div>

            <div class="relative">
              <button id="practice-selector-button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border hover:text-accent-foreground h-10 w-64 justify-between border-gray-200 bg-white text-left font-normal rounded-full px-4 py-2.5 shadow-sm hover:bg-gray-50 hover:border-gray-300 transition-all">
                <span class="truncate"><%= @selected_practice.name.truncate(24) %></span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 text-gray-400">
                  <path d="m6 9 6 6 6-6"></path>
                </svg>
              </button>

              <div id="practice-selector-dropdown" class="absolute top-full left-0 mt-2 z-[9999] w-64 rounded-xl overflow-hidden hidden bg-white border border-gray-200 shadow-lg">
                <div class="px-4 py-3 border-b border-gray-100">
                  <h3 class="text-gray-900 text-sm font-medium">Select Practice</h3>
                </div>
                <div class="px-4 py-3">
                  <div class="relative">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400">
                      <circle cx="11" cy="11" r="8"></circle>
                      <path d="m21 21-4.3-4.3"></path>
                    </svg>
                    <input id="practice-search-input" placeholder="Search practices..." class="w-full bg-gray-50 border border-gray-200 rounded-lg py-2 pl-9 pr-3 text-sm text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200" type="text" value="">
                  </div>
                </div>
                <div class="max-h-[300px] overflow-y-auto overflow-x-hidden">
                  <div class="overflow-hidden p-1">
                    <% @available_practices.each do |practice| %>
                      <%= link_to admin_general_settings_calendar_setting_path(selected_practice_id: practice.id),
                          class: "relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-gray-100 transition-colors w-full text-left #{'bg-blue-50 text-blue-700' if practice.id == @selected_practice.id}" do %>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check mr-2 h-4 w-4 <%= practice.id == @selected_practice.id ? 'opacity-100' : 'opacity-0' %>">
                          <path d="M20 6 9 17l-5-5"></path>
                        </svg>
                        <%= practice.name %>
                      <% end %>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex items-center gap-1 bg-white rounded-full shadow-sm border border-gray-200 px-1 py-1.5">
              <%= link_to admin_general_settings_calendar_setting_path(date: calculate_date('-', 1.week), selected_practice_id: @selected_practice.id), id: "prev-week", class: "inline-flex items-center justify-center gap-2 whitespace-nowrap !text-sm !font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground h-7 !px-2 !rounded-full hover:!bg-[#f8f0fc]" do %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left !h-4 !w-4 text-[#9b7aa8]">
                  <path d="m15 18-6-6 6-6"></path>
                </svg>
              <% end %>
              <div class="text-sm whitespace-nowrap px-3 py-1">
                <label id="current-week" for='week-picker' data-selected-date="<%= @selected_date.to_s %>">
                  Week of <%= week_range_string %>
                </label>
                <input type="date" id="week-picker" style="display: none;"/>
              </div>
              <%= link_to admin_general_settings_calendar_setting_path(date: calculate_date('+', 1.week), selected_practice_id: @selected_practice.id), id: "next-week", class: "inline-flex items-center justify-center gap-2 whitespace-nowrap !text-sm !font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground h-7 !px-2 !rounded-full hover:!bg-[#f8f0fc]" do %>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right !h-4 !w-4 text-[#9b7aa8]">
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              <% end %>
            </div>

            <span class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 bg-blue-100 hover:bg-blue-200 text-blue-700 border border-blue-200 shadow-sm font-medium px-4 py-2 rounded-full transition-all duration-200 hover:shadow-md cursor-pointer"
                  id="calendar-reserved-block-type-modal-toggle">
              <span class="material-symbols-outlined">add_circle</span>
              Create New Type
            </span>
          </div>
        </div>

        <%= render 'calendar_reserved_block_types_table', calendar_reserved_block_types: @calendar_reserved_block_types %>
      </div>
    </div>
  </div>
</main>

<%= render 'admin/general_settings/calendar_settings/type_modal' %>

<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/flatpickr/4.6.6/plugins/weekSelect/weekSelect.min.js" integrity="sha512-BeAjlUELLWVcE3WJGHeoiIrj8Mi2ogpLBhTAFcjOd0WicYgiVJCseMmodFRoj0WFYqsg7OelpgLpj2afxquFkQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
