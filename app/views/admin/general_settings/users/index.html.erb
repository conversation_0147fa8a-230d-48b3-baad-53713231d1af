<div class="container-fluid py-4 general-settings">
  <%= render 'admin/general_settings/side_panel' %>

  <div id="general-settings-main" class="ml-72">
    <main class="flex-1 flex flex-col transition-all duration-300 ease-in-out p-8">
      <div class="flex-grow" __v0_r="0,7033,7044">
        <div class="space-y-6" __v0_r="0,1974,1985">
          <div class="flex items-center justify-between" __v0_r="0,2008,2043">
            <h1 class="text-2xl font-semibold text-slate-800" __v0_r="0,2067,2106">Users</h1>
            <a href="<%= new_admin_general_settings_user_path %>"
              class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-600 border border-blue-200"
              __v0_r="0,2170,2216"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="lucide lucide-circle-plus mr-2 h-4 w-4"
                __v0_r="0,2250,2264"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M8 12h8"></path>
                <path d="M12 8v8"></path>
              </svg>
              Create New User
            </a>
          </div>
          <div dir="ltr" data-orientation="horizontal">
            <div role="tablist" aria-orientation="horizontal" class="inline-flex h-10 items-center justify-center rounded-md bg-[#f4f4f5] p-1 text-muted-foreground" tabindex="0" data-orientation="horizontal" style="outline: none;">
              <%= link_to "Active",
                          admin_general_settings_users_path(query: { archived_eq: false }),
                          class: "#{'bg-white' if params.dig(:query, :archived_eq).nil? || params.dig(:query, :archived_eq) == 'false'} inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all hover:text-gray-800",
                          role: "tab",
                          aria: { controls: "treatment-details", selected: true } %>
              <%= link_to "Archived",
                          admin_general_settings_users_path(query: { archived_eq: true }),
                          class: "#{'bg-white' if params.dig(:query, :archived_eq) == 'true'} inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all ",
                          role: "tab",
                          aria: { controls: "treatment-details", selected: true } %>
            </div>
          </div>
          <div class="md:grid grid-cols-6 gap-4 px-4 text-sm font-semibold text-slate-500" __v0_r="1,2642,2724">
            <div>First Name</div>
            <div>Last Name</div>
            <div>Roles</div>
            <div>Email</div>
            <div>Contact Number</div>
            <div class="text-center" __v0_r="0,2895,2908">Clinician</div>
          </div>
          <div class="space-y-2.5 user-list-items" __v0_r="0,2979,2992">
            <% @users.each do |user| %>
              <%= render partial: 'admin/general_settings/users/user_list_item', locals: {
                first_name: user.first_name,
                last_name: user.last_name,
                email: user.email,
                phone_number: user.mobile_phone,
                user_id: user.id,
                roles: user.roles.pluck(:name).join(', '),
                edit_link_url: edit_admin_general_settings_user_path(user),
                archive_link_url: archive_admin_general_settings_user_path(user),
                unarchive_link_url: unarchive_admin_general_settings_user_path(user),
                delete_link_url: admin_general_settings_user_path(user),
                user_archived: user.archived?
              } %>
            <% end %>
          </div>
        </div>
      </div>
      <div class="flex justify-center pt-4">
        <%= will_paginate @users %>
      </div>
    </main>
  </div>
</div>
