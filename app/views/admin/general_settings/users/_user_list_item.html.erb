<div class="user-list-item user-list-item-<%= user_id %> group grid relative grid-cols-6 items-center gap-4 bg-white shadow-sm rounded-xl p-4 text-sm transition-shadow hover:shadow-md" __v0_r="1,3141,3266">
  <div class="font-medium text-slate-800" __v0_r="0,3310,3338">
    <%= first_name %>
  </div>
  <div class="text-slate-800" __v0_r="0,3391,3407"><%= last_name %></div>
  <div class="text-slate-600" __v0_r="0,3459,3475"><%= roles.present? ? roles : "No roles assigned" %></div>
  <div class="text-slate-600 truncate" __v0_r="0,3527,3552"><%= email %></div>
  <div class="text-slate-600" __v0_r="0,3601,3617"><%= phone_number %></div>
  <% if true %>
    <div class="flex justify-center" __v0_r="0,3674,3695">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="lucide lucide-circle-check h-5 w-5 text-green-500"
        __v0_r="0,3777,3801"
      >
        <circle cx="12" cy="12" r="10"></circle>
        <path d="m9 12 2 2 4-4"></path>
      </svg>
    </div>
  <% else %>
    <div class="flex justify-center" __v0_r="0,3674,3695">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="lucide lucide-circle-x h-5 w-5 text-slate-400"
        __v0_r="0,3864,3888"
      >
        <circle cx="12" cy="12" r="10"></circle>
        <path d="m15 9-6 6"></path>
        <path d="m9 9 6 6"></path>
      </svg>
    </div>
  <% end %>
  <div class="absolute right-2 opacity-1 group-hover:opacity-100 transition-opacity" __v0_r="0,3961,4032">
    <button
      class="dropdown-button inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-10 w-10"
      type="button"
      id="radix-«r6»"
      aria-haspopup="menu"
      aria-expanded="false"
      data-state="closed"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="lucide lucide-ellipsis h-4 w-4"
        __v0_r="0,4218,4227"
      >
        <circle cx="12" cy="12" r="1"></circle>
        <circle cx="19" cy="12" r="1"></circle>
        <circle cx="5" cy="12" r="1"></circle>
      </svg>
    </button>
  </div>
  <div class="dropdown-container invisible absolute top-11 right-1 z-50 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-white p-1 text-popover-foreground shadow-md">
    <%= link_to "Edit", edit_link_url, class: "relative cursor-pointer flex items-center gap-2 rounded-sm hover:bg-gray-200 px-2 py-1.5 outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:opacity-50 [&_svg]:size-4 [&_svg]:shrink-0 text-[14px]" %>
    <% if user_archived %>
      <%= link_to "Unarchive", unarchive_link_url, method: :patch, data: { confirm: "Unarchive this user?" }, class: "relative cursor-pointer flex items-center gap-2 rounded-sm hover:bg-gray-200 px-2 py-1.5 outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:opacity-50 [&_svg]:size-4 [&_svg]:shrink-0 text-[14px]" %>
    <% else %>
      <%= link_to "Archive", archive_link_url, method: :patch, data: { confirm: "Archive this user?" }, class: "relative cursor-pointer flex items-center gap-2 rounded-sm hover:bg-gray-200 px-2 py-1.5 outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:opacity-50 [&_svg]:size-4 [&_svg]:shrink-0 text-[14px]" %>
    <% end %>
    <%= link_to "Delete",
                delete_link_url,
                method: :delete,
                data: { confirm: "Are you sure you want to delete this?" },
                class: "relative cursor-pointer flex items-center gap-2 rounded-sm hover:bg-gray-200 px-2 py-1.5 outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:opacity-50 [&_svg]:size-4 [&_svg]:shrink-0 text-[14px] text-red-500" %>
  </div>
</div>
