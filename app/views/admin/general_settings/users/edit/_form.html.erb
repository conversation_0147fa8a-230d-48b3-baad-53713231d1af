<%= form_with model: @user, url: admin_general_settings_user_path(@user), method: :patch, id: "user-form", html: { class: "py-8" } do |f| %>
  <script>
    window.userHolidayAllowance = <%= @user.holiday_allowance || 0 %>;
  </script>
  <div class="p-6 md:p-8 bg-white rounded-lg shadow-sm border border-slate-200">
    <%= render partial: 'admin/general_settings/users/edit/basic', locals: { f: f } %>
    <%= render partial: 'admin/general_settings/users/edit/role', locals: { f: f } %>
    <%= render partial: 'admin/general_settings/users/edit/contact', locals: { f: f } %>

    <!-- Filter Buttons for Accordion Sections -->
    <div class="pt-8">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-slate-800">Additional Settings</h3>
        <div class="bg-gray-100 rounded-full p-2 shadow-sm">
          <div class="filter-container flex gap-2">
          <%= render 'shared/apple_filter_button',
              text: 'Holiday',
              icon: 'fas fa-calendar-days',
              color: 'blue',
              active: true,
              options: {
                id: 'holiday-filter-btn',
                data: {
                  section: 'holiday',
                  group: 'user-settings'
                }
              } %>

          <%= render 'shared/apple_filter_button',
              text: 'Breaks',
              icon: 'fas fa-coffee',
              color: 'green',
              active: false,
              options: {
                id: 'breaks-filter-btn',
                data: {
                  section: 'breaks',
                  group: 'user-settings'
                }
              } %>

          <%= render 'shared/apple_filter_button',
              text: 'Diagnocat',
              icon: 'fas fa-tooth',
              color: 'purple',
              active: false,
              options: {
                id: 'diagnocat-filter-btn',
                data: {
                  section: 'diagnocat',
                  group: 'user-settings'
                }
              } %>

          <%= render 'shared/apple_filter_button',
              text: 'Login Restrictions',
              icon: 'fas fa-lock',
              color: 'orange',
              active: false,
              options: {
                id: 'login-filter-btn',
                data: {
                  section: 'login',
                  group: 'user-settings'
                }
              } %>

          <%= render 'shared/apple_filter_button',
              text: 'IP Restrictions',
              icon: 'fas fa-shield-halved',
              color: 'red',
              active: false,
              options: {
                id: 'ip-filter-btn',
                data: {
                  section: 'ip',
                  group: 'user-settings'
                }
              } %>

          <%= render 'shared/apple_filter_button',
              text: 'Associate Pay',
              icon: 'fas fa-money-bill',
              color: 'teal',
              active: false,
              options: {
                id: 'pay-filter-btn',
                data: {
                  section: 'pay',
                  group: 'user-settings'
                }
              } %>
          </div>
        </div>
      </div>

      <!-- Dynamic Content Area -->
      <div id="dynamic-content" class="hidden">
        <!-- Content will be loaded here when filter buttons are clicked -->
      </div>
    </div>

    <div id="accordion" class="pt-8">
      <%= render 'admin/general_settings/users/edit/accordion/section_item', title: "Holiday" do %>
        <%= render partial: 'admin/general_settings/users/edit/accordion/holiday', locals: { f: f } %>
      <% end %>

      <%= render 'admin/general_settings/users/edit/accordion/section_item', title: "Breaks" do %>
        <%= render partial: 'admin/general_settings/users/edit/accordion/breaks', locals: { f: f } %>
      <% end %>

      <%= render 'admin/general_settings/users/edit/accordion/section_item', title: "Diagnocat" do %>
        <%= render partial: 'admin/general_settings/users/edit/accordion/diagnocat', locals: { f: f } %>
      <% end %>

      <%= render 'admin/general_settings/users/edit/accordion/section_item', title: "Login Restrictions" do %>
        <%= render partial: 'admin/general_settings/users/edit/accordion/login', locals: { f: f } %>
      <% end %>

      <%= render 'admin/general_settings/users/edit/accordion/section_item', title: "IP Restrictions" do %>
        <%= render partial: 'admin/general_settings/users/edit/accordion/ip', locals: { f: f } %>
      <% end %>

      <%= render 'admin/general_settings/users/edit/accordion/section_item', title: "Associate Pay" do %>
        <%= render partial: 'admin/general_settings/users/edit/accordion/pay', locals: { f: f } %>
      <% end %>
    </div>

    <!-- SUBMIT BUTTON -->
    <div class="flex justify-end gap-2 mt-8 border-t border-slate-200 pt-6">
      <%= link_to admin_general_settings_users_path, class: "ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border-input bg-background hover:bg-accent hover:text-accent-foreground border border-gray-200 h-10 px-4 py-2" do %>
        Cancel
      <% end %>
      <%= f.submit "Save Changes", class: "ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 px-4 py-2 bg-blue-200 hover:bg-blue-300 text-blue-800" %>
    </div>
  </div>
<% end %>
