<!-- ROLE SELECTION -->
<h2 class="text-lg font-semibold text-slate-800 mb-4">Roles and practices</h2>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
  <div>
    <%= f.label :role_ids, "Roles", class: "block text-sm font-medium text-slate-700 mb-1" %>
    <%= f.select :role_ids, options_for_select(
                          Role.all.map { |role| [role.name, role.id] },
                          @user.role_ids
                        ),
      {},
      {
        multiple: true,
        class: "select2-multiple-roles flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        data: { placeholder: "Select roles..." }
      }
      %>
  </div>
  <div>
    <div class="block mb-1 font-semibold text-slate-700">Practices</div>

    <!-- Practice Selector Button -->
    <button type="button" id="practice-selector-btn" class="w-full flex items-center gap-2 rounded-xl bg-slate-50 p-4 text-sm border border-slate-200 hover:bg-slate-100 transition-colors">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-slate-500">
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.3-4.3"></path>
      </svg>
      <span class="text-slate-600">Select practices...</span>
    </button>

    <!-- Selected Practices Display -->
    <div id="selected-practices-display" class="mt-3 <%= @user.practice_ids.any? ? '' : 'hidden' %>">
      <div class="flex flex-wrap gap-2">
        <% @user.practices.each do |practice| %>
          <div class="flex items-center gap-2 p-1.5 pr-2 bg-blue-50 border border-blue-200 rounded-md text-sm" data-practice-id="<%= practice.id %>">
            <span class="relative flex shrink-0 overflow-hidden rounded-full h-5 w-5">
              <% if practice.logo.attached? %>
                <%= image_tag practice.logo.url, class: "aspect-square h-full w-full object-cover", alt: practice.name %>
              <% else %>
                <div class="bg-blue-100 h-full w-full flex items-center justify-center text-blue-600 font-medium text-xs">
                  <%= practice.name.first.upcase %>
                </div>
              <% end %>
            </span>
            <span class="font-medium text-blue-900"><%= practice.name %></span>
            <input type="hidden" name="user[practice_ids][]" value="<%= practice.id %>" />
          </div>
        <% end %>
      </div>
    </div>

    <!-- Practice Selection Modal -->
    <div id="user-practice-modal" class="fixed inset-0 flex items-center justify-center hidden transition-opacity duration-300 ease-in-out" style="z-index: 3000;">
      <!-- Backdrop -->
      <div class="absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300 ease-in-out"></div>

      <!-- Modal content -->
      <div class="bg-white rounded-xl shadow-lg w-full max-w-lg mx-auto overflow-hidden relative transform transition-all duration-300 ease-in-out">
        <div class="flex h-full w-full flex-col overflow-hidden rounded-md bg-white text-gray-900">
          <!-- Search Input -->
          <div class="flex items-center border-b border-gray-200 px-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4 shrink-0 opacity-50">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.3-4.3"></path>
            </svg>
            <input
              class="flex h-11 w-full rounded-md bg-transparent py-3 outline-none placeholder:text-gray-500 disabled:cursor-not-allowed disabled:opacity-50 text-sm"
              placeholder="Search for practices..."
              id="user-practices-search-input"
              type="text"
              autocomplete="off"
              autocorrect="off"
              spellcheck="false"
            >
            <button type="button" class="text-gray-400 hover:text-gray-500 transition-colors duration-200 ml-2" id="close-user-practice-modal">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Available Practices List -->
          <div class="overflow-y-auto overflow-x-hidden max-h-[calc(100vh-350px)] sm:max-h-[300px]">
            <div class="overflow-hidden text-gray-900">
              <div class="px-3 pt-5 pb-2 text-[11px] font-semibold text-gray-500 tracking-wider uppercase">Available Practices</div>
              <div id="user-practices-list" class="px-3">
                <% @practices.each do |practice| %>
                  <div class="relative cursor-pointer select-none rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-gray-50 transition-colors duration-150 flex items-center justify-between gap-3"
                       data-practice-id="<%= practice.id %>"
                       data-practice-name="<%= practice.name %>"
                       data-practice-search="<%= practice.name.downcase %>"
                       data-selected="<%= @user.practices.include?(practice) ? 'true' : 'false' %>"
                       title="Click to select <%= practice.name %>">
                    <div class="flex items-center gap-3 flex-grow">
                      <input
                        type="checkbox"
                        class="h-4 w-4 shrink-0 rounded-sm border border-gray-300 focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 user-practice-checkbox"
                        id="user-practice-checkbox-<%= practice.id %>"
                        value="<%= practice.id %>"
                        <%= @user.practices.include?(practice) ? 'checked' : '' %>
                        aria-label="Select <%= practice.name %>"
                      >
                      <span class="relative flex shrink-0 overflow-hidden rounded-full h-8 w-8">
                        <% if practice.logo.attached? %>
                          <%= image_tag practice.logo.url, class: "aspect-square h-full w-full object-cover", alt: practice.name %>
                        <% else %>
                          <div class="bg-blue-100 h-full w-full flex items-center justify-center text-blue-600 font-medium text-sm">
                            <%= practice.name.first.upcase %>
                          </div>
                        <% end %>
                      </span>
                      <span class="font-medium text-gray-900"><%= practice.name %></span>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Footer with Save Button -->
          <div class="border-t border-gray-200 px-4 py-3 bg-gray-50">
            <button type="button" id="save-user-practices" class="w-full inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors bg-blue-100 text-blue-600 border border-blue-200 hover:bg-blue-200">
              Confirm Selection
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
