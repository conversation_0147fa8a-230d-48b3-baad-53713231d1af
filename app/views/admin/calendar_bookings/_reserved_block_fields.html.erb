<div class="space-y-6">
  <div class="space-y-3">
    <p class="text-[13px] font-medium text-gray-700">Reserved Block Type</p>
    <div class="relative">
      <%= f.select :calendar_reserved_block_type_id,
            options_from_collection_for_select(@calendar_reserved_block_types, :id, :treatment_name),
            { prompt: 'Choose Reserved Block Type' },
            {
              required: true,
              id: 'calendar-form-reserved-block-type-select',
              class: 'w-full h-11 px-3 py-2 text-sm text-gray-700 bg-white border border-gray-200 rounded-md'
            } %>
      <%= image_tag('icons/arrow-down.svg', class: 'absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 pointer-events-none') %>
    </div>
  </div>

  <div class="space-y-3 mb-3">
    <label class="flex items-center space-x-3 px-3 py-2.5 rounded-xl cursor-pointer transition-colors hover:bg-[#f5f5f7]">
      <%= f.check_box :repeat_event, class: "hidden peer", id: "reserved-block-repeat-checkbox" %>

      <div class="h-5 w-5 rounded-full border border-gray-300 flex items-center justify-center bg-white
                  peer-checked:border-[#FFD8B1] peer-checked:border-6">
        <div class="h-2 w-2 rounded-full bg-[#FFD8B1] hidden peer-checked:block"></div>
      </div>

      <span class="text-[13px] text-gray-700 peer-checked:text-black peer-checked:font-medium">Repeat Reserved Block</span>
    </label>
  </div>

  <div id="reserved-block-repeat-options" class="flex flex-col gap-6 ml-5 hidden">
    <div class="space-y-3">
      <%= f.label :repeat_days, "Repeat on", class: "text-[13px] font-medium text-gray-700" %>
      <div class="grid grid-cols-7 my-2" id="reserved-block-repeat-days-container">
        <% day_short = %w[S M T W T F S] %>
        <% Date::DAYNAMES.each_with_index do |day, index| %>
          <div class="relative">
            <%= f.check_box :repeat_days, {
                  multiple: true,
                  id: "reserved_block_repeat_days_#{index}",
                  value: index,
                  class: "hidden peer"
                }, index, nil %>

            <label for="reserved_block_repeat_days_<%= index %>"
                  class="flex items-center justify-center h-8 w-8 rounded-full cursor-pointer transition-all
                          peer-checked:bg-[#FFD8B1] peer-checked:text-black peer-checked:font-medium
                          bg-[#f5f5f7] text-gray-600 hover:bg-[#ebebed] text-[12px]">
              <span><%= day_short[index] %></span>
            </label>
          </div>
        <% end %>
      </div>
    </div>

    <div class="space-y-3">
      <p class="text-[13px] font-medium text-gray-700">Repeat Pattern</p>

      <div class="flex flex-col space-y-3">
        <label class="flex items-center space-x-3 px-3 py-2.5 rounded-xl cursor-pointer transition-colors hover:bg-[#f5f5f7]">
          <%= f.radio_button :repeat_pattern, 'weekly', class: "hidden peer", checked: true %>
          <div class="h-5 w-5 rounded-full border border-gray-300 flex items-center justify-center bg-white
                      peer-checked:border-[#FFD8B1] peer-checked:border-6">
            <div class="h-2 w-2 rounded-full bg-[#FFD8B1] hidden peer-checked:block"></div>
          </div>
          <span class="text-[13px] text-gray-700 peer-checked:text-black peer-checked:font-medium">Weekly</span>
        </label>

        <label class="flex items-center space-x-3 px-3 py-2.5 rounded-xl cursor-pointer transition-colors hover:bg-[#f5f5f7]">
          <%= f.radio_button :repeat_pattern, 'alternating', class: "hidden peer" %>
          <div class="h-5 w-5 rounded-full border border-gray-300 flex items-center justify-center bg-white
                      peer-checked:border-[#FFD8B1] peer-checked:border-6">
            <div class="h-2 w-2 rounded-full bg-[#FFD8B1] hidden peer-checked:block"></div>
          </div>
          <span class="text-[13px] text-gray-700 peer-checked:text-black peer-checked:font-medium">Alternating Weeks</span>
        </label>
      </div>
    </div>

    <div class="space-y-3">
      <p class="text-[13px] font-medium text-gray-700">End Repeat</p>

      <label class="flex items-center space-x-3 px-3 py-2.5 rounded-xl cursor-pointer transition-colors hover:bg-[#f5f5f7]">
        <%= f.radio_button :end_repeat_mode, 'on_date', class: "hidden peer", checked: true %>
        <div class="h-5 w-5 rounded-full border border-gray-300 flex items-center justify-center bg-white
                    peer-checked:border-[#FFD8B1] peer-checked:border-6">
          <div class="h-2 w-2 rounded-full bg-[#FFD8B1] hidden peer-checked:block"></div>
        </div>
        <span class="text-[13px] text-gray-700 peer-checked:text-black peer-checked:font-medium">On Date</span>
      </label>

      <div class="pl-8">
        <div class="relative">
          <%= f.date_field :end_repeat_date,
                class: "flex h-10 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 w-full py-2.5 px-3 rounded-xl border border-gray-200 focus:ring-0 focus:border-gray-300 text-[13px] bg-white",
                value: Date.current + 3.months,
                data: { end_repeat_target: 'dateInput' } %>

          <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                class="h-4 w-4 text-gray-400">
              <path d="M8 2v4M16 2v4M3 10h18M8 14h.01M12 14h.01M16 14h.01M8 18h.01M12 18h.01M16 18h.01" />
              <rect width="18" height="18" x="3" y="4" rx="2" />
            </svg>
          </div>
        </div>
      </div>

      <label class="flex items-center space-x-3 px-3 py-2.5 rounded-xl cursor-pointer transition-colors hover:bg-[#f5f5f7]">
        <%= f.radio_button :end_repeat_mode, 'indefinite', class: "hidden peer" %>
        <div class="h-5 w-5 rounded-full border border-gray-300 flex items-center justify-center bg-white
                    peer-checked:border-[#FFD8B1] peer-checked:border-6">
          <div class="h-2 w-2 rounded-full bg-[#FFD8B1] hidden peer-checked:block"></div>
        </div>
        <span class="text-[13px] text-gray-700 peer-checked:text-black peer-checked:font-medium">Repeat Indefinitely</span>
      </label>
    </div>
  </div>
</div>
