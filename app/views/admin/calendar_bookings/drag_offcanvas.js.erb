(function() {
  openOffcanvas('drag-offcanvas');
  document.getElementById('drag-offcanvas-container').innerHTML = "<%= j render partial: 'new_appointment' %>";

  document.getElementById('drag-offcanvas-close').addEventListener('click', function() {
    document.querySelectorAll('.dragging-booking').forEach(el => el.remove());
  });

  handleAddAppointmentButton();

  <% if @reserved_block.present? %>
    const reservedBlockTreatmentSelect = document.getElementById('calendar-form-treatment-select');
    if (reservedBlockTreatmentSelect) {
      const treatmentId = '<%= @reserved_block.calendar_reserved_block_type.treatment.id %>';
      const treatmentName = '<%= @reserved_block.calendar_reserved_block_type.treatment.patient_friendly_name %>';

      reservedBlockTreatmentSelect.innerHTML = '';
      const option = new Option(treatmentName, treatmentId, true, true);
      reservedBlockTreatmentSelect.appendChild(option);

      reservedBlockTreatmentSelect.disabled = true;

      $(reservedBlockTreatmentSelect).trigger('change');
    }
  <% end %>

  $('input[name="booking_mode"]').on('change', function() {
    if (isEventTabActive()) {
      $('#calendar-booking-type').val('event');
      $('#treatment-fields-container').addClass('hidden');
      $('#recurrence-fields-container').removeClass('hidden');
      $('#calendar-form-patient-select-container').addClass('hidden');
      $('#treatment-mode-tabs').addClass('hidden');
      $('#reserved-block-fields-container').addClass('hidden');
      $('#reserved-block-recurrence-fields-container').addClass('hidden');
      $('#new-appointments-notes-input').removeClass('hidden');
      $('#add-appointment-button').text('Add Event');
    } else if (isTreatmentTabActive()) {
      $('#calendar-booking-type').val('treatment');
      $('#treatment-fields-container').removeClass('hidden');
      $('#recurrence-fields-container').addClass('hidden');
      $('#calendar-form-patient-select-container').removeClass('hidden');
      $('#treatment-mode-tabs').removeClass('hidden');
      $('#reserved-block-fields-container').addClass('hidden');
      $('#reserved-block-recurrence-fields-container').addClass('hidden');
      $('#new-appointments-notes-input').removeClass('hidden');
      $('#add-appointment-button').text('Add Appointment');
    } else if (isReservedBlockTabActive()) {
      $('#calendar-booking-type').val('reserved_block');
      $('#treatment-fields-container').addClass('hidden');
      $('#recurrence-fields-container').addClass('hidden');
      $('#calendar-form-patient-select-container').addClass('hidden');
      $('#treatment-mode-tabs').addClass('hidden');
      $('#reserved-block-fields-container').removeClass('hidden');
      $('#reserved-block-recurrence-fields-container').removeClass('hidden');
      $('#new-appointments-notes-input').addClass('hidden');
      $('#add-appointment-button').text('Add Reserved Block');
    }
  });

  $('#calendar-booking-repeat-checkbox').on('change', function () {
    if ($(this).is(':checked')) {
      $('#calendar-booking-repeat-options').removeClass('hidden');
    } else {
      $('#calendar-booking-repeat-options').addClass('hidden');
    }
  });

  $('#reserved-block-repeat-checkbox').on('change', function () {
    if ($(this).is(':checked')) {
      $('#reserved-block-repeat-options').removeClass('hidden');
    } else {
      $('#reserved-block-repeat-options').addClass('hidden');
    }
  });

  function isEventTabActive() {
    return $('#event-mode').is(':checked');
  }

  function isTreatmentTabActive() {
    return $('#treatment-mode').is(':checked');
  }

  function isReservedBlockTabActive() {
    return $('#reserved-block-mode').is(':checked');
  }

  $('input[name="charting_tab"]').on('change', function () {
    if ($('#upcoming-charting-appointments-tab').is(':checked')) {
      $('#treatment-mode-upcoming-charting-appointments-section').removeClass('hidden');
      $('#treatment-mode-cancellation-list-section').addClass('hidden');

      fetchUpcomingChartingAppointments();
    } else if ($('#cancellation-list-tab').is(':checked')) {
      $('#treatment-mode-upcoming-charting-appointments-section').addClass('hidden');
      $('#treatment-mode-cancellation-list-section').removeClass('hidden');

      fetchCancellationList();
    }
  });

  const patientSelect = $('#calendar-form-patient-select');
  const treatmentPlanSelect = $('#calendar-form-treatment-plan-select');
  const treatmentSelect = $('#calendar-form-treatment-select');

  patientSelect.select2({
    theme: 'tailwindcss-3',
    allowClear: false,
    selectionCssClass: 'form-control',
    dropdownCssClass: 'form-control',
    placeholder: 'Search for a patient',
    minimumInputLength: 3,
    width: '100%',
    ajax: {
      url: '/admin/patients/select2_search',
      dataType: 'json',
      delay: 250,
      data: function (params) {
        return {
          q: params.term
        };
      },
      processResults: function (data, params) {
        return data;
      }
    }
  });

  treatmentSelect.select2({
    theme: 'tailwindcss-3',
    allowClear: false,
    selectionCssClass: 'form-control',
    dropdownCssClass: 'form-control',
    placeholder: 'Search for a treatment',
    minimumInputLength: 3,
    width: '100%',
  });

  const reservedBlockTypeSelect = $('#calendar-form-reserved-block-type-select');
  reservedBlockTypeSelect.select2({
    theme: 'tailwindcss-3',
    allowClear: false,
    selectionCssClass: 'form-control',
    dropdownCssClass: 'form-control',
    placeholder: 'Search for a reserved block type',
    width: '100%',
  });

  patientSelect.on('change', function() {
    fetchCancellationList();
    fetchUpcomingChartingAppointments();

    var patientId = $(this).val();

    if (!patientId) {
      treatmentPlanSelect.empty().append(new Option('Do Not Open Course Of Treatment', ''));
      treatmentPlanSelect.append(new Option('Create New Course Of Treatment', 'new'));

      return;
    }

    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    $.ajax({
      url: '/admin/calendar_bookings/course_of_treatments/for_select',
      method: 'GET',
      data: { patient_id: patientId },
      headers: { 'X-CSRF-Token': csrfToken },
      success: function(data) {
        treatmentPlanSelect.empty();
        treatmentPlanSelect.append(new Option('Create New Course Of Treatment', 'new'));
        treatmentPlanSelect.append(new Option('Do Not Open Course Of Treatment', ''));

        data.forEach(function(item) {
          treatmentPlanSelect.append(new Option(item.name, item.id));
        });

        treatmentPlanSelect.val('').trigger('change');
      },
      error: function() {
        alert('Failed to load treatment plans');
      }
    });
  });

  patientSelect.trigger('change');

  function handleAddAppointmentButton() {
    if (!document.getElementById('add-appointment-button')) return;

    document.getElementById('add-appointment-button').addEventListener('click', function(e) {
      e.preventDefault();

      const patientId = $('#calendar-form-patient-select').val();

      if (!patientId && isTreatmentTabActive()) {
        Swal.fire('Patient Not Selected', 'Please select a patient first to view their upcoming charting appointments and recalls.', 'info');
        return;
      }

      if (isReservedBlockTabActive()) {
        const reservedBlockTypeId = $('#calendar-form-reserved-block-type-select').val();
        if (!reservedBlockTypeId) {
          Swal.fire('Reserved Block Type Not Selected', 'Please select a reserved block type.', 'info');
          return;
        }
      }

      const practitionerId = $('#calendar-booking-practitioner-id').val();
      const startTime = document.querySelector('#drag-offcanvas input[name="calendar_booking[start_time]"]').value;
      const endTime = document.querySelector('#drag-offcanvas input[name="calendar_booking[end_time]"]').value;
      const date = document.querySelector('#drag-offcanvas input[name="calendar_booking[date]"]').value;
      const bookingType = document.getElementById('calendar-booking-type').value;
      const notes = document.querySelector('#drag-offcanvas textarea[name="calendar_booking[notes]"]').value;

      let appointmentDuration = null;
      const selectedCard = document.querySelector('#drag-offcanvas [data-booking-id].selected-active');
      if (selectedCard) {
        const cardDuration = parseInt(selectedCard.dataset.duration,10);
        if (cardDuration > 0) {
          appointmentDuration = cardDuration;
        }
      }

      let slotDuration = null;
      let durationInput = document.querySelector('#drag-offcanvas input[name="calendar_booking[duration_in_minutes]"]');
      if (!durationInput) {
        durationInput = document.querySelector('#drag-offcanvas select[name="calendar_booking[duration_in_munutes]"]');
      }
      if (durationInput) {
        slotDuration = parseInt(durationInput.value, 10);
      }

      const difference = (appointmentDuration !== null && slotDuration !== null) ? Math.abs(appointmentDuration - slotDuration) : null;

      function submitForm() {
        const mode = '<%= @mode %>';
        const hiddenDentists = JSON.stringify('<%= raw(params[:hidden_dentists].presence || [].to_json) %>');
        const form = document.createElement('form');

        if (isReservedBlockTabActive()) {
          form.action = '<%= create_reserved_block_admin_calendar_bookings_path %>';
        } else {
          form.action = '<%= admin_calendar_bookings_path %>';
        }

        form.method = 'POST';
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'authenticity_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        if (isReservedBlockTabActive()) {
          form.appendChild(createHiddenInput('calendar_reserved_block[start_time]', startTime));
          form.appendChild(createHiddenInput('calendar_reserved_block[end_time]', endTime));
          form.appendChild(createHiddenInput('calendar_reserved_block[date]', date));
          form.appendChild(createHiddenInput('calendar_reserved_block[practitioner_id]', practitionerId));
        } else {
          form.appendChild(createHiddenInput('calendar_booking[start_time]', startTime));
          form.appendChild(createHiddenInput('calendar_booking[end_time]', endTime));
          form.appendChild(createHiddenInput('calendar_booking[date]', date));
          form.appendChild(createHiddenInput('calendar_booking[practitioner_id]', practitionerId));
        }

        form.appendChild(createHiddenInput('mode', mode));
        form.appendChild(createHiddenInput('hidden_dentists', hiddenDentists));

        if (!isReservedBlockTabActive()) {
          form.appendChild(createHiddenInput('calendar_booking[notes]', notes));
        }

        if (isTreatmentTabActive()) {
          form.appendChild(createHiddenInput('calendar_booking[patient_id]', patientId));
          form.appendChild(createHiddenInput('calendar_booking[booking_type]', bookingType));
          const courseOfTreatmentId = document.querySelector('#drag-offcanvas select[name="calendar_booking[course_of_treatment_id]"]')?.value;
          const treatmentId = document.querySelector('#drag-offcanvas select[name="calendar_booking[treatment_id]"]').value;
          if (courseOfTreatmentId) {
            form.appendChild(createHiddenInput('calendar_booking[course_of_treatment_id]', courseOfTreatmentId));
          }
          form.appendChild(createHiddenInput('calendar_booking[treatment_id]', treatmentId));
          const chartingAppointmentId = document.querySelector('#drag-offcanvas input[name="calendar_booking[charting_appointment_id]"]')?.value || '';
          if (chartingAppointmentId) {
            form.appendChild(createHiddenInput('calendar_booking[charting_appointment_id]', chartingAppointmentId));
          }
        } else if (isEventTabActive()) {
          form.appendChild(createHiddenInput('calendar_booking[booking_type]', bookingType));

          const colorRadio = document.querySelector('input[name="calendar_booking[event_color]"]:checked');
          if (colorRadio) {
            form.appendChild(createHiddenInput('calendar_booking[event_color]', colorRadio.value));
          }

          // TODO: fix this
          if (document.getElementById('calendar-booking-repeat-checkbox').checked) {
            const dayCheckboxes = document.querySelectorAll('#calendar-booking-repeat-days-container input[type="checkbox"]:checked');
            if (dayCheckboxes.length === 0) {
              Swal.fire({
                icon: 'warning',
                title: 'No weekdays selected',
                text: 'Please select at least one weekday if "Repeat Event" is checked.'
              });

              return false;
            }

            form.appendChild(createHiddenInput('calendar_booking[repeat_event]', '1'));

            dayCheckboxes.forEach((checkbox) => {
              form.appendChild(createHiddenInput('calendar_booking[repeat_days][]', checkbox.value));
            });

            const repeatPatternRadio = document.querySelector('input[name="calendar_booking[repeat_pattern]"]:checked');
            if (repeatPatternRadio) {
              form.appendChild(createHiddenInput('calendar_booking[repeat_pattern]', repeatPatternRadio.value));
            }

            const endRepeatDateField = document.querySelector('input[name="calendar_booking[end_repeat_date]"]');
            if (endRepeatDateField) {
              form.appendChild(createHiddenInput('calendar_booking[end_repeat_date]', endRepeatDateField.value));
            }

            const repeatIndefinitelyCheckbox = document.querySelector('input[name="calendar_booking[repeat_indefinitely]"]');
            const repeatIndefinitelyValue = repeatIndefinitelyCheckbox && repeatIndefinitelyCheckbox.checked ? '1' : '0';
            form.appendChild(createHiddenInput('calendar_booking[repeat_indefinitely]', repeatIndefinitelyValue));
          }
        } else if (isReservedBlockTabActive()) {
          const reservedBlockTypeId = document.querySelector('#calendar-form-reserved-block-type-select').value;
          if (reservedBlockTypeId) {
            form.appendChild(createHiddenInput('calendar_reserved_block[calendar_reserved_block_type_id]', reservedBlockTypeId));
          }

          if (document.getElementById('reserved-block-repeat-checkbox').checked) {
            const dayCheckboxes = document.querySelectorAll('#reserved-block-repeat-days-container input[type="checkbox"]:checked');
            if (dayCheckboxes.length === 0) {
              Swal.fire({
                icon: 'warning',
                title: 'No weekdays selected',
                text: 'Please select at least one weekday if "Repeat Reserved Block" is checked.'
              });

              return false;
            }

            form.appendChild(createHiddenInput('calendar_reserved_block[repeat_event]', '1'));

            dayCheckboxes.forEach((checkbox) => {
              form.appendChild(createHiddenInput('calendar_reserved_block[repeat_days][]', checkbox.value));
            });

            const repeatPatternRadio = document.querySelector('input[name="calendar_booking[repeat_pattern]"]:checked');
            if (repeatPatternRadio) {
              form.appendChild(createHiddenInput('calendar_reserved_block[repeat_pattern]', repeatPatternRadio.value));
            }

            const endRepeatDateField = document.querySelector('input[name="calendar_booking[end_repeat_date]"]');
            if (endRepeatDateField) {
              form.appendChild(createHiddenInput('calendar_reserved_block[end_repeat_date]', endRepeatDateField.value));
            }

            const repeatIndefinitelyCheckbox = document.querySelector('input[name="calendar_booking[repeat_indefinitely]"]');
            const repeatIndefinitelyValue = repeatIndefinitelyCheckbox && repeatIndefinitelyCheckbox.checked ? '1' : '0';
            form.appendChild(createHiddenInput('calendar_reserved_block[repeat_indefinitely]', repeatIndefinitelyValue));
          }
        }

        document.body.appendChild(form);
        form.submit();

        function createHiddenInput(name, value) {
          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = name;
          input.value = value;
          return input;
        }
      }

      if (
        appointmentDuration !== null &&
        slotDuration !== null &&
        appointmentDuration !== slotDuration
      ) {
        Swal.fire({
          title: 'Appointment Duration Warning',
          html: `The appointment you are trying to book is <b>${appointmentDuration} minutes</b> long, but the selected time slot is <b>${slotDuration} minutes</b>.<br><br>Please either adjust the time slot by <b>${difference} minutes</b> or ask the clinician to amend the appointment duration accordingly.<br><br>Scheduling appointments in unsuitable time slots can negatively impact clinic efficiency, increase the risk of clinical errors, and place unnecessary stress on the clinical team. It is imperative that the clinician is allocated the appropriate time requested for the appointment to ensure safe and effective patient care.<br><br>Would you like to proceed with the booking despite the time mismatch?`,
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: 'Proceed',
          cancelButtonText: 'Cancel',
        }).then((result) => {
          if (result.isConfirmed) {
            submitForm();
          }
        });
        return;
      }

      submitForm();
    });
  }

  async function fetchCancellationList() {
    const patientId = $('#calendar-form-patient-select').val();
    const practitionerId = $('#calendar-booking-practitioner-id').val();

    const durationInput = document.querySelector('input[name="calendar_booking[duration_in_minutes]"]');
    const duration = durationInput.value;

    const mode = '<%= @mode %>';
    const dateParam = '<%= @date %>';
    const hiddenDentists = JSON.stringify('<%= raw(params[:hidden_dentists].presence || [].to_json) %>');

    await $.ajax({
      url: '/admin/calendar_bookings/cancellation_list',
      data: {
        patient_id: patientId,
        practitioner_id: practitionerId,
        duration: duration,
        mode: mode,
        date: dateParam,
        hidden_dentists: hiddenDentists
      },
      success: function(response) {
        document.getElementById('treatment-mode-cancellation-list-section').innerHTML = response;
        // TODO: offer slot to patient action + notification
      }
    });
  }

  async function fetchUpcomingChartingAppointments() {
    const patientId = $('#calendar-form-patient-select').val();
    const practitionerId = $('#calendar-booking-practitioner-id').val();

    const endTime = document.querySelector('input[name="calendar_booking[end_time]').value;
    const date = document.querySelector('input[name="calendar_booking[date]').value;

    await $.ajax({
      url: '/admin/calendar_bookings/charting_appointments/upcoming',
      data: { patient_id: patientId, end_time: endTime, date: date, practitioner_id: practitionerId },
      success: function(response) {
        document.getElementById('treatment-mode-upcoming-charting-appointments-section').innerHTML = response;
        handleChartingAppointments();
      }
    });
  }

  function handleChartingAppointments() {
    const ringColorClass = 'ring-[#3b82f6]';

    const selectionContainer = document.getElementById('new-appointment-selected-appointment-card');
    const resetWrapper       = document.getElementById('new-appointment-reset-selection-container');
    const resetBtn           = document.getElementById('new-appointment-reset-selection-button');

    let lastCard = null;

    const hiddenInput = (n,v)=>Object.assign(document.createElement('input'),{type:'hidden',name:n,value:v});

    resetBtn.addEventListener('click', () => {
      const form = document.getElementById('new-appointment-form');
      form.querySelectorAll('input[name="calendar_booking[charting_appointment_id]"]').forEach(i=>i.remove());

      document.querySelectorAll('[data-booking-id].selected-active').forEach(c=>c.classList.remove('ring-2',ringColorClass,'bg-[#bfdbfe]','selected-active'));

      lastCard?.blur?.(); lastCard = null;

      selectionContainer.innerHTML = '';
      selectionContainer.style.display = 'none';
      resetWrapper.classList.add('hidden');

      document.getElementById('treatment-fields-container').style.display = 'flex';
      document.getElementById('new-appointments-notes-input').style.display = 'block';
    });

    document.addEventListener('click', e => {
      if (!e.target.closest('#drag-offcanvas-container')) return;

      const card = e.target.closest('[data-booking-id]');
      if (!card) return;
      const confirm = card.dataset.previousBooked !== 'true';
      if (confirm) {
        Swal.fire({
          title:'Are you sure?',
          text:'The previous appointment in that treatment plan is not booked yet. Are you sure you want continue?',
          icon:'warning',
          showCancelButton:true,
          confirmButtonText:'Yes, continue',
          cancelButtonText:'No, cancel',
          reverseButtons:true,
          returnFocus: false
        }).then((result) => {
          if (result.isConfirmed) {
            selectCard(card);
          }
        });
      } else {
        selectCard(card);
      }
    });

    function selectCard(card){
      const duration     = parseInt(card.dataset.duration,10)||30;
      const bookableFrom = card.dataset.bookableFrom;
      const id           = card.dataset.bookingId;

      const form = document.getElementById('new-appointment-form');
      form.querySelectorAll('input[name="calendar_booking[charting_appointment_id]"]').forEach(el=>el.remove());
      form.appendChild(hiddenInput('calendar_booking[charting_appointment_id]',id));

      const sDate = document.querySelector('input[name="calendar_booking[start_date]"]');
      const eDate = document.querySelector('input[name="calendar_booking[end_date]"]');
      if (sDate && eDate){
        sDate.value = bookableFrom;
        const d = new Date(bookableFrom); d.setMonth(d.getMonth()+1);
        eDate.value = d.toISOString().split('T')[0];
        sDate.dispatchEvent(new Event('change',{bubbles:true}));
      }

      const durSel = document.querySelector('select[name="calendar_booking[duration_in_munutes]"]');
      if (durSel) durSel.value = duration;

      document.getElementById('treatment-fields-container').style.display='none';
      document.getElementById('new-appointments-notes-input').style.display='none';

      document.querySelectorAll('[data-booking-id].selected-active').forEach(c=>c.classList.remove('ring-2',ringColorClass,'bg-[#bfdbfe]','selected-active'));
      card.classList.add('ring-2',ringColorClass,'bg-[#bfdbfe]','selected-active');

      const clone = card.cloneNode(true);
      clone.style.pointerEvents='none';
      clone.removeAttribute('data-booking-id');
      clone.tabIndex=-1;
      clone.classList.add('ring-2',ringColorClass,'bg-[#bfdbfe]');
      selectionContainer.innerHTML='';
      selectionContainer.appendChild(clone);
      selectionContainer.style.display='block';
      resetWrapper.classList.remove('hidden');

      lastCard = card;

      const container = document.getElementById('drag-offcanvas-container');
      if (container) {
        container.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    }
  }
})();
