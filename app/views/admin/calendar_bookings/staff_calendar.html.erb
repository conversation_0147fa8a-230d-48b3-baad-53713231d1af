<div class="calendar-scss" style="background-color: #f5f5f6 !important;">
  <%= render 'admin/calendar_bookings/staff_calendar/header', from_cancellation_list: false %>
  <%= render 'admin/calendar_bookings/staff_calendar/sub_header' %>

  <% if @multisite_calendar == true %>
    <%= render 'admin/calendar_bookings/staff_calendar/multisite' %>
  <% else %>
    <% if @mode == :month %>
      <%= render 'admin/calendar_bookings/staff_calendar/month' %>
    <% elsif @mode == :week %>
      <%= render 'admin/calendar_bookings/staff_calendar/week' %>
    <% elsif @mode == :day %>
      <%= render 'admin/calendar_bookings/staff_calendar/day' %>
    <% end %>
  <% end %>
</div>

<%= render 'admin/calendar_bookings/offcanvas', id: 'reschedule-offcanvas', title: 'Reschedule Appointment' %>
<%= render 'admin/calendar_bookings/offcanvas', id: 'search-offcanvas', title: 'Search Results' %>
<%= render 'admin/calendar_bookings/offcanvas', id: 'drag-offcanvas', title: 'New Appointment' %>
<%= render 'admin/calendar_bookings/offcanvas', id: 'slot-finder-offcanvas', title: 'Slot Finder' %>

<script>
  function formatTime(date) {
    const londonDate = new Date(date.toLocaleString("en-US", {timeZone: "Europe/London"}));
    let hours = londonDate.getHours();
    let minutes = londonDate.getMinutes();
    const ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12;
    if (hours === 0) hours = 12;
    minutes = minutes < 10 ? '0' + minutes : minutes;
    return `${hours}.${minutes}${ampm}`;
  }

  function formatDuration(minutes) {
    const hrs = Math.floor(minutes / 60);
    const mins = minutes % 60;
    let durationStr = '';
    if (hrs > 0) {
      durationStr += `${hrs} hour${hrs > 1 ? 's' : ''}`;
    }
    if (mins > 0) {
      if (hrs > 0) durationStr += ' ';
      durationStr += `${mins} minute${mins > 1 ? 's' : ''}`;
    }
    return durationStr;
  }
</script>

<!-- NOTE: timenav search feature -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
  const $input   = $('#timenav-search-input');
  const $all     = $('#timenav-container .timenav-interval');
  const txt      = h=>$('<div>').html(h).text().toLowerCase();

  $input.on('input',function(){
    const q=$(this).val().trim().toLowerCase();
    $all.removeClass('highlight dim');
    if(!q) return;
    $all.each(function(){
      const hit=txt($(this).attr('data-tippy-content')||'').includes(q);
      $(this).toggleClass('highlight',hit).toggleClass('dim',!hit);
    });
  });
  });
</script>

<!-- Auto-open slot finder when patient_id parameter is present -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    const urlParams = new URLSearchParams(window.location.search);
    const patientId = urlParams.get('patient_id');
    const autoOpenSlotFinder = urlParams.get('auto_open_slot_finder');

    if (patientId && autoOpenSlotFinder === 'true') {
      // Wait a moment for the page to fully load
      setTimeout(() => {
        $.ajax({
          url: `/admin/calendar_bookings/slot_finder_offcanvas`,
          type: 'GET',
          dataType: 'script',
          data: {
            patient_id: patientId,
            date: new Date().toISOString().split('T')[0]
          }
        });
      }, 500);
    }
  });
</script>
