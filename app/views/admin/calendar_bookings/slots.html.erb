<div class="mt-4 space-y-5">
  <div class="mb-4">
    <div class="border border-gray-200 rounded-lg overflow-hidden">
      <div class="flex items-center border-b border-gray-200">
        <button id="slots-prev-day-button" class="cursor-pointer p-3 hover:bg-gray-50">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </button>
        <div id="current-date-label" class="flex-1 text-center text-[16px] py-3 font-medium"><%= @date.strftime('%a %d %b %Y') %></div>

        <button id="slots-next-day-button" class="cursor-pointer p-3 hover:bg-gray-50">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </button>
      </div>
      <div class="flex divide-x divide-gray-200">
        <button id="slots-today-button" class="cursor-pointer flex-1 py-2 text-[13px] hover:bg-gray-50">
          Today
        </button>

        <button id="slots-plus-1w-button" class="cursor-pointer flex-1 py-2 text-[13px] hover:bg-gray-50">
          +1W
        </button>

        <button id="slots-plus-3m-button" class="cursor-pointer flex-1 py-2 text-[13px] hover:bg-gray-50">
          +3M
        </button>

        <button id="slots-plus-6m-button" class="cursor-pointer flex-1 py-2 text-[13px] hover:bg-gray-50">
          +6M
        </button>
      </div>
    </div>
  </div>

  <div>
    <% if @slots.empty? %>
      <%= render partial: 'admin/calendar_bookings/no_items_placeholder', locals: { text: 'No Available Slots' } %>
    <% end %>

    <% if @preferred_slots.present? %>
      <h3 class="text-[14px] font-medium text-gray-700 mb-3 flex items-center">
        <div class="w-2 h-2 bg-emerald-300 rounded-full mr-2"></div>
        Preferred Slots
      </h3>
      <div class="space-y-3 mb-6">
        <% @preferred_slots.each do |slot| %>
          <div class="bg-white rounded-xl p-3 relative cursor-pointer transition-all border border-emerald-100 shadow-sm hover:shadow-md hover:border-emerald-200 group overflow-hidden">
            <div class="absolute top-0 left-0 w-1 h-full bg-emerald-300"></div>

            <div class="flex justify-between items-center pl-3">
              <div class="flex items-center space-x-3">
                <div class="flex flex-col">
                  <div class="font-semibold text-[15px] text-gray-900"><%= slot.begin.strftime('%H:%M') %> - <%= slot.end.strftime('%H:%M') %></div>
                  <div class="text-[11px] text-gray-500 mt-0.5"><%= slot.begin.strftime('%a, %d %b') %></div>
                </div>
              </div>
              <div class="flex items-center gap-3">
                <div class="flex items-center bg-gray-50 px-2 py-1 rounded-full">
                  <span class="relative flex shrink-0 overflow-hidden rounded-full h-6 w-6 mr-1.5">
                    <%= render 'layouts/shared/user_avatar', user: slot.practitioner, width: 24, height: 24 %>
                  </span>

                  <div class="text-[11px] font-medium text-gray-700 whitespace-nowrap"><%= slot.practitioner.full_name_with_title %></div>
                </div>
                <button class="slots-book-now-button cursor-pointer justify-center whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary hover:bg-primary/90 h-9 bg-gradient-to-r from-[#a8d8ea] to-[#86c6e0] hover:from-[#86c6e0] hover:to-[#6ab4d0] text-[#1a4657] text-[11px] px-4 py-1.5 rounded-full transition-all duration-300 transform group-hover:scale-105 font-medium shadow-sm hover:shadow flex items-center gap-1.5"
                        data-slot-start="<%= slot.begin %>"
                        data-slot-end="<%= slot.end %>"
                        data-slot-date="<%= slot.begin.to_date %>">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-current">
                    <path d="M21 14V8C21 6.89543 20.1046 6 19 6H5C3.89543 6 3 6.89543 3 8V14C3 15.1046 3.89543 16 5 16H19C20.1046 16 21 15.1046 21 14Z" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M3 10H21" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M16 14H8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                  Book Slot
                </button>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% end %>

    <% if @reserved_slots.present? %>
      <h3 class="text-[14px] font-medium text-gray-700 mb-3 flex items-center">
        <div class="w-2 h-2 bg-purple-300 rounded-full mr-2"></div>
        Reserved Slots
      </h3>
      <div class="space-y-3 mb-6">
        <% @reserved_slots.each do |slot| %>
          <div class="bg-white rounded-xl p-3 relative cursor-pointer transition-all border border-purple-100 shadow-sm hover:shadow-md hover:border-purple-200 group overflow-hidden">
            <div class="absolute top-0 left-0 w-1 h-full bg-purple-300"></div>

            <div class="flex justify-between items-center pl-3">
              <div class="flex items-center space-x-3">
                <div class="flex flex-col">
                  <div class="font-semibold text-[15px] text-gray-900"><%= slot.begin.strftime('%H:%M') %> - <%= slot.end.strftime('%H:%M') %></div>
                  <div class="text-[11px] text-gray-500 mt-0.5"><%= slot.begin.strftime('%a, %d %b') %></div>
                  <% if slot.reserved_treatment_name.present? %>
                    <div class="text-[10px] text-purple-600 font-medium mt-1 bg-purple-50 px-2 py-0.5 rounded-full inline-block">
                      <%= slot.reserved_treatment_name %>
                    </div>
                  <% end %>
                </div>
              </div>
              <div class="flex items-center gap-3">
                <div class="flex items-center bg-gray-50 px-2 py-1 rounded-full">
                  <span class="relative flex shrink-0 overflow-hidden rounded-full h-6 w-6 mr-1.5">
                    <%= render 'layouts/shared/user_avatar', user: slot.practitioner, width: 24, height: 24 %>
                  </span>

                  <div class="text-[11px] font-medium text-gray-700 whitespace-nowrap"><%= slot.practitioner.full_name_with_title %></div>
                </div>
                <button class="slots-book-now-button cursor-pointer justify-center whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary hover:bg-primary/90 h-9 bg-gradient-to-r from-[#a8d8ea] to-[#86c6e0] hover:from-[#86c6e0] hover:to-[#6ab4d0] text-[#1a4657] text-[11px] px-4 py-1.5 rounded-full transition-all duration-300 transform group-hover:scale-105 font-medium shadow-sm hover:shadow flex items-center gap-1.5"
                        data-slot-start="<%= slot.begin.in_time_zone %>"
                        data-slot-end="<%= slot.end.in_time_zone %>"
                        data-slot-date="<%= slot.begin.to_date %>">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-current">
                    <path d="M21 14V8C21 6.89543 20.1046 6 19 6H5C3.89543 6 3 6.89543 3 8V14C3 15.1046 3.89543 16 5 16H19C20.1046 16 21 15.1046 21 14Z" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M3 10H21" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M16 14H8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                  Book Slot
                </button>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% end %>

    <% if @alternative_slots.present? %>
      <h3 class="text-[14px] font-medium text-gray-700 mb-3 flex items-center">
        <div class="w-2 h-2 bg-sky-300 rounded-full mr-2"></div>
        Alternative Slots
      </h3>
      <div class="space-y-3">
        <% @alternative_slots.each do |slot| %>
          <div class="bg-white rounded-xl p-3 relative cursor-pointer transition-all border border-gray-100 shadow-sm hover:shadow-md hover:border-sky-200 group overflow-hidden">
            <div class="absolute top-0 left-0 w-1 h-full bg-sky-300"></div>
            <div class="flex justify-between items-center pl-3">
              <div class="flex items-center space-x-3">
                <div class="flex flex-col">
                  <div class="font-semibold text-[15px] text-gray-900"><%= slot.begin.strftime('%H:%M') %> - <%= slot.end.strftime('%H:%M') %></div>
                  <div class="text-[11px] text-gray-500 mt-0.5"><%= slot.begin.strftime('%a, %d %b') %></div>
                </div>
              </div>
              <div class="flex items-center gap-3">
                <div class="flex items-center bg-gray-50 px-2 py-1 rounded-full">
                  <span class="relative flex shrink-0 overflow-hidden rounded-full h-6 w-6 mr-1.5">
                    <%= render 'layouts/shared/user_avatar', user: slot.practitioner, width: 24, height: 24 %>
                  </span>
                  <div class="text-[11px] font-medium text-gray-700 whitespace-nowrap"><%= slot.practitioner.full_name_with_title %></div>
                </div>
                <button class="slots-book-now-button cursor-pointer justify-center whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary hover:bg-primary/90 h-9 bg-gradient-to-r from-[#a8d8ea] to-[#86c6e0] hover:from-[#86c6e0] hover:to-[#6ab4d0] text-[#1a4657] text-[11px] px-4 py-1.5 rounded-full transition-all duration-300 transform group-hover:scale-105 font-medium shadow-sm hover:shadow flex items-center gap-1.5"
                        data-slot-start="<%= slot.begin %>"
                        data-slot-end="<%= slot.end %>"
                        data-slot-date="<%= slot.begin.to_date %>">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-current">
                    <path d="M21 14V8C21 6.89543 20.1046 6 19 6H5C3.89543 6 3 6.89543 3 8V14C3 15.1046 3.89543 16 5 16H19C20.1046 16 21 15.1046 21 14Z" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M3 10H21" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M16 14H8" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                  Book Slot
                </button>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% end %>
  </div>
</div>
