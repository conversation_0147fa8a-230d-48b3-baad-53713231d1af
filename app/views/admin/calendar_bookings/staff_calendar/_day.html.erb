<%= render 'admin/calendar_bookings/staff_calendar/calendar_navigation' %>

<div class="day-calendar-container">
  <% if Time.now.in_time_zone('London').hour >= 8 && Time.now.in_time_zone('London').hour <= 18 && @date == Date.current %>
    <div class="day-calendar-current-time-horizonal-line absolute bg-[#FBE099] w-full h-[2px] z-[999]"
         style="top: <%= horizontal_time_line_height_px(@timeline) + 140 %>px;"></div>
  <% end %>

  <div class="flex-none min-w-[80px] text-right pt-[162px] bg-[#f5f5f6]" style="z-index: 6;">
    <%= render 'admin/calendar_bookings/staff_calendar/timeline' %>
  </div>

  <div class="day-calendar">
    <div class="day-calendar-content">
      <% @filtered_practitioners.each do |dentist| %>
        <div class="day-calendar-dentist-column"
          style="width: calc(100% / <%= [@filtered_practitioners.count, 6].min %>);"
          data-practitioner-id="<%= dentist.id %>"
          data-date="<%= @date.to_s %>"
          data-schedules="<%= (@shifts_by_practitioner[dentist.id] || []).to_json %>"
        >
          <%= render 'admin/calendar_bookings/staff_calendar/column_header', dentist: dentist, date: @date %>

          <!-- NOTE: render calendar booking cards -->
          <% @bookings.each do |booking| %>
            <% if booking.practitioner_id == dentist.id %>
              <%= render 'admin/calendar_bookings/staff_calendar/calendar_booking_card',
                booking: booking,
                card_klass: 'day-calendar-booking',
                card_id: "day-calendar-booking-#{booking.id}",
                card_top: calendar_item_top_px(booking.start_time_of_day, @timeline) + 160,
                card_height: calendar_item_height_px(booking.duration_in_minutes, @timeline) - 2,
                card_bg_color: booking.booking_type_event? ? booking.event_color : @calendar_reserved_block_types.find { _1.treatment_id == booking.treatment_id }&.color
              %>
            <% end %>
          <% end %>

          <!-- NOTE: render reserved block cards -->
          <% @reserved_blocks.each do |reserved_block| %>
            <% if reserved_block.practitioner_id == dentist.id && reserved_block.start_time.to_date == @date %>
              <%= render 'admin/calendar_bookings/staff_calendar/reserved_block_card',
                reserved_block: reserved_block,
                timeline: @timeline,
                top: calendar_item_top_px(reserved_block.start_time.seconds_since_midnight, @timeline) + 164,
                height: calendar_item_height_px(reserved_block.duration_in_minutes, @timeline) - 2
              %>
            <% end %>
          <% end %>

          <!-- NOTE: render holiday cards -->
          <% @holidays.each do |holiday| %>
            <% if holiday.user_id == dentist.id %>
              <% if holiday.custom_hours? %>
                <% duration_in_minutes = (Time.parse(holiday.event_end) - Time.parse(holiday.event_start)) / 60 %>
                <% card_top = calendar_item_top_px(Time.parse(holiday.event_start).seconds_since_midnight, @timeline) + 164 %>
                <% card_height = calendar_item_height_px(duration_in_minutes, @timeline) - 2 %>
              <% end %>

              <%= render 'admin/calendar_bookings/staff_calendar/holiday_card',
                holiday: holiday,
                timeline: @timeline,
                top: card_top,
                height: card_height
              %>
            <% end %>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
</div>

<%= render 'admin/calendar_bookings/staff_calendar/calendar_day_note_modal' %>
<%= render 'admin/calendar_bookings/staff_calendar/payment_request_modal' %>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const calendarContainer = document.querySelector('.day-calendar');

    if (!calendarContainer) return;

    const calendar = document.querySelector('.day-calendar-content');
    const leftButton = document.querySelector('.calendar-nav-button-left');
    const rightButton = document.querySelector('.calendar-nav-button-right');
    let timeline = <%= raw(@timeline.to_json) %>;

    function getColumnWidth() {
      const column = calendar.querySelector('.day-calendar-dentist-column');
      const gap = 12;
      return column ? column.offsetWidth + gap : 0;
    }

    function scrollCalendar(direction) {
      const columnWidth = getColumnWidth();
      const scrollBy = columnWidth > 800 ? 800 : columnWidth;

      calendarContainer.scrollBy({
        left: scrollBy * direction,
        behavior: 'smooth'
      });
    }

    leftButton.addEventListener('click', function() {
      scrollCalendar(-1);
    });
    rightButton.addEventListener('click', function() {
      scrollCalendar(1);
    });

    function updateButtonStates() {
      const isScrollable = calendarContainer.scrollWidth > calendarContainer.clientWidth;

      leftButton.classList.toggle('disabled', !isScrollable);
      rightButton.classList.toggle('disabled', !isScrollable);

      if (isScrollable) {
        calendar.classList.add('has-arrows');
      } else {
        calendar.classList.remove('has-arrows');
      }
    }

    calendarContainer.addEventListener('scroll', updateButtonStates);
    updateButtonStates();

    let isDragging = false;
    let startY = null;
    let currentDentistColumn = null;
    let dragElement = null;
    let startStepIndex = null;
    let endStepIndex = null;
    let currentNonBookableBlocks = [];

    const stepSize = <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>;

    function calculateSteps(startY, currentY) {
      const delta = (currentY - startY) / stepSize;
      if (delta >= 0) {
        return Math.floor(delta);
      } else {
        return Math.ceil(delta);
      }
    }

    function findNearestTimeLabel(startY) {
      return Math.round(startY / stepSize) * stepSize;
    }

    function findStepIndexByYPosition(yPosition) {
      const relativeY = yPosition / stepSize;
      return Math.round(relativeY);
    }

    function isInClipboardMode() {
      return document.querySelector('.clipboard-preview') !== null;
    }

    function isInStatusOverlayMode() {
      return $('.booking-status-label-modal-overlay:visible').length > 0
    }

    function createDragElement(column, startY) {
      const dragEl = document.createElement('div');

      dragEl.classList.add('dragging-booking');
      dragEl.style.position = 'absolute';
      dragEl.style.left = '6px';
      dragEl.style.top = `${startY}px`;
      dragEl.style.width = 'calc(100% - 12px)';
      dragEl.style.height = `${stepSize}px`;
      dragEl.style.userSelect = 'none';
      dragEl.style.padding = '10px';
      dragEl.style.background = 'rgba(137, 153, 176, 0.20)';
      dragEl.style.borderRadius = '14px';
      dragEl.style.overflow = 'hidden';
      dragEl.style.border = '2px #2A4558 dotted';
      dragEl.style.display = 'inline-flex';
      dragEl.style.flexDirection = 'column';
      dragEl.style.justifyContent = 'center';
      dragEl.style.alignItems = 'center';
      dragEl.style.gap = '10px';
      dragEl.style.zIndex = '3';

      const firstText = document.createElement('div');
      firstText.classList.add('first-text');
      firstText.style.color = '#2A4558';
      firstText.style.fontSize = '14px';
      firstText.style.fontFamily = 'Poppins';
      firstText.style.fontWeight = '400';
      firstText.style.wordWrap = 'break-word';

      const secondText = document.createElement('div');
      secondText.classList.add('second-text');
      secondText.style.color = '#2A4558';
      secondText.style.fontSize = '12px';
      secondText.style.fontFamily = 'Poppins';
      secondText.style.fontWeight = '700';
      secondText.style.wordWrap = 'break-word';

      dragEl.appendChild(firstText);
      dragEl.appendChild(secondText);

      column.appendChild(dragEl);
      return dragEl;
    }

    function removeExistingDragElements() {
      const existingDragElements = document.querySelectorAll('.dragging-booking');
      existingDragElements.forEach(el => el.remove());
    }

    var currentDate;
    calendar.addEventListener('mousedown', function(event) {
      if (isInClipboardMode() || isInStatusOverlayMode()) {
        return;
      }

      const dentistColumn = event.target.closest('.day-calendar-dentist-column');
      if (dentistColumn && !event.target.closest('.day-calendar-booking')) {
        const date = dentistColumn.dataset.date;
        currentDate = date;

        let clickY = event.clientY - dentistColumn.getBoundingClientRect().top;
        if (clickY < 162) {
          return;
        }
        isDragging = true;
        startY = clickY - 178;
        startY = findNearestTimeLabel(startY);
        startStepIndex = findStepIndexByYPosition(startY);
        endStepIndex = startStepIndex;
        currentDentistColumn = dentistColumn;
        removeExistingDragElements();
        dragElement = createDragElement(currentDentistColumn, startY + 162);

        // NOTE: find all non-bookable blocks in the column - booking cards and non working shift times
        currentNonBookableBlocks = Array.from(dentistColumn.querySelectorAll('.day-calendar-booking, .no-shift-overlay')).map(booking => {
          const bookingRect = booking.getBoundingClientRect();
          const columnRect = currentDentistColumn.getBoundingClientRect();
          const top = bookingRect.top - columnRect.top - 162;
          const height = booking.offsetHeight;
          const snappedTop = Math.round(top / stepSize) * stepSize;
          const snappedBottom = Math.round((top + height) / stepSize) * stepSize;
          return { top: snappedTop, bottom: snappedBottom };
        });
      }
    });

    document.addEventListener('mousemove', function(event) {
      if (isInClipboardMode() || isInStatusOverlayMode()) {
        return;
      }

      if (isDragging && dragElement) {
        // NOTE: auto-scrolling when dragging near the edge of the viewport
        const scrollMargin = 50;
        const maxScrollSpeed = 20;
        const cursorY = event.clientY;
        const cursorX = event.clientX;
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        if (cursorY < scrollMargin) {
          window.scrollBy(0, -maxScrollSpeed);
        } else if (cursorY > viewportHeight - scrollMargin) {
          window.scrollBy(0, maxScrollSpeed);
        }

        let currentY = event.clientY - currentDentistColumn.getBoundingClientRect().top - 162;
        if (currentY < 0) {
          currentY = 0;
        }
        const draggingDown = currentY >= startY;
        let dragTop = Math.min(startY, currentY);
        let dragBottom = Math.max(startY, currentY);

        dragTop = Math.round(dragTop / stepSize) * stepSize;
        dragBottom = Math.round(dragBottom / stepSize) * stepSize;

        for (let block of currentNonBookableBlocks) {
          if (dragBottom > block.top && dragTop < block.bottom) {
            if (draggingDown) {
              dragBottom = block.top;
              currentY = startY + Math.floor((dragBottom - startY) / stepSize) * stepSize - stepSize;
            } else {
              dragTop = block.bottom;
              currentY = startY + Math.ceil((dragTop - startY) / stepSize) * stepSize;
            }
            if (dragBottom <= dragTop) {
              isDragging = false;
              dragElement.remove();
              dragElement = null;
              return;
            }
            break;
          }
        }
        const steps = calculateSteps(startY, currentY);
        let newTop, newHeight;
        if (steps >= 0) {
          newTop = startY + 162;
          newHeight = (steps + 1) * stepSize;
        } else {
          newTop = startY + steps * stepSize + 162;
          newHeight = Math.abs(steps) * stepSize;
        }
        dragElement.style.top = `${newTop}px`;
        dragElement.style.height = `${newHeight}px`;
        endStepIndex = startStepIndex + steps;

        let startTimeStr, endTimeStr;
        if (startStepIndex <= endStepIndex) {
          startTimeStr = timeline[startStepIndex].start_time;
          endTimeStr = timeline[endStepIndex].end_time;
        } else {
          startTimeStr = timeline[endStepIndex].start_time;
          endTimeStr = timeline[startStepIndex].start_time;
        }

        const startTime = new Date(startTimeStr);
        const endTime = new Date(endTimeStr);

        const firstText = dragElement.querySelector('.first-text');
        const secondText = dragElement.querySelector('.second-text');

        if (firstText && secondText && startTime && endTime) {
          firstText.innerText = `${formatTime(startTime)} - ${formatTime(endTime)}`;
          const durationMinutes = (endTime - startTime) / 60000;
          secondText.innerText = formatDuration(durationMinutes);
        }
      }
    });

    document.addEventListener('mouseup', function(event) {
      if (isInClipboardMode() || isInStatusOverlayMode()) {
        return;
      }

      if (isDragging && dragElement) {
        isDragging = false;
        dragElement.style.pointerEvents = 'none';

        if (startStepIndex < 0) startStepIndex = 0;
        if (endStepIndex < 0) endStepIndex = 0;
        if (startStepIndex >= timeline.length) startStepIndex = timeline.length - 1;
        if (endStepIndex >= timeline.length) endStepIndex = timeline.length - 1;

        let startTimeStr, endTimeStr;
        if (startStepIndex <= endStepIndex) {
          startTimeStr = timeline[startStepIndex].start_time;
          endTimeStr = timeline[endStepIndex].end_time;
        } else {
          startTimeStr = timeline[endStepIndex].start_time;
          endTimeStr = timeline[startStepIndex].start_time;
        }

        const startTime = new Date(startTimeStr);
        const endTime = new Date(endTimeStr);

        const firstText = dragElement.querySelector('.first-text');
        const secondText = dragElement.querySelector('.second-text');

        if (firstText && secondText && startTime && endTime) {
          firstText.innerText = `${formatTime(startTime)} - ${formatTime(endTime)}`;
          const durationMinutes = (endTime - startTime) / 60000;
          secondText.innerText = formatDuration(durationMinutes);
        }

        if (startTime && endTime) {
          console.log(`New booking from ${startTime} to ${endTime}`);

          const mode = '<%= @mode %>';
          const dateParam = '<%= @date %>';
          const hiddenDentists = JSON.stringify('<%= raw(params[:hidden_dentists].presence || [].to_json) %>');

          $.ajax({
            url: '/admin/calendar_bookings/drag_offcanvas',
            method: 'GET',
            data: {
              practitioner_id: currentDentistColumn.dataset.practitionerId,
              start_time: startTimeStr,
              end_time: endTimeStr,
              mode: mode,
              date: currentDate,
              hidden_dentists: hiddenDentists
            },
            success: function(response) {}
          });
        }
        dragElement = null;
        currentDentistColumn = null;
      }
    });

    let bookingElement = null;
    let currentBookingId = null;

    function addLinesToColumn(column, startHeight, lineSpacing) {
      const existingLines = column.querySelectorAll('.horizontal-line');
      existingLines.forEach(line => line.remove());

      const columnHeight = column.offsetHeight;

      const numberOfLines = Math.floor((columnHeight - startHeight) / lineSpacing);

      for (let i = 0; i <= numberOfLines; i++) {
        const line = document.createElement('div');
        line.classList.add('horizontal-line');
        line.style.position = 'absolute';
        line.style.top = (startHeight + i * lineSpacing) + 'px';
        line.style.left = '0';
        line.style.width = '100%';
        line.style.height = '0';

        if (i % 6 === 0) {
          line.style.borderTop = '1px solid #D8D8D8';
          line.style.opacity = '1';
        } else if (i % 3 === 0) {
          line.style.borderTop = '1px dashed #D8D8D8';
          line.style.opacity = '0.7';
        } else {
          line.style.borderTop = '1px dotted #D8D8D8';
          line.style.opacity = '0.3';
        }

        column.appendChild(line);
      }
    }

    const columns = document.querySelectorAll('.day-calendar-dentist-column');

    columns.forEach(column => {
      const startHeight = 162;
      const lineSpacing = <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>;
      addLinesToColumn(column, startHeight, lineSpacing);
    });
  }, { once: true });
</script>

<!-- Highlight feature -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    <% if @highlight_booking.present? %>
      const highlightedBookingElement = document.getElementsByClassName('calendar-booking-highlight')[0];

      if (!highlightedBookingElement) {
        return;
      }

      if (highlightedBookingElement) {
        const rect = highlightedBookingElement.getBoundingClientRect();
        const elementTop = rect.top + window.pageYOffset;
        const windowHeight = window.innerHeight;
        const scrollToPosition = elementTop - (windowHeight / 2);

        window.scrollTo({
          top: scrollToPosition,
          behavior: 'instant'
        });
      }
    <% else %>
      const timeLineElement = document.querySelector('.day-calendar-current-time-horizonal-line');

      if (!timeLineElement) {
        return;
      }

      if (timeLineElement) {
        const rect = timeLineElement.getBoundingClientRect();
        const elementTop = rect.top + window.pageYOffset;
        const windowHeight = window.innerHeight;
        const scrollToPosition = elementTop - (windowHeight / 2);

        window.scrollTo({
          top: scrollToPosition,
          behavior: 'smooth'
        });
      }
    <% end %>
  }, { once: true });
</script>

<!-- Clipboard feature -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    let isClipboardMode = false;
    let clipboardBookingData = null;
    let clipboardPreviewElement = null;
    let currentPractitionerId = null;
    let currentHighlightElement = null;
    const stepSize = <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>;
    const timeline = <%= raw(@timeline.to_json) %>;

    // Minimal persistence - just save and restore clipboard state
    function saveClipboardState() {
      if (isClipboardMode && clipboardBookingData) {
        sessionStorage.setItem('calendar_clipboard_state', JSON.stringify({
          isClipboardMode: true,
          clipboardBookingData: clipboardBookingData,
          currentPractitionerId: currentPractitionerId
        }));
      }
    }

    function restoreClipboardState() {
      try {
        const savedState = sessionStorage.getItem('calendar_clipboard_state');
        if (savedState) {
          const state = JSON.parse(savedState);
          isClipboardMode = state.isClipboardMode;
          clipboardBookingData = state.clipboardBookingData;
          currentPractitionerId = state.currentPractitionerId;

          if (isClipboardMode && clipboardBookingData) {
            clipboardPreviewElement = createClipboardPreview(clipboardBookingData);
            document.body.style.cursor = 'grabbing';

            const clearClipboard = document.querySelector('.clear-clipboard');
            if (clearClipboard) {
              clearClipboard.style.display = 'flex';
            }

            const allDentistColumns = document.querySelectorAll('.day-calendar-dentist-column');
            allDentistColumns.forEach(column => {
              if (column.dataset.practitionerId !== currentPractitionerId) {
                column.style.filter = 'blur(4px)';
              }
            });
          }
        }
      } catch (error) {
        console.warn('Failed to restore clipboard state:', error);
        sessionStorage.removeItem('calendar_clipboard_state');
      }
    }

    function clearClipboardState() {
      sessionStorage.removeItem('calendar_clipboard_state');
    }

    function createClipboardPreview(bookingData) {
      const preview = document.createElement('div');
      preview.className = 'clipboard-preview';
      const nameDiv = document.createElement('div');
      nameDiv.className = 'name';
      nameDiv.innerText = bookingData.patientName;
      const timeDiv = document.createElement('div');
      timeDiv.className = 'time';
      timeDiv.innerText = bookingData.timeRange;
      const durationDiv = document.createElement('div');
      durationDiv.className = 'duration';
      durationDiv.innerText = bookingData.durationText;
      preview.appendChild(nameDiv);
      preview.appendChild(timeDiv);
      preview.appendChild(durationDiv);
      document.body.appendChild(preview);
      return preview;
    }

    function removeHighlight() {
      if (currentHighlightElement) {
        currentHighlightElement.remove();
        currentHighlightElement = null;
      }
      if (clipboardPreviewElement) {
        clipboardPreviewElement.classList.remove('valid');
        clipboardPreviewElement.style.opacity = '0.5';
      }
    }

    function checkOverlap(column, startIndex, endIndex) {
      const blocks = Array.from(column.querySelectorAll('.day-calendar-booking, .no-shift-overlay')).map(block => {
        const top = parseInt(block.style.top) - 164;
        const height = parseInt(block.style.height) + 2;
        const blockStartIndex = Math.round(top / stepSize);
        const blockEndIndex = Math.round((top + height) / stepSize);
        return { start: blockStartIndex, end: blockEndIndex };
      });
      return blocks.some(block => !(endIndex <= block.start || startIndex >= block.end));
    }

    // Restore clipboard state on page load
    restoreClipboardState();

    const clearClipboard = document.querySelector('.clear-clipboard');
    clearClipboard.addEventListener('click', function() {
      isClipboardMode = false;
      clipboardBookingData = null;
      if (clipboardPreviewElement) {
        clipboardPreviewElement.remove();
        clipboardPreviewElement = null;
      }
      document.body.style.cursor = 'default';
      const allDentistColumns = document.querySelectorAll('.day-calendar-dentist-column');
      allDentistColumns.forEach(column => {
        column.style.filter = '';
      });
      removeHighlight();
      clearClipboard.style.display = 'none';
      clearClipboardState();
    });

    const clipboardIcons = document.querySelectorAll('.calendar-booking-clipboard-button');
    clipboardIcons.forEach(icon => {
      icon.addEventListener('click', function(event) {
        event.preventDefault();

        if (isClipboardMode) {
          return;
        }

        isClipboardMode = true;
        clearClipboard.style.display = 'flex';

        const bookingId = $(this).closest('.booking-actions-menu').data('bookingId')
        const bookingElement = document.querySelector(
          `.day-calendar-booking[data-booking-id='${bookingId}']`
        )

        const practitionerId = bookingElement.closest('.day-calendar-dentist-column').dataset.practitionerId;
        const patientNameElement = bookingElement.querySelector('.calendar-booking-patient-name');
        const timeRangeElement = bookingElement.querySelector('.calendar-booking-times');
        const durationTextElement = bookingElement.querySelector('.calendar-booking-treatment');

        const patientName = patientNameElement ? patientNameElement.innerText.trim() : '';
        const timeRange = timeRangeElement ? timeRangeElement.innerText.trim() : '';
        const durationText = durationTextElement ? durationTextElement.innerText.trim() : '';

        const duration = parseInt(bookingElement.style.height) + 2;
        clipboardBookingData = {
          bookingId,
          practitionerId,
          patientName,
          timeRange,
          durationText,
          duration
        };
        currentPractitionerId = practitionerId;
        clipboardPreviewElement = createClipboardPreview(clipboardBookingData);
        document.body.style.cursor = 'grabbing';
        const allDentistColumns = document.querySelectorAll('.day-calendar-dentist-column');
        allDentistColumns.forEach(column => {
          if (column.dataset.practitionerId !== currentPractitionerId) {
            column.style.filter = 'blur(4px)';
          }
        });

        // Save clipboard state
        saveClipboardState();
      });
    });

    document.addEventListener('mousemove', function(event) {
      if (isClipboardMode && clipboardPreviewElement) {
        const scrollY = window.scrollY || window.pageYOffset;
        const scrollX = window.scrollX || window.pageXOffset;
        clipboardPreviewElement.style.left = event.clientX + 10 + scrollX + 'px';
        clipboardPreviewElement.style.top = event.clientY + 10 + scrollY + 'px';
        const dentistColumn = event.target.closest('.day-calendar-dentist-column');
        if (dentistColumn && dentistColumn.dataset.practitionerId === currentPractitionerId) {
          const columnRect = dentistColumn.getBoundingClientRect();
          let clickY = event.clientY - columnRect.top - 160;
          if (clickY < 0) clickY = 0;
          const stepIndex = Math.floor(clickY / stepSize);
          const durationSteps = Math.round(clipboardBookingData.duration / stepSize);
          const endIndex = stepIndex + durationSteps;
          if (stepIndex >= 0 && endIndex <= timeline.length) {
            if (!checkOverlap(dentistColumn, stepIndex, endIndex)) {
              if (!currentHighlightElement) {
                currentHighlightElement = document.createElement('div');
                currentHighlightElement.className = 'clipboard-highlight';
                dentistColumn.appendChild(currentHighlightElement);
              }
              currentHighlightElement.style.top = (stepIndex * stepSize + 160) + 'px';
              currentHighlightElement.style.height = clipboardBookingData.duration + 'px';
              clipboardPreviewElement.classList.add('valid');
              clipboardPreviewElement.style.opacity = '1';
            } else {
              removeHighlight();
            }
          } else {
            removeHighlight();
          }
        } else {
          removeHighlight();
        }
      }
    });

    document.addEventListener('click', function(event) {
      if (isClipboardMode) {
        const dentistColumn = event.target.closest('.day-calendar-dentist-column');
        if (dentistColumn && dentistColumn.dataset.practitionerId === currentPractitionerId && currentHighlightElement) {
          const columnRect = dentistColumn.getBoundingClientRect();
          let clickY = event.clientY - columnRect.top - 160;
          if (clickY < 0) clickY = 0;
          const stepIndex = Math.floor(clickY / stepSize);
          const durationSteps = Math.round(clipboardBookingData.duration / stepSize);
          const endIndex = stepIndex + durationSteps;
          if (stepIndex >= 0 && endIndex <= timeline.length && !checkOverlap(dentistColumn, stepIndex, endIndex)) {
            Swal.fire({
              title: 'Move Appointment?',
              text: 'Do you want to move the appointment in the clipboard to this time slot?',
              icon: 'warning',
              showCancelButton: true,
              confirmButtonText: 'Yes, move it!',
              cancelButtonText: 'No, cancel'
            }).then((result) => {
              if (result.isConfirmed) {
                isClipboardMode = false;
                const bookingId = clipboardBookingData.bookingId;
                clipboardBookingData = null;
                if (clipboardPreviewElement) {
                  clipboardPreviewElement.remove();
                  clipboardPreviewElement = null;
                }
                document.body.style.cursor = 'default';
                const allDentistColumns = document.querySelectorAll('.day-calendar-dentist-column');
                allDentistColumns.forEach(column => {
                  column.style.filter = '';
                });
                clearClipboard.style.display = 'none';
                removeHighlight();
                clearClipboardState();

                $.ajax({
                  url: '/admin/calendar_bookings/' + bookingId + '/move',
                  method: 'POST',
                  data: {
                    calendar_booking: {
                      start_time: timeline[stepIndex].start_time,
                      end_time: timeline[endIndex].start_time,
                      date: dentistColumn.dataset.date,
                    }
                  },
                  success: function(response) {
                    location.reload();
                  },
                  error: function(xhr, status, error) {
                    var text;
                    if (error === 'Forbidden') {
                      text = 'You do not have permission to move this appointment.';
                    } else {
                      text = 'An error occurred while moving the appointment. Please try again.';
                    }

                    Swal.fire({
                      icon: 'error',
                      title: 'Error',
                      text: text
                    });
                  }
                });
              }
            });
          }
        }
      }
    });

    document.addEventListener('keydown', function(event) {
      if (isClipboardMode && event.key === 'Escape') {
        isClipboardMode = false;
        clipboardBookingData = null;
        if (clipboardPreviewElement) {
          clipboardPreviewElement.remove();
          clipboardPreviewElement = null;
        }
        document.body.style.cursor = 'default';
        const allDentistColumns = document.querySelectorAll('.day-calendar-dentist-column');
        allDentistColumns.forEach(column => {
          column.style.filter = '';
        });
        removeHighlight();

        const clearClipboard = document.querySelector('.clear-clipboard');
        clearClipboard.style.display = 'none';
        clearClipboardState();
      }
    });
  });
</script>

<!-- Timenav feature -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    $('.timenav-bar-day').click(function(event) {
      const timenavBar = event.currentTarget;
      const practitionerId = timenavBar.dataset.practitionerId;

      const rect = timenavBar.getBoundingClientRect();
      const timePercentage = (event.clientX - rect.left) / rect.width;
      const totalShiftMinutes = 14 * 60; // 8 AM to 10 PM
      const minutesIntoShift = totalShiftMinutes * timePercentage;

      const time = new Date();
      time.setHours(8, 0, 0, 0); // Start time at 8:00 AM
      time.setMinutes(time.getMinutes() + minutesIntoShift);

      scrollToDentistAndTime(practitionerId, time);
    });

    function scrollToDentistAndTime(practitionerId, time) {
      const calendarContainer = document.querySelector('.day-calendar');
      const calendarContent = document.querySelector('.day-calendar-content');
      const targetDentistColumn = document.querySelector(`.day-calendar-dentist-column[data-practitioner-id="${practitionerId}"]`);

      if (!targetDentistColumn) return;

      const dentistColumnOffset = targetDentistColumn.offsetLeft;
      calendarContainer.scrollTo({
        left: dentistColumnOffset - 1000,
        behavior: 'smooth'
      });

      const timelineStartTime = new Date();
      timelineStartTime.setHours(8, 0, 0, 0);
      const minutesFromStart = (time - timelineStartTime) / 60000;

      const stepSize = <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>;
      const totalSteps = minutesFromStart / 5;
      const verticalOffset = totalSteps * stepSize;

      const calendarContainerVertical = document.querySelector('.day-calendar-container');
      window.scrollTo({
        top: verticalOffset,
        behavior: 'smooth'
      });
    }
  }, { once: true });
</script>

<!-- NOTE: fixed header for dentist columns feature -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    const columns = document.querySelectorAll('.day-calendar-dentist-column');
    const fixedHeaders = [];

    function createFixedHeaders() {
      columns.forEach(column => {
        const originalHeader = column.querySelector('.calendar-dentist-header');
        if (originalHeader) {
          const fixedHeader = originalHeader.cloneNode(true);

          fixedHeader.style.position = 'fixed';
          fixedHeader.style.top = '140px';
          fixedHeader.style.zIndex = '4';
          fixedHeader.style.margin = '0';
          fixedHeader.style.width = `${column.offsetWidth}px`;
          fixedHeader.style.backgroundColor = 'rgba(255, 255, 255, 0.85)';
          fixedHeader.style.backdropFilter = 'blur(6px)';
          fixedHeader.style.webkitBackdropFilter = 'blur(6px)';

          updateHeaderPosition(column, fixedHeader);

          document.body.appendChild(fixedHeader);

          fixedHeaders.push({ column, originalHeader, fixedHeader });

          applyCalendarDayNoteHandlers();
        }
      });
    }

    function updateHeaderPosition(column, fixedHeader) {
      const columnRect = column.getBoundingClientRect();
      const columnLeftInViewport = columnRect.left;

      if (columnRect.right < 0 || columnRect.left > window.innerWidth) {
        fixedHeader.style.display = 'none';
      } else {
        fixedHeader.style.display = 'block';
      }

      fixedHeader.style.left = `${columnLeftInViewport}px`;
    }

    function updateFixedHeaders() {
      fixedHeaders.forEach(({ fixedHeader, column, originalHeader }) => {
        updateHeaderPosition(column, fixedHeader);
      });

      if (window.scrollY >= 240) {
        fixedHeaders.forEach(({ fixedHeader, originalHeader }) => {
          fixedHeader.style.visibility = 'visible';
          if (originalHeader) {
            originalHeader.style.visibility = 'hidden';
          }
        });
      } else {
        fixedHeaders.forEach(({ fixedHeader, originalHeader }) => {
          fixedHeader.style.visibility = 'hidden';
          if (originalHeader) {
            originalHeader.style.visibility = 'visible';
          }
        });
      }
    }

    createFixedHeaders();

    document.querySelector('.day-calendar').addEventListener('scroll', updateFixedHeaders);
    window.addEventListener('scroll', updateFixedHeaders);
    window.addEventListener('resize', updateFixedHeaders);

    updateFixedHeaders();
  }, { once: true });
</script>

<!-- NOTE: no shift overlay feature -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    const container = document.querySelector('.day-calendar-container');
    if (!container) return;
    const SHIFT_START = <%= @practice_opening_time.to_i - @practice_opening_time.beginning_of_day.to_i %>;
    const SHIFT_END = <%= @practice_closing_time.to_i - @practice_opening_time.beginning_of_day.to_i %>;
    const stepSize = <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>;
    function pxForSec(sec) {
      if (sec < SHIFT_START) sec = SHIFT_START;
      if (sec > SHIFT_END) sec = SHIFT_END;
      return ((sec - SHIFT_START) / 300) * stepSize + 162;
    }
    function gaps(schedules) {
      const r = [];
      let c = SHIFT_START;
      schedules.forEach(s => {
        const st = Math.max(s.start, SHIFT_START);
        const en = Math.min(s.end, SHIFT_END);
        if (st > c) r.push({ start: c, end: st });
        if (en > c) c = en;
      });
      if (c < SHIFT_END) r.push({ start: c, end: SHIFT_END });
      return r;
    }
    document.querySelectorAll('.day-calendar-dentist-column').forEach(col => {
      let arr;
      try { arr = JSON.parse(col.dataset.schedules || "[]"); } catch(e) { arr = []; }
      arr.sort((a,b) => a.start - b.start);
      gaps(arr).forEach(g => {
        const startPx = pxForSec(g.start);
        const endPx = pxForSec(g.end);
        if (endPx > startPx) {
        const existingOverlay = Array.from(col.querySelectorAll('.no-shift-overlay'))
          .find(overlay => {
            return (
              parseInt(overlay.style.top, 10) === startPx &&
              parseInt(overlay.style.height, 10) === (endPx - startPx)
            );
          });

          if (!existingOverlay) {
            const o = document.createElement('div');
            o.classList.add('no-shift-overlay');
            o.style.top = startPx + 'px';
            o.style.height = (endPx - startPx) + 'px';
            o.addEventListener('mousedown', e => {
              e.stopPropagation();
            });

            o.addEventListener('click', e => {
              e.stopPropagation();
              e.preventDefault();

              Swal.fire({
                title: 'No Working Shift',
                text: 'There is no working shift in this time block. Go to HR Management to create one?',
                icon: 'info',
                showCancelButton: true,
                confirmButtonText: 'Yes, open HR',
                cancelButtonText: 'No'
              }).then(res => {
                if (res.isConfirmed) window.location.href = '/admin/hr_management/shifts';
              });
            });

            o.style.left = '0px';
            o.style.width = '100%';

            col.appendChild(o);
          }
        }
      });
    });
  }, { once: true });
</script>
