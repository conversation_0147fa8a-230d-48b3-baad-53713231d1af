<%= render 'admin/calendar_bookings/staff_calendar/calendar_navigation' %>

<div class="week-calendar-container">
  <% if Time.now.in_time_zone('London').hour >= 8 && Time.now.in_time_zone('London').hour <= 18 && @date&.cweek == Date.today.cweek %>
    <div class="week-calendar-current-time-horizonal-line absolute bg-[#FBE099] w-full h-[2px] z-[999]"
         style="top: <%= horizontal_time_line_height_px(@timeline) + 174 %>px;"></div>
  <% end %>

  <div class="flex-none min-w-[80px] text-right mr-3 mt-[196px] bg-[#f5f5f6]" style="z-index: 6;">
    <%= render 'admin/calendar_bookings/staff_calendar/timeline' %>
  </div>

  <div class="week-calendar">
    <div class="week-calendar-content">
      <% (@date.beginning_of_week(:monday)..(@date.beginning_of_week(:monday) + 6.days)).each_with_index do |day, index| %>
        <% background_class = "bg-day-#{(index % 2) + 1}" %>
        <div class="week-calendar-day-column <%= background_class %>" data-date="<%= day.to_s %>">
          <%= link_to day.strftime("%A %d %B %Y"),
                      staff_calendar_admin_calendar_bookings_path(date: day, mode: :day, hidden_dentists: params[:hidden_dentists]),
                      class: 'week-calendar-day-header text-[14px] font-medium text-gray-800 py-[7px]'%>

          <div class="week-calendar-dentist-columns">
            <% @filtered_practitioners.each do |dentist| %>

              <% dentist_holidays_for_day = @holidays.select do |holiday|
                holiday.user_id == dentist.id && (holiday.start_date.to_date..holiday.end_date.to_date).cover?(day)
              end %>

              <% dentist_has_bookings_for_day = @bookings.any? do |booking|
                booking.practitioner_id == dentist.id && booking.start_time.to_date == day
              end %>

              <!-- NOTE: only show practitioners which have a shift, holiday or a booking on that day -->
              <% if @shifts_by_practitioner_and_day.dig(dentist.id, day).present? || dentist_holidays_for_day.present? || dentist_has_bookings_for_day %>
                <div class="week-calendar-dentist-column"
                  data-practitioner-id="<%= dentist.id %>"
                  data-schedules="<%= ((@shifts_by_practitioner_and_day[dentist.id] || {})[day] || []).to_json %>"
                >
                  <%= render 'admin/calendar_bookings/staff_calendar/column_header', dentist: dentist, date: day %>

                  <!-- NOTE: render calendar booking cards -->
                  <% @bookings.each do |booking| %>
                    <% if booking.practitioner_id == dentist.id && booking.start_time.to_date == day %>
                      <%= render 'admin/calendar_bookings/staff_calendar/calendar_booking_card',
                        booking: booking,
                        card_klass: 'week-calendar-booking',
                        card_id: "week-calendar-booking-#{booking.id}",
                        card_top: calendar_item_top_px(booking.start_time_of_day, @timeline) + 158,
                        card_height: calendar_item_height_px(booking.duration_in_minutes, @timeline) - 2,
                        card_bg_color: booking.booking_type_event? ? booking.event_color : @calendar_reserved_block_types.find { _1.treatment_id == booking.treatment_id }&.color
                      %>
                    <% end %>
                  <% end %>

                  <!-- NOTE: render reserved block cards -->
                  <% @reserved_blocks.each do |reserved_block| %>
                    <% if reserved_block.practitioner_id == dentist.id && reserved_block.start_time.to_date == day %>
                      <%= render 'admin/calendar_bookings/staff_calendar/reserved_block_card',
                        reserved_block: reserved_block,
                        timeline: @timeline,
                        top: calendar_item_top_px(reserved_block.start_time.seconds_since_midnight, @timeline) + 164,
                        height: calendar_item_height_px(reserved_block.duration_in_minutes, @timeline) - 2
                      %>
                    <% end %>
                  <% end %>

                  <!-- NOTE: render holiday cards -->
                  <% dentist_holidays_for_day.each do |holiday| %>
                    <% if holiday.custom_hours? %>
                      <% duration_in_minutes = (Time.parse(holiday.event_end) - Time.parse(holiday.event_start)) / 60 %>
                      <% card_top = calendar_item_top_px(Time.parse(holiday.event_start).seconds_since_midnight, @timeline) + 164 %>
                      <% card_height = calendar_item_height_px(duration_in_minutes, @timeline) - 2 %>
                    <% end %>

                    <%= render 'admin/calendar_bookings/staff_calendar/holiday_card',
                      holiday: holiday,
                      timeline: @timeline,
                      top: card_top,
                      height: card_height
                    %>
                  <% end %>
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<%= render 'admin/calendar_bookings/staff_calendar/calendar_day_note_modal' %>
<%= render 'admin/calendar_bookings/staff_calendar/payment_request_modal' %>

<!-- NOTE: horizontal navigation, dragging, and clipboard features, grey rectangles -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    const calendarContainer = document.querySelector('.week-calendar');

    if (!calendarContainer) return;

    const calendar = document.querySelector('.week-calendar-content');
    const leftButton = document.querySelector('.calendar-nav-button-left');
    const rightButton = document.querySelector('.calendar-nav-button-right');
    let timeline = <%= raw(@timeline.to_json) %>;

    function getColumnWidth() {
      const column = calendar.querySelector('.week-calendar-day-column');
      const gap = 12;
      return column ? column.offsetWidth + gap : 0;
    }

    function scrollCalendar(direction) {
      const columnWidth = getColumnWidth();
      const scrollBy = columnWidth > 800 ? 800 : columnWidth;

      calendarContainer.scrollBy({
        left: scrollBy * direction,
        behavior: 'smooth'
      });
    }

    leftButton.addEventListener('click', function() {
      scrollCalendar(-1);
    });
    rightButton.addEventListener('click', function() {
      scrollCalendar(1);
    });

    function updateButtonStates() {
      const isScrollable = calendarContainer.scrollWidth > calendarContainer.clientWidth;

      leftButton.classList.toggle('disabled', !isScrollable);
      rightButton.classList.toggle('disabled', !isScrollable);

      if (isScrollable) {
        calendar.classList.add('has-arrows');
      } else {
        calendar.classList.remove('has-arrows');
      }
    }

    calendarContainer.addEventListener('scroll', updateButtonStates);
    updateButtonStates();

    let isDragging = false;
    let startY = null;
    let currentDentistColumn = null;
    let dragElement = null;
    let startStepIndex = null;
    let endStepIndex = null;
    let currentNonBookableBlocks = [];

    const stepSize = <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>;

    function calculateSteps(startY, currentY) {
      const delta = (currentY - startY) / stepSize;
      if (delta >= 0) {
        return Math.floor(delta);
      } else {
        return Math.ceil(delta);
      }
    }

    function findNearestTimeLabel(startY) {
      return Math.round(startY / stepSize) * stepSize;
    }

    function findStepIndexByYPosition(yPosition) {
      const relativeY = yPosition / stepSize;
      return Math.round(relativeY);
    }

    function isInClipboardMode() {
      return document.querySelector('.clipboard-preview') !== null;
    }

    function isInStatusOverlayMode() {
      return $('.booking-status-label-modal-overlay:visible').length > 0
    }

    function createDragElement(column, startY) {
      const dragEl = document.createElement('div');
      dragEl.classList.add('dragging-booking');
      dragEl.style.position = 'absolute';
      dragEl.style.left = '6px';
      dragEl.style.top = `${startY}px`;
      dragEl.style.width = 'calc(100% - 12px)';
      dragEl.style.height = `${stepSize}px`;
      dragEl.style.userSelect = 'none';
      dragEl.style.padding = '10px';
      dragEl.style.background = 'rgba(137, 153, 176, 0.20)';
      dragEl.style.borderRadius = '14px';
      dragEl.style.overflow = 'hidden';
      dragEl.style.border = '2px #2A4558 dotted';
      dragEl.style.display = 'inline-flex';
      dragEl.style.flexDirection = 'column';
      dragEl.style.justifyContent = 'center';
      dragEl.style.alignItems = 'center';
      dragEl.style.gap = '4px';
      dragEl.style.zIndex = '3';

      const firstText = document.createElement('div');
      firstText.classList.add('first-text');
      firstText.style.color = '#2A4558';
      firstText.style.fontSize = '14px';
      firstText.style.fontFamily = 'Poppins';
      firstText.style.fontWeight = '400';
      firstText.style.wordWrap = 'break-word';

      const secondText = document.createElement('div');
      secondText.classList.add('second-text');
      secondText.style.color = '#2A4558';
      secondText.style.fontSize = '12px';
      secondText.style.fontFamily = 'Poppins';
      secondText.style.fontWeight = '700';
      secondText.style.wordWrap = 'break-word';

      dragEl.appendChild(firstText);
      dragEl.appendChild(secondText);

      column.appendChild(dragEl);
      return dragEl;
    }

    function removeExistingDragElements() {
      const existingDragElements = document.querySelectorAll('.dragging-booking');
      existingDragElements.forEach(el => el.remove());
    }

    const subHeader = $('#calendar-sub-header');
    // NOTE: subheader is not present in settings page
    if (subHeader.length > 0) {
      // Event listener for creating new bookings by dragging over empty space
      var currentDate;
      calendar.addEventListener('mousedown', function(event) {
        if (isInClipboardMode() || isInStatusOverlayMode()) {
          return;
        }

        const dentistColumn = event.target.closest('.week-calendar-dentist-column');
        if (dentistColumn && !event.target.closest('.week-calendar-booking')) {
          const dayColumn = dentistColumn.closest('.week-calendar-day-column');
          const date = dayColumn.dataset.date;
          currentDate = date;

          let clickY = event.clientY - dentistColumn.getBoundingClientRect().top;
          if (clickY < 162) {
            return;
          }
          isDragging = true;
          startY = clickY - 176;
          startY = findNearestTimeLabel(startY);
          startStepIndex = findStepIndexByYPosition(startY);
          endStepIndex = startStepIndex;

          currentDentistColumn = dentistColumn;
          removeExistingDragElements();
          dragElement = createDragElement(currentDentistColumn, startY + 162);
          currentNonBookableBlocks = Array.from(currentDentistColumn.querySelectorAll('.week-calendar-booking, .no-shift-overlay')).map(booking => {
            const bookingRect = booking.getBoundingClientRect();
            const columnRect = currentDentistColumn.getBoundingClientRect();
            const top = bookingRect.top - columnRect.top - 160;
            const height = booking.offsetHeight;
            const snappedTop = Math.round(top / stepSize) * stepSize;
            const snappedBottom = Math.round((top + height) / stepSize) * stepSize;
            return { top: snappedTop, bottom: snappedBottom };
          });
        }
      });

      document.addEventListener('mousemove', function(event) {
        if (isInClipboardMode() || isInStatusOverlayMode()) {
          return;
        }

        if (isDragging && dragElement) {
          // NOTE: auto-scrolling when dragging near the edge of the viewport
          const scrollMargin = 50;
          const maxScrollSpeed = 20;
          const cursorY = event.clientY;
          const cursorX = event.clientX;
          const viewportHeight = window.innerHeight;
          const viewportWidth = window.innerWidth;

          if (cursorY < scrollMargin) {
            window.scrollBy(0, -maxScrollSpeed);
          } else if (cursorY > viewportHeight - scrollMargin) {
            window.scrollBy(0, maxScrollSpeed);
          }

          let currentY = event.clientY - currentDentistColumn.getBoundingClientRect().top - 160;
          if (currentY < 0) {
            currentY = 0;
          }
          const draggingDown = currentY >= startY;
          let dragTop = Math.min(startY, currentY);
          let dragBottom = Math.max(startY, currentY);

          dragTop = Math.round(dragTop / stepSize) * stepSize;
          dragBottom = Math.round(dragBottom / stepSize) * stepSize;

          for (let block of currentNonBookableBlocks) {
            if (dragBottom > block.top && dragTop < block.bottom) {
              if (draggingDown) {
                dragBottom = block.top;
                currentY = startY + Math.floor((dragBottom - startY) / stepSize) * stepSize;
              } else {
                dragTop = block.bottom;
                currentY = startY + Math.ceil((dragTop - startY) / stepSize) * stepSize;
              }
              if (dragBottom <= dragTop) {
                isDragging = false;
                dragElement.remove();
                dragElement = null;
                return;
              }
              break;
            }
          }
          const steps = calculateSteps(startY, currentY);
          let newTop, newHeight;
          if (steps >= 0) {
            newTop = startY + 162;
            newHeight = (steps + 1) * stepSize;
          } else {
            newTop = startY + steps * stepSize + 162;
            newHeight = Math.abs(steps) * stepSize;
          }
          dragElement.style.top = `${newTop}px`;
          dragElement.style.height = `${newHeight}px`;
          endStepIndex = startStepIndex + steps;

          let startTimeStr, endTimeStr;
          if (startStepIndex <= endStepIndex) {
            startTimeStr = timeline[startStepIndex].start_time;
            endTimeStr = timeline[endStepIndex].end_time;
          } else {
            startTimeStr = timeline[endStepIndex].start_time;
            endTimeStr = timeline[startStepIndex].start_time;
          }

          const startTime = new Date(startTimeStr);
          const endTime = new Date(endTimeStr);

          const firstText = dragElement.querySelector('.first-text');
          const secondText = dragElement.querySelector('.second-text');

          if (firstText && secondText && startTime && endTime) {
            firstText.innerText = `${formatTime(startTime)} - ${formatTime(endTime)}`;
            const durationMinutes = (endTime - startTime) / 60000;
            secondText.innerText = formatDuration(durationMinutes);
          }
        }
      });

      document.addEventListener('mouseup', function(event) {
        if (isInClipboardMode() || isInStatusOverlayMode()) {
          return;
        }

        if (isDragging && dragElement) {
          isDragging = false;
          dragElement.style.pointerEvents = 'none';

          if (startStepIndex < 0) startStepIndex = 0;
          if (endStepIndex < 0) endStepIndex = 0;
          if (startStepIndex >= timeline.length) startStepIndex = timeline.length - 1;
          if (endStepIndex >= timeline.length) endStepIndex = timeline.length - 1;

          let startTimeStr, endTimeStr;
          if (startStepIndex <= endStepIndex) {
            startTimeStr = timeline[startStepIndex].start_time;
            endTimeStr = timeline[endStepIndex].end_time;
          } else {
            startTimeStr = timeline[endStepIndex].start_time;
            endTimeStr = timeline[startStepIndex].start_time;
          }

          const startTime = new Date(startTimeStr);
          const endTime = new Date(endTimeStr);

          const firstText = dragElement.querySelector('.first-text');
          const secondText = dragElement.querySelector('.second-text');

          if (firstText && secondText && startTime && endTime) {
            firstText.innerText = `${formatTime(startTime)} - ${formatTime(endTime)}`;
            const durationMinutes = (endTime - startTime) / 60000;
            secondText.innerText = formatDuration(durationMinutes);
          }

          if (startTime && endTime) {
            console.log(`New booking from ${startTime} to ${endTime}`);

            const mode = '<%= @mode %>';
            const dateParam = '<%= @date %>';
            const hiddenDentists = JSON.stringify('<%= raw(params[:hidden_dentists].presence || [].to_json) %>');

            $.ajax({
              url: '/admin/calendar_bookings/drag_offcanvas',
              method: 'GET',
              data: {
                practitioner_id: currentDentistColumn.dataset.practitionerId,
                start_time: startTimeStr,
                end_time: endTimeStr,
                mode: mode,
                date: currentDate,
                hidden_dentists: hiddenDentists
              },
              success: function(response) {}
            });
          }
          dragElement = null;
          currentDentistColumn = null;
        }
      });
    }

    // Variables for dragging existing bookings
    let bookingElement = null;
    let currentBookingId = null;

    const timeLineElement = document.querySelector('.week-calendar-current-time-horizonal-line');
    if (timeLineElement) {
      const rect = timeLineElement.getBoundingClientRect();
      const elementTop = rect.top + window.pageYOffset;
      const windowHeight = window.innerHeight;
      const scrollToPosition = elementTop - (windowHeight / 2);

      window.scrollTo({
        top: scrollToPosition,
        behavior: 'smooth'
      });
    }

    function addRectanglesToColumn(column, startHeight, rectangleHeight, rectangleSpacing) {
      const existingRectangles = column.querySelectorAll('.semi-transparent-rectangle');
      existingRectangles.forEach(rect => rect.remove());

      const columnHeight = column.offsetHeight;
      const availableHeight = columnHeight - startHeight;
      const totalCycleHeight = rectangleHeight + rectangleSpacing;
      const numberOfRectangles = Math.ceil(availableHeight / totalCycleHeight);

      for (let i = 0; i < numberOfRectangles; i++) {
        const rect = document.createElement('div');
        rect.classList.add('semi-transparent-rectangle');
        rect.style.position = 'absolute';
        rect.style.top = (startHeight + i * totalCycleHeight) + 'px';
        rect.style.left = '0';
        rect.style.width = '100%';
        rect.style.height = rectangleHeight + 'px';
        rect.style.background = 'rgba(217, 217, 217, 0.20)';
        rect.style.pointerEvents = 'none';
        column.appendChild(rect);
      }
    }

    const weekColumns = document.querySelectorAll('.week-calendar-day-column');

    weekColumns.forEach(column => {
      const startHeight = 196;
      const rectangleHeight = <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT * 3 %>;
      const rectangleSpacing = <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT * 3 %>;
      addRectanglesToColumn(column, startHeight, rectangleHeight, rectangleSpacing);
    });
  });
</script>

<!-- Clipboard feature -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    let isClipboardMode = false;
    let clipboardBookingData = null;
    let clipboardPreviewElement = null;
    let currentPractitionerId = null;
    let currentHighlightElement = null;
    const stepSize = <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>;
    const timeline = <%= raw(@timeline.to_json) %>;

    // Minimal persistence - just save and restore clipboard state
    function saveClipboardState() {
      if (isClipboardMode && clipboardBookingData) {
        sessionStorage.setItem('calendar_clipboard_state', JSON.stringify({
          isClipboardMode: true,
          clipboardBookingData: clipboardBookingData,
          currentPractitionerId: currentPractitionerId
        }));
      }
    }

    function restoreClipboardState() {
      try {
        const savedState = sessionStorage.getItem('calendar_clipboard_state');
        if (savedState) {
          const state = JSON.parse(savedState);
          isClipboardMode = state.isClipboardMode;
          clipboardBookingData = state.clipboardBookingData;
          currentPractitionerId = state.currentPractitionerId;

          if (isClipboardMode && clipboardBookingData) {
            clipboardPreviewElement = createClipboardPreview(clipboardBookingData);
            document.body.style.cursor = 'grabbing';

            const clearClipboard = document.querySelector('.clear-clipboard');
            if (clearClipboard) {
              clearClipboard.style.display = 'flex';
            }

            const allDentistColumns = document.querySelectorAll('.week-calendar-dentist-column');
            allDentistColumns.forEach(column => {
              if (column.dataset.practitionerId !== currentPractitionerId) {
                column.style.filter = 'blur(4px)';
              }
            });
          }
        }
      } catch (error) {
        console.warn('Failed to restore clipboard state:', error);
        sessionStorage.removeItem('calendar_clipboard_state');
      }
    }

    function clearClipboardState() {
      sessionStorage.removeItem('calendar_clipboard_state');
    }

    function createClipboardPreview(bookingData) {
      const preview = document.createElement('div');
      preview.className = 'clipboard-preview';
      const nameDiv = document.createElement('div');
      nameDiv.className = 'name';
      nameDiv.innerText = bookingData.patientName;
      const timeDiv = document.createElement('div');
      timeDiv.className = 'time';
      timeDiv.innerText = bookingData.timeRange;
      const durationDiv = document.createElement('div');
      durationDiv.className = 'duration';
      durationDiv.innerText = bookingData.durationText;
      preview.appendChild(nameDiv);
      preview.appendChild(timeDiv);
      preview.appendChild(durationDiv);
      document.body.appendChild(preview);
      return preview;
    }

    function removeHighlight() {
      if (currentHighlightElement) {
        currentHighlightElement.remove();
        currentHighlightElement = null;
      }
      if (clipboardPreviewElement) {
        clipboardPreviewElement.classList.remove('valid');
        clipboardPreviewElement.style.opacity = '0.5';
      }
    }

    function checkOverlap(column, startIndex, endIndex) {
      const blocks = Array.from(column.querySelectorAll('.week-calendar-booking, .no-shift-overlay')).map(block => {
        const top = parseInt(block.style.top) - 164;
        const height = parseInt(block.style.height) + 2;
        const blockStartIndex = Math.round(top / stepSize);
        const blockEndIndex = Math.round((top + height) / stepSize);
        return { start: blockStartIndex, end: blockEndIndex };
      });
      return blocks.some(block => !(endIndex <= block.start || startIndex >= block.end));
    }

    restoreClipboardState();

    const clearClipboard = document.querySelector('.clear-clipboard');
    clearClipboard.addEventListener('click', function() {
      isClipboardMode = false;
      clipboardBookingData = null;
      if (clipboardPreviewElement) {
        clipboardPreviewElement.remove();
        clipboardPreviewElement = null;
      }
      document.body.style.cursor = 'default';
      const allDentistColumns = document.querySelectorAll('.week-calendar-dentist-column');
      allDentistColumns.forEach(column => {
        column.style.filter = '';
      });
      removeHighlight();
      clearClipboard.style.display = 'none';
      clearClipboardState();
    });

    const clipboardIcons = document.querySelectorAll('.calendar-booking-clipboard-button');
    clipboardIcons.forEach(icon => {
      icon.addEventListener('click', function(event) {
        event.preventDefault();

        if (isClipboardMode) {
          return;
        }

        isClipboardMode = true;
        clearClipboard.style.display = 'flex';

        const bookingId = $(this).closest('.booking-actions-menu').data('bookingId')
        const bookingElement = document.querySelector(
          `.week-calendar-booking[data-booking-id='${bookingId}']`
        )

        const practitionerId = bookingElement.closest('.week-calendar-dentist-column').dataset.practitionerId;
        const patientNameElement = bookingElement.querySelector('.calendar-booking-patient-name');
        const timeRangeElement = bookingElement.querySelector('.calendar-booking-times');
        const durationTextElement = bookingElement.querySelector('.calendar-booking-treatment');

        const patientName = patientNameElement ? patientNameElement.innerText.trim() : '';
        const timeRange = timeRangeElement ? timeRangeElement.innerText.trim() : '';
        const durationText = durationTextElement ? durationTextElement.innerText.trim() : '';

        const duration = parseInt(bookingElement.style.height) + 2;
        clipboardBookingData = {
          bookingId,
          practitionerId,
          patientName,
          timeRange,
          durationText,
          duration
        };
        currentPractitionerId = practitionerId;
        clipboardPreviewElement = createClipboardPreview(clipboardBookingData);
        document.body.style.cursor = 'grabbing';
        const allDentistColumns = document.querySelectorAll('.week-calendar-dentist-column');
        allDentistColumns.forEach(column => {
          if (column.dataset.practitionerId !== currentPractitionerId) {
            column.style.filter = 'blur(4px)';
          }
        });

        // Save clipboard state
        saveClipboardState();
      });
    });

    document.addEventListener('mousemove', function(event) {
      if (isClipboardMode && clipboardPreviewElement) {
        const scrollY = window.scrollY || window.pageYOffset;
        const scrollX = window.scrollX || window.pageXOffset;

        clipboardPreviewElement.style.left = event.clientX + 10 + scrollX + 'px';
        clipboardPreviewElement.style.top = event.clientY + 10 + scrollY + 'px';

        const dentistColumn = event.target.closest('.week-calendar-dentist-column');
        if (dentistColumn && dentistColumn.dataset.practitionerId === currentPractitionerId) {
          const columnRect = dentistColumn.getBoundingClientRect();
          let clickY = event.clientY - columnRect.top - 160;
          if (clickY < 0) clickY = 0;
          const stepIndex = Math.floor(clickY / stepSize);
          const durationSteps = Math.round(clipboardBookingData.duration / stepSize);
          const endIndex = stepIndex + durationSteps;
          if (stepIndex >= 0 && endIndex <= timeline.length) {
            if (!checkOverlap(dentistColumn, stepIndex, endIndex)) {
              if (!currentHighlightElement) {
                currentHighlightElement = document.createElement('div');
                currentHighlightElement.className = 'clipboard-highlight';
                dentistColumn.appendChild(currentHighlightElement);
              }
              currentHighlightElement.style.top = (stepIndex * stepSize + 160) + 'px';
              currentHighlightElement.style.height = clipboardBookingData.duration + 'px';
              clipboardPreviewElement.classList.add('valid');
              clipboardPreviewElement.style.opacity = '1';
            } else {
              removeHighlight();
            }
          } else {
            removeHighlight();
          }
        } else {
          removeHighlight();
        }
      }
    });

    document.addEventListener('click', function(event) {
      if (isClipboardMode) {
        const dentistColumn = event.target.closest('.week-calendar-dentist-column');
        if (dentistColumn && dentistColumn.dataset.practitionerId === currentPractitionerId && currentHighlightElement) {
          const dayColumn = dentistColumn.closest('.week-calendar-day-column');
          const date = dayColumn.dataset.date;
          const columnRect = dentistColumn.getBoundingClientRect();
          let clickY = event.clientY - columnRect.top - 160;
          if (clickY < 0) clickY = 0;
          const stepIndex = Math.floor(clickY / stepSize);
          const durationSteps = Math.round(clipboardBookingData.duration / stepSize);
          const endIndex = stepIndex + durationSteps;
          if (stepIndex >= 0 && endIndex <= timeline.length && !checkOverlap(dentistColumn, stepIndex, endIndex)) {
            Swal.fire({
              title: 'Move Appointment?',
              text: 'Do you want to move the appointment in the clipboard to this time slot?',
              icon: 'warning',
              showCancelButton: true,
              confirmButtonText: 'Yes, move it!',
              cancelButtonText: 'No, cancel'
            }).then((result) => {
              if (result.isConfirmed) {
                isClipboardMode = false;
                const bookingId = clipboardBookingData.bookingId;
                clipboardBookingData = null;
                if (clipboardPreviewElement) {
                  clipboardPreviewElement.remove();
                  clipboardPreviewElement = null;
                }
                document.body.style.cursor = 'default';
                const allDentistColumns = document.querySelectorAll('.week-calendar-dentist-column');
                allDentistColumns.forEach(column => {
                  column.style.filter = '';
                });
                clearClipboard.style.display = 'none';
                removeHighlight();
                clearClipboardState();

                $.ajax({
                  url: '/admin/calendar_bookings/' + bookingId + '/move',
                  method: 'POST',
                  data: {
                    calendar_booking: {
                      start_time: timeline[stepIndex].start_time,
                      end_time: timeline[endIndex].start_time,
                      date: date,
                    }
                  },
                  success: function(response) {
                    location.reload();
                  },
                  error: function(xhr, status, error) {
                    var text;
                    if (error === 'Forbidden') {
                      text = 'You do not have permission to move this appointment.';
                    } else {
                      text = 'An error occurred while moving the appointment. Please try again.';
                    }

                    Swal.fire({
                      icon: 'error',
                      title: 'Error',
                      text: text
                    });
                  }
                });
              }
            });
          }
        }
      }
    });

    document.addEventListener('keydown', function(event) {
      if (isClipboardMode && event.key === 'Escape') {
        isClipboardMode = false;
        clipboardBookingData = null;
        if (clipboardPreviewElement) {
          clipboardPreviewElement.remove();
          clipboardPreviewElement = null;
        }
        document.body.style.cursor = 'default';
        const allDentistColumns = document.querySelectorAll('.week-calendar-dentist-column');
        allDentistColumns.forEach(column => {
          column.style.filter = '';
        });
        removeHighlight();

        const clearClipboard = document.querySelector('.clear-clipboard');
        clearClipboard.style.display = 'none';
        clearClipboardState();
      }
    });
  });
</script>

<!-- Timenav feature -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    $('.timenav-bar-week').click(function(event) {
      const timenavBar = event.currentTarget;
      const practitionerId = timenavBar.dataset.practitionerId;
      const rect = timenavBar.getBoundingClientRect();

      const clickX = event.clientX - rect.left;
      const dayIndex = Math.floor((clickX / rect.width) * 7);
      const totalShiftMinutes = 14 * 60;
      const timePercentage = (clickX % (rect.width / 7)) / (rect.width / 7);
      const minutesIntoShift = totalShiftMinutes * timePercentage;
      const time = new Date();
      time.setHours(8, 0, 0, 0);
      time.setMinutes(time.getMinutes() + minutesIntoShift);

      scrollToDentistIndexAndTime(practitionerId, dayIndex, time);
    });

    function scrollToDentistIndexAndTime(practitionerId, dayIndex, time) {
      const calendarContainer      = document.querySelector('.week-calendar');
      if (!calendarContainer) return;

      const weekCalendarContent    = document.querySelector('.week-calendar-content');
      const dayColumns             = weekCalendarContent.querySelectorAll('.week-calendar-day-column');
      if (!dayColumns[dayIndex]) return;

      const targetDentistColumn = [...dayColumns[dayIndex]
        .querySelectorAll('.week-calendar-dentist-column')]
        .find(c => c.dataset.practitionerId === practitionerId);
      if (!targetDentistColumn) return;

      const dentistColumnOffset = targetDentistColumn.getBoundingClientRect().left
                                - weekCalendarContent.getBoundingClientRect().left;
      calendarContainer.scrollTo({ left: dentistColumnOffset - 800, behavior: 'smooth' });

      const timelineStartTime  = new Date();
      timelineStartTime.setHours(8, 0, 0, 0);

      const minutesFromStart   = (time - timelineStartTime) / 60000;
      const stepSize           = <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>;
      const verticalOffset     = (minutesFromStart / 5) * stepSize;

      window.scrollTo({ top: verticalOffset, behavior: 'smooth' });
    }
  }, { once: true });
</script>

<!-- NOTE: no shift overlay feature -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    const container = document.querySelector('.week-calendar-container');
    if (!container) return;

    const SHIFT_START = <%= @practice_opening_time.to_i - @practice_opening_time.beginning_of_day.to_i %>;
    const SHIFT_END = <%= @practice_closing_time.to_i - @practice_opening_time.beginning_of_day.to_i %>;

    const stepSize = <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>;

    function pxForSec(sec) {
      if (sec < SHIFT_START) sec = SHIFT_START;
      if (sec > SHIFT_END) sec = SHIFT_END;

      return ((sec - SHIFT_START) / 300) * stepSize + 162;
    }

    function gaps(schedules) {
      const r = [];
      let c = SHIFT_START;
      schedules.forEach(s => {
        const st = Math.max(s.start, SHIFT_START);
        const en = Math.min(s.end, SHIFT_END);
        if (st > c) r.push({ start: c, end: st });
        if (en > c) c = en;
      });
      if (c < SHIFT_END) r.push({ start: c, end: SHIFT_END });
      return r;
    }

    document.querySelectorAll('.week-calendar-day-column .week-calendar-dentist-column')
      .forEach(col => {
        let arr;
        try {
          arr = JSON.parse(col.dataset.schedules || '[]');
        } catch(e) {
          arr = [];
        }
        arr.sort((a,b) => a.start - b.start);

        const dayGaps = gaps(arr);
        dayGaps.forEach(g => {
          const startPx = pxForSec(g.start);
          const endPx = pxForSec(g.end);
          if (endPx > startPx) {
            const existingOverlay = Array.from(col.querySelectorAll('.no-shift-overlay'))
              .find(overlay => {
                return (
                  parseInt(overlay.style.top, 10) === startPx &&
                  parseInt(overlay.style.height, 10) === (endPx - startPx)
                );
              });

            if (!existingOverlay) {
              const overlay = document.createElement('div');
              overlay.classList.add('no-shift-overlay');
              overlay.style.top = startPx + 'px';
              overlay.style.height = (endPx - startPx) + 'px';
              overlay.addEventListener('mousedown', e => {
                e.stopPropagation();
              });
              overlay.addEventListener('click', e => {
                e.stopPropagation();
                e.preventDefault();
                Swal.fire({
                  title: 'No Working Shift',
                  text: 'There is no working shift in this time block. Go to HR Management to create one?',
                  icon: 'info',
                  showCancelButton: true,
                  confirmButtonText: 'Yes, open HR',
                  cancelButtonText: 'No'
                }).then(res => {
                  if (res.isConfirmed) {
                    window.location.href = '/admin/hr_management/shifts';
                  }
                });
              });

              col.appendChild(overlay);
            }
          }
        });
    });
  }, { once: true });
</script>

<!-- NOTE: fixed headers feature -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    const dayColorMapping = {
      'bg-day-1': 'rgba(254, 226, 226, 0.75)',
      'bg-day-2': 'rgba(243, 232, 255, 0.75)',
    };

    const dayColumns = document.querySelectorAll('.week-calendar-day-column');
    const fixedDayHeaders = [];

    function createFixedDayHeaders() {
      dayColumns.forEach(column => {
        const originalHeader = column.querySelector('.week-calendar-day-header');
        if (!originalHeader) return;

        const fixedHeader = originalHeader.cloneNode(true);
        fixedHeader.style.position = 'fixed';
        fixedHeader.style.top = '144px';
        fixedHeader.style.zIndex = '998';
        fixedHeader.style.margin = '0';
        fixedHeader.style.padding = '8px 0';
        fixedHeader.style.borderTopLeftRadius = '16px';
        fixedHeader.style.borderTopRightRadius = '16px';
        fixedHeader.style.borderBottomLeftRadius = '0';
        fixedHeader.style.borderBottomRightRadius = '0';
        fixedHeader.style.width = `${column.offsetWidth}px`;
        fixedHeader.style.backdropFilter = 'blur(6px)';
        fixedHeader.style.webkitBackdropFilter = 'blur(6px)';

        const dayClass = [...column.classList].find(cls => cls.startsWith('bg-day-'));
        if (dayClass && dayColorMapping[dayClass]) {
          fixedHeader.style.backgroundColor = dayColorMapping[dayClass];
        } else {
          fixedHeader.style.backgroundColor = 'white';
        }

        updateDayHeaderPosition(column, fixedHeader);
        document.body.appendChild(fixedHeader);
        fixedDayHeaders.push({ column, originalHeader, fixedHeader });
      });
    }

    function updateDayHeaderPosition(column, fixedHeader) {
      const rect = column.getBoundingClientRect();
      if (rect.right < 0 || rect.left > window.innerWidth) {
        fixedHeader.style.display = 'none';
      } else {
        fixedHeader.style.display = 'block';
        fixedHeader.style.left = `${rect.left}px`;
        fixedHeader.style.width = `${rect.width}px`;
      }
    }

    function updateAllDayHeaders() {
      fixedDayHeaders.forEach(({ column, fixedHeader, originalHeader }) => {
        updateDayHeaderPosition(column, fixedHeader);
      });

      if (window.scrollY >= 240) {
        fixedDayHeaders.forEach(({ fixedHeader, originalHeader }) => {
          fixedHeader.style.visibility = 'visible';
          if (originalHeader) originalHeader.style.visibility = 'hidden';
        });
      } else {
        fixedDayHeaders.forEach(({ fixedHeader, originalHeader }) => {
          fixedHeader.style.visibility = 'hidden';
          if (originalHeader) originalHeader.style.visibility = 'visible';
        });
      }
    }

    const dentistColumns = document.querySelectorAll('.week-calendar-dentist-column');
    const fixedDentistHeaders = [];

    function createFixedDentistHeaders() {
      dentistColumns.forEach(column => {
        const originalHeader = column.querySelector('.calendar-dentist-header');
        if (!originalHeader) return;

        const fixedHeader = originalHeader.cloneNode(true);
        fixedHeader.style.position = 'fixed';
        fixedHeader.style.top = '180px';
        fixedHeader.style.zIndex = '998';
        fixedHeader.style.margin = '0';
        fixedHeader.style.backgroundColor = 'rgba(255, 255, 255, 0.75)';
        fixedHeader.style.backdropFilter = 'blur(6px)';
        fixedHeader.style.webkitBackdropFilter = 'blur(6px)';

        updateDentistHeaderPosition(column, fixedHeader);
        document.body.appendChild(fixedHeader);
        fixedDentistHeaders.push({ column, originalHeader, fixedHeader });

        applyCalendarDayNoteHandlers();
      });
    }

    function updateDentistHeaderPosition(column, fixedHeader) {
      const rect = column.getBoundingClientRect();
      if (rect.right < 0 || rect.left > window.innerWidth) {
        fixedHeader.style.display = 'none';
      } else {
        fixedHeader.style.display = 'block';
        fixedHeader.style.left = `${rect.left}px`;
        fixedHeader.style.width = `${rect.width}px`;
      }
    }

    function updateAllDentistHeaders() {
      fixedDentistHeaders.forEach(({ column, fixedHeader, originalHeader }) => {
        updateDentistHeaderPosition(column, fixedHeader);
      });

      if (window.scrollY >= 240) {
        fixedDentistHeaders.forEach(({ fixedHeader, originalHeader }) => {
          fixedHeader.style.visibility = 'visible';
          if (originalHeader) originalHeader.style.visibility = 'hidden';
        });
      } else {
        fixedDentistHeaders.forEach(({ fixedHeader, originalHeader }) => {
          fixedHeader.style.visibility = 'hidden';
          if (originalHeader) originalHeader.style.visibility = 'visible';
        });
      }
    }

    function updateAllHeaders() {
      updateAllDayHeaders();
      updateAllDentistHeaders();
    }

    createFixedDayHeaders();
    createFixedDentistHeaders();
    updateAllHeaders();

    const weekCalendar = document.querySelector('.week-calendar');
    if (weekCalendar) {
      weekCalendar.addEventListener('scroll', updateAllHeaders);
    }
    window.addEventListener('scroll', updateAllHeaders);
    window.addEventListener('resize', updateAllHeaders);
  }, { once: true });
</script>
