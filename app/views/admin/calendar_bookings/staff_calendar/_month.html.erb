<div class="mx-4 mt-4 rounded-xl overflow-hidden shadow-lg border border-gray-100">
   <div class="flex flex-col overflow-y-auto bg-white">
      <div class="sticky top-0 z-20 bg-white border-b border-gray-100 py-4 px-6 shadow-sm">
         <div class="flex items-center justify-between">
            <h2 class="text-xl font-medium text-gray-900"><%= @date.strftime("%B %Y") %></h2>
            <div class="relative w-[330px] flex items-center">
              <i class="far fa-search absolute left-3 pointer-events-none text-gray-400"></i>
              <input class="month-search-input bg-white flex h-10 bg-background px-8 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 w-full pl-10 pr-4 py-2 rounded-full border border-gray-200 focus:ring-2 focus:ring-gray-100 focus:border-gray-300 shadow-sm"
                    placeholder="Search appointments and patients…"
                    type="search">
            </div>
         </div>
      </div>

      <div class="flex-1 h-full p-2">
         <div class="grid grid-cols-[auto_repeat(7,1fr)] gap-1 bg-gray-50 rounded-xl overflow-hidden h-full" style="opacity: 1; will-change: opacity, transform; transform: none;">
            <div class="bg-gray-50 py-3"></div>
            <div class="bg-gray-50 py-3 text-center text-sm font-medium text-gray-500">Sun</div>
            <div class="bg-gray-50 py-3 text-center text-sm font-medium text-gray-500">Mon</div>
            <div class="bg-gray-50 py-3 text-center text-sm font-medium text-gray-500">Tue</div>
            <div class="bg-gray-50 py-3 text-center text-sm font-medium text-gray-500">Wed</div>
            <div class="bg-gray-50 py-3 text-center text-sm font-medium text-gray-500">Thu</div>
            <div class="bg-gray-50 py-3 text-center text-sm font-medium text-gray-500">Fri</div>
            <div class="bg-gray-50 py-3 text-center text-sm font-medium text-gray-500">Sat</div>

            <% 6.times do |row| %>
              <% row_dates = (1..7).map { |i| @date.beginning_of_month + (row * 7 + i - start_day_offset(@date)).days } %>
              <% practitioners_available_current_row = @filtered_practitioners.select do |practitioner| %>
                <% row_dates.any? { |date| @availability_data.dig(date, practitioner.id).present? } %>
              <% end %>

              <div class="space-y-3 py-2 px-1 mt-[26px]">
                <% practitioners_available_current_row.each do |dentist| %>
                  <div class="w-6 h-6 rounded-full overflow-hidden flex-shrink-0">
                    <% if dentist.image.attached? %>
                      <%= image_tag(dentist.image, class: "w-full h-full object-cover", 'data-tippy-content': dentist.full_name) %>
                    <% else %>
                      <%= image_tag("default-avatar.webp", class: "w-full h-full object-cover", 'data-tippy-content': dentist.full_name) %>
                    <% end %>
                  </div>
                <% end %>
              </div>

              <% 7.times do |i| %>
                <% day_index = row * 7 + i + 1 %>
                <% cell_date = @date.beginning_of_month + (day_index - start_day_offset(@date)).days %>

                <%= link_to staff_calendar_admin_calendar_bookings_path(
                  mode: 'day',
                  hidden_dentists: params[:hidden_dentists].to_a,
                  date: cell_date
                ), class: "h-full p-2 border border-gray-100 rounded-md #{cell_date.month == @date.month ? 'bg-white' : 'bg-gray-50/50'} #{"ring-2 ring-blue-200 ring-inset" if cell_date == Date.current}" do %>
                  <div class="text-sm font-medium text-gray-700 mb-2"><%= cell_date.day %></div>

                  <div class="w-full mt-3">
                    <% practitioners_available_current_row.each_with_index do |dentist, dentist_index| %>
                      <div class="relative h-[6px] mb-[30px] w-full rounded-[6px] bg-gray-100">
                        <% busy_intervals = @availability_data.dig(cell_date, dentist.id) || [] %>
                        <% busy_intervals.each do |interval| %>
                          <div class="absolute inset-y-0 rounded-[6px] busy-interval"
                              data-tippy-content='
                                <div class="w-[250px] p-2">
                                  <div class="font-semibold text-sm mb-1 text-gray-800"><%= interval[:patient_name] %></div>
                                  <div class="text-gray-700 font-medium text-xs mb-1"><%= interval[:treatment_name] %></div>
                                  <div class="flex items-center justify-between">
                                    <span class="text-[0.7rem] text-gray-500"><%= interval[:booking_times_with_date] %></span>
                                    <span class="text-[0.65rem] bg-gray-100 px-1.5 py-0.5 rounded-full text-gray-500">
                                      <%= interval[:duration_in_minutes] %> min
                                    </span>
                                  </div>
                                  <div class="mt-2 pt-2 border-t border-gray-100 text-[0.7rem] text-gray-600 italic line-clamp-2">
                                    <%= interval[:notes] %>
                                  </div>
                                </div>'
                              style="left: <%= interval[:start_percentage] %>%;
                                     width: <%= interval[:duration_percentage] %>%;
                                     background-color: <%= interval[:color] %>;">
                          </div>
                        <% end %>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              <% end %>
            <% end %>
         </div>
      </div>
   </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    $(function(){
      // TODO: fix search not working
      $('.month-search-input').on('input', function(){
        const $f = $(this);
        const $iv = $('.busy-interval');
        const txt = h => $('<div>').html(h).text().toLowerCase();

        const q = $f.val().trim().toLowerCase();
        $iv.removeClass('dim highlight');
        if(!q) return;
        $iv.each(function(){
          const hit = txt($(this).attr('data-tippy-content') || '').includes(q);
          $(this).toggleClass('highlight', hit).toggleClass('dim', !hit);
        });
      });
    });
  });
</script>
