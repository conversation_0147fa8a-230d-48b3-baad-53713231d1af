<% @timeline.each do |entry| %>
  <% if (entry[:start_time].min % 60).zero? %>
    <div class="h-6 flex items-center justify-end pr-2 text-sm font-semibold text-gray-700 border-t border-gray-200"
          style="height: <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>px;">
      <%= entry[:start_time].in_time_zone.strftime('%-l.%M %p') %>
    </div>
  <% elsif (entry[:start_time].min % 30).zero? %>
    <div class="h-6 flex items-center justify-end pr-2 text-xs font-medium text-gray-600 border-t border-gray-200 whitespace-nowrap"
          style="height: <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>px;">
      <%= entry[:start_time].in_time_zone.strftime('%-l.%M %p') %>
    </div>
  <% elsif (entry[:start_time].min % 15).zero? %>
    <div class="h-6 flex items-center justify-end pr-2 text-[12px] font-normal text-gray-500 pl-5 border-t border-gray-200 whitespace-nowrap"
          style="height: <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>px;">
      <%= entry[:start_time].in_time_zone.strftime('%M') %>
    </div>
  <% elsif (entry[:start_time].min % 5).zero? %>
    <div class="h-6 flex items-center justify-end pr-2 text-[9px] font-normal text-gray-500 pl-6  whitespace-nowrap"
          style="height: <%= Admin::CalendarBookingsHelper::TIMELINE_ENTRY_HEIGHT %>px;">
      <%= entry[:start_time].in_time_zone.strftime('%M') %>
    </div>
  <% end %>
<% end %>
