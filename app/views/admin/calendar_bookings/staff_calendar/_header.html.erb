<div class="flex justify-between items-center pt-8 mb-4 mx-8 p-2">
  <div class="text-2xl font-semibold text-gray-900">
    <% if from_cancellation_list %>
      Cancellation List
    <% elsif @multisite_calendar %>
      Calendar (All Practices)
    <% else %>
      Calendar
    <% end %>
  </div>

  <div class="flex gap-2 justify-center items-center">
    <% if from_cancellation_list %>
      <%= link_to 'Back to Calendar', staff_calendar_admin_calendar_bookings_path, class: 'inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border hover:text-accent-foreground h-9 px-3 rounded-full border-gray-200 bg-white text-gray-700 hover:bg-gray-50 text-sm font-medium shadow-sm', data: { turbo: false } %>
    <% end %>

    <div class="relative w-64 items-center flex">
      <i class="far fa-search absolute left-3 pointer-events-none" style="color: gray;"></i>

      <input id="calendar-search-input" class="bg-white flex h-10 bg-background px-8 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 w-full pl-10 pr-4 py-2 rounded-full border border-gray-200 focus:ring-2 focus:ring-gray-100 focus:border-gray-300 shadow-sm" placeholder="Search..." type="search">
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function() {
    const searchInput = document.getElementById('calendar-search-input');

    <% if @highlight_booking %>
      if (searchInput) {
        searchInput.value = "<%= session[:search_query] %>";
      }
    <% end %>

    if (searchInput) {
      searchInput.addEventListener('keydown', function(event) {
        if (event.key === 'Enter') {
          event.preventDefault();

          const query = searchInput.value.trim();
          if (query === '') {
            return;
          }

          if (query.length < 5) {
            Swal.fire({
              title: 'Search requires more characters',
              text: 'Please enter at least 5 characters to search for appointments.',
              icon: 'info',
              confirmButtonText: 'OK',
            });
            return;
          }

          const isCancellationList = "<%= from_cancellation_list %>" === 'true';

          let additionalData = isCancellationList ? '&on_cancellation_list=true' : '';

          $.ajax({
            url: `/admin/calendar_bookings/search`,
            type: 'GET',
            data: `query=${encodeURIComponent(query)}${additionalData}`,
            dataType: 'script'
          });
        }
      });
    }
  });
</script>
