<div class="<%= card_klass %> z-3 absolute shadow-md bg-[#c4dffe] rounded-[14px] w-full flex flex-col gap-3 box-border justify-between mt-1 mb-1 <%= "calendar-booking-highlight" if @highlight_booking == booking %>"
     style="
       top: <%= card_top %>px;
       height: <%= card_height %>px;
       background-color: <%= card_bg_color %>;
       margin-left: 3px;
       width: calc(100% - 3px);
       padding: <%= booking.duration_in_minutes <= 10 ? '2px 6px' : '8px 12px' %>;
     "
     id="<%= card_id %>"
     data-booking-id="<%= booking.id %>"
>
  <div class="flex flex-col flex-grow gap-2">
    <div class="flex flex-row items-center">
      <div class="text-left flex flex-col gap-[2px] font-[400] break-words no-underline text-gray-800">

        <div class="font-medium text-gray-800 text-[14px] tracking-tight whitespace-nowrap overflow-hidden text-ellipsis">
          <%= link_to booking.display_title.truncate(@mode == :day ? 18 : 16), booking.patient ? admin_patient_charting_path(booking.patient) : '#', class: 'calendar-booking-patient-name no-underline hover:underline text-gray-800' %>

          <% if booking.duration_in_minutes <= 5 %>
            <span class="calendar-booking-times items-center text-[12px] ml-1 text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis">
            </span>
          <% end %>
        </div>

        <% if booking.duration_in_minutes > 5 %>
          <div class="calendar-booking-times flex items-center text-[12px] text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis">
            <%= booking.start_time.in_time_zone.strftime('%-l.%M%p') %> - <%= booking.end_time.in_time_zone.strftime('%-l.%M%p') %>
          </div>
        <% end %>
      </div>

      <%= render 'admin/calendar_bookings/status_label', booking: booking, show_duration: true %>

      <% unless booking.booking_type_event? %>
        <!-- NOTE: info button -->
        <div class="relative">
          <div
            class="booking-info-toggle w-6 h-6 ml-1 mr-1 rounded-full bg-white flex items-center justify-center shadow-sm border border-gray-100 cursor-pointer"
            data-booking-id="<%= booking.id %>"
            data-info-url="<%= modal_admin_calendar_booking_path(booking, mode: @mode, date: @date, format: :html) %>">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-info w-3.5 h-3.5 text-gray-500">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 16v-4"></path>
              <path d="M12 8h.01"></path>
            </svg>
          </div>

          <div class="booking-info-popup hidden w-[400px] rounded-lg overflow-hidden border border-gray-200 bg-white shadow-lg pointer-events-auto text-left"
              data-booking-id="<%= booking.id %>">
            <div class="booking-info-card p-2 w-full rounded-lg bg-white opacity-0 scale-90 transform transition duration-200 ease-out">
              <div class="p-4 text-center text-sm text-gray-500">Loading…</div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- NOTE: actions button -->
      <div class="relative <%= 'ml-1' if booking.booking_type_event? %>">
        <div
          class="booking-actions-toggle w-6 h-6 rounded-full bg-white/80 flex items-center justify-center shadow-xs border border-blue-300/30 hover:bg-white transition-colors cursor-pointer backdrop-blur-sm"
          data-booking-id="<%= booking.id %>">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-ellipsis w-3.5 h-3.5 text-gray-500/90">
            <circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle>
          </svg>
        </div>

        <div class="booking-actions-menu hidden absolute right-0 top-8 z-50 w-48 rounded-xl overflow-hidden border border-gray-100 bg-white shadow-lg"
             data-booking-id="<%= booking.id %>">
          <button type="button" class="edit-booking-times-button cursor-pointer flex w-full items-center gap-2 px-4 py-2 text-xs text-gray-800 hover:bg-gray-50" data-booking-id="<%= booking.id %>">
            Edit Appointment
          </button>

          <button type="button" class="calendar-booking-clipboard-button cursor-pointer flex w-full items-center gap-2 px-4 py-2 text-xs text-gray-800 hover:bg-gray-50">
            Copy to clipboard
          </button>

          <% unless booking.booking_type_event? %>
            <% if booking.is_on_cancellation_list? %>
              <%= link_to cancellation_list_modal_admin_calendar_booking_path(
                booking,
                mode: @mode,
                date: @date,
                hidden_dentists: params[:hidden_dentists]
              ), method: :get, remote: true, class: 'cursor-pointer flex w-full items-center gap-2 px-4 py-2 text-xs text-gray-800 hover:bg-gray-50' do %>
                View Cancellation List Criteria
              <% end %>

              <%= link_to remove_from_cancellation_list_admin_calendar_booking_path(
                booking,
                mode: @mode,
                date: @date,
                hidden_dentists: params[:hidden_dentists]
              ), method: :post, class: 'cursor-pointer flex w-full items-center gap-2 px-4 py-2 text-xs text-gray-800 hover:bg-gray-50' do %>
                Remove from Cancellation List
              <% end %>
            <% else %>
              <%= link_to cancellation_list_modal_admin_calendar_booking_path(
                booking,
                mode: @mode,
                date: @date,
                hidden_dentists: params[:hidden_dentists]
              ), method: :get, remote: true, class: 'cursor-pointer flex w-full items-center gap-2 px-4 py-2 text-xs text-gray-800 hover:bg-gray-50' do %>
                Add to Cancellation List
              <% end %>
            <% end %>

            <% if booking.payment_request_status == 'paid' %>
              <div class="flex w-full items-center gap-2 px-4 py-2 text-xs text-gray-400 cursor-not-allowed">
                Payment Request is paid
              </div>
            <% elsif booking.payment_request_status == 'unpaid' %>
              <button type="button" class="edit-payment-request-button cursor-pointer flex w-full items-center gap-2 px-4 py-2 text-xs text-gray-800 hover:bg-gray-50"
                      data-booking-id="<%= booking.id %>"
                      data-payment-status="<%= booking.payment_request_status %>"
                      data-payment-amount="<%= booking.payment_request_amount %>">
                Edit Payment Request
              </button>
            <% else %>
              <button type="button" class="add-payment-request-button cursor-pointer flex w-full items-center gap-2 px-4 py-2 text-xs text-gray-800 hover:bg-gray-50"
                      data-booking-id="<%= booking.id %>">
                Add Payment Request
              </button>
            <% end %>

            <%= link_to reschedule_offcanvas_admin_calendar_booking_path(
                  booking,
                  mode: @mode,
                  date: @date,
                  hidden_dentists: params[:hidden_dentists]
                ),
                method: :get,
                remote: true,
                class: 'flex w-full items-center gap-2 px-4 py-2 text-xs text-gray-800 hover:bg-gray-50' do %>
              Reschedule appointment
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <% if booking.duration_in_minutes > 10 && !booking.booking_type_event? %>
      <div class="mb-2 flex items-center justify-between">
        <div class="calendar-booking-treatment inline-block px-3.5 py-0.5 bg-white rounded-full text-[12px] text-gray-800 font-medium shadow-sm border border-gray-100 whitespace-nowrap overflow-hidden text-ellipsis"
             data-tippy-content="<%= booking.treatment&.patient_friendly_name || 'Other' %>">
          <%= booking.treatment&.patient_friendly_name.presence&.truncate(@mode == :day ? 20 : 10) || 'Other' %>
        </div>

        <div class="flex gap-[6px]">
          <%= render 'admin/calendar_bookings/staff_calendar/icons_group',
                      record: booking,
                      icon_klass: 'calendar-booking-icon' %>
        </div>
      </div>
    <% end %>

    <% if booking.notes.present? && booking.duration_in_minutes > 20 && !booking.booking_type_event? %>
      <div class="text-xs text-gray-700 font-medium line-clamp-2 pr-6">
        <%= booking.notes %>
      </div>
    <% end %>
  </div>
</div>

<%= render 'admin/calendar_bookings/status_label_overlay', booking: booking %>

<!-- NOTE: Actions dropdown -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    const zHigh = 1000

    $(document)
      .off('click.bookingActions')
      .on('click.bookingActions', '.booking-actions-toggle', function (e) {
        e.stopPropagation()
        const id    = $(this).data('bookingId')
        const $menu = $(`.booking-actions-menu[data-booking-id='${id}']`)
        const $card = $(this).closest('.day-calendar-booking')

        $('.booking-actions-menu').not($menu).each((_, el) =>
          $(el).addClass('hidden').appendTo($(el).data('origin'))
        )

        if ($menu.hasClass('hidden')) {
          if (!$menu.data('origin')) $menu.data('origin', $menu.parent())
          $('body').append($menu)

          const r = this.getBoundingClientRect()
          let left = r.right + scrollX - $menu.outerWidth()
          let top  = r.bottom + scrollY + 8
          if (left < 8) left = 8
          if (left + $menu.outerWidth() > innerWidth) left = innerWidth - $menu.outerWidth() - 8

          $menu.css({ position: 'absolute', left, top, zIndex: zHigh }).removeClass('hidden')
          $card.css('z-index', zHigh - 1)
        } else {
          $menu.addClass('hidden').appendTo($menu.data('origin'))
          $card.css('z-index', 3)
        }
      })

      .off('click.bookingClose')
      .on('click.bookingClose', '.booking-actions-menu button, .booking-actions-menu a', function () {
        const $menu = $(this).closest('.booking-actions-menu')
        $menu.addClass('hidden').appendTo($menu.data('origin'))
        $('.day-calendar-booking').css('z-index', 3)
      })

      .off('click.bookingOutside')
      .on('click.bookingOutside', e => {
        if (!$(e.target).closest('.booking-actions-menu, .booking-actions-toggle').length) {
          $('.booking-actions-menu').each((_, el) =>
            $(el).addClass('hidden').appendTo($(el).data('origin'))
          )
          $('.day-calendar-booking').css('z-index', 3)
        }
      })

      .off('click.editBookingTimes')
      .on('click.editBookingTimes', '.edit-booking-times-button', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const bookingId = $(this).data('booking-id');
        const $menu = $(this).closest('.booking-actions-menu');

        $menu.addClass('hidden').appendTo($menu.data('origin'));
        $('.day-calendar-booking').css('z-index', 3);

        $.ajax({
          url: `/admin/calendar_bookings/${bookingId}/edit_times_modal`,
          method: 'GET',
          data: {
            mode: '<%= @mode %>',
            date: '<%= @date %>',
            hidden_dentists: JSON.stringify('<%= raw(params[:hidden_dentists].presence || [].to_json) %>')
          },
          success: function(response) {}
        });
      })

      .off('click.addPaymentRequest')
      .on('click.addPaymentRequest', '.add-payment-request-button', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const bookingId = $(this).data('booking-id');
        const $menu = $(this).closest('.booking-actions-menu');

        $menu.addClass('hidden').appendTo($menu.data('origin'));
        $('.day-calendar-booking').css('z-index', 3);

        $('#payment-request-modal-title').text('Add Payment Request');
        $('#payment-request-booking-id').val(bookingId);
        $('#payment-request-status').val('unpaid');
        $('#payment-request-amount').val('');
        $('#status-field').removeClass('hidden');
        $('#mark-as-paid-button').addClass('hidden');
        $('#remove-payment-request-button').addClass('hidden');

        openPaymentRequestModal();
      })

      .off('click.editPaymentRequest')
      .on('click.editPaymentRequest', '.edit-payment-request-button', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const bookingId = $(this).data('booking-id');
        const paymentStatus = $(this).data('payment-status');
        const paymentAmount = $(this).data('payment-amount');
        const $menu = $(this).closest('.booking-actions-menu');

        $menu.addClass('hidden').appendTo($menu.data('origin'));
        $('.day-calendar-booking').css('z-index', 3);

        $('#payment-request-modal-title').text('Edit Payment Request');
        $('#payment-request-booking-id').val(bookingId);
        $('#payment-request-status').val(paymentStatus || 'unpaid');
        $('#payment-request-amount').val(paymentAmount || '');
        $('#status-field').addClass('hidden');
        $('#remove-payment-request-button').removeClass('hidden');

        if (paymentStatus === 'unpaid') {
          $('#mark-as-paid-button').removeClass('hidden');
        } else {
          $('#mark-as-paid-button').addClass('hidden');
        }

        openPaymentRequestModal();
      });
  }, { once: true })
</script>

<!-- NOTE: Info popup -->
<script>
  document.addEventListener("DOMContentLoaded", () => {
    const zHigh = 1000
    const loadingTpl =
      '<div class="booking-info-card p-2 w-full rounded-lg bg-white opacity-0 scale-95 transform transition duration-150 ease-out">' +
      '<div class="p-4 text-center text-sm text-gray-500">Loading…</div></div>'

    function showPopup($icon, $popup) {
      $('body').append($popup)
      const r = $icon[0].getBoundingClientRect()
      let left = r.left + scrollX, top = r.bottom + scrollY + 8
      if (left + $popup.outerWidth() > innerWidth) left = innerWidth - $popup.outerWidth() - 8
      $popup.css({ position: 'absolute', left, top, zIndex: zHigh }).removeClass('hidden')

      const $card = $popup.find('.booking-info-card')
      $card.removeClass('opacity-0 scale-90').addClass('opacity-100 scale-105')
      setTimeout(() => $card.removeClass('scale-105').addClass('scale-100'), 180)

      $icon.closest('.day-calendar-booking').css('z-index', zHigh - 1)
    }

    function hidePopup($p) {
      const $card = $p.find('.booking-info-card')
      $card.removeClass('opacity-100 scale-100').addClass('opacity-0 scale-90')
      setTimeout(() => $p.addClass('hidden'), 200)
      $('.day-calendar-booking').css('z-index', 3)
    }

    $(document)
      .off('click.bookingInfo')
      .on('click.bookingInfo', '.booking-info-toggle', function (e) {
        e.stopPropagation()
        const id = $(this).data('bookingId')
        const url = $(this).data('infoUrl')
        const $pop = $(`.booking-info-popup[data-booking-id='${id}']`)

        $('.booking-info-popup').not($pop).each((_, el) => hidePopup($(el)))

        if ($pop.hasClass('hidden')) {
          if (!$pop.data('loaded')) {
            if (!$pop.find('.booking-info-card').length) $pop.html(loadingTpl)   // inject loader once
            showPopup($(this), $pop)                                             // show immediately
            $.get(url, html => {
              $pop.html(html).data('loaded', true)
              requestAnimationFrame(() =>
                $pop.find('.booking-info-card')
                    .removeClass('opacity-0 scale-95')
                    .addClass('opacity-100 scale-100'))
            })
          } else {
            showPopup($(this), $pop)
          }
        } else {
          hidePopup($pop)
        }
      })

      .off('click.bookingInfoOutside')
      .on('click.bookingInfoOutside', e => {
        if (!$(e.target).closest('.booking-info-popup, .booking-info-toggle').length)
          $('.booking-info-popup').each((_, el) => hidePopup($(el)))
      })
  }, { once: true })
</script>
