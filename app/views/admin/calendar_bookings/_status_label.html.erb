<div class="bg-orange-100 px-[10px] py-1 text-center items-center rounded-full ml-auto h-[26px] flex cursor-pointer gap-1 booking-status-label"
     id="booking-status-label-<%= booking.id %>"
     style="<%= 'margin-left: 0;' unless show_duration %>">

  <%= status_icon(booking, 'booking-status-label-icon') %>

  <% if show_duration %>
    <div class="text-center text-[12px] text-orange-700 whitespace-nowrap overflow-hidden text-ellipsis">
      <%= format_duration(booking.duration_in_minutes) %>
    </div>
  <% end %>
</div>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const bookingId = '<%= booking.id %>';
    const bookingStatusLabel = document.getElementById('booking-status-label-' + bookingId);
    const modalOverlay = document.getElementById('booking-status-label-modal-overlay-' + bookingId);
    const modalContent = modalOverlay.querySelector('.booking-status-label-modal-content');
    const statusOptions = modalContent.querySelectorAll('.status-option');
    const minuteOptions = modalContent.querySelector('.minute-options');
    const pillOptions = minuteOptions.querySelectorAll('.pill-option');

    let runningLateOpen = false;

    bookingStatusLabel.addEventListener('click', (event) => {
      event.stopPropagation();
      event.preventDefault();

      modalOverlay.style.display = 'block';

      const rect = bookingStatusLabel.getBoundingClientRect();

      let leftPosition = rect.left;
      let topPosition = Math.min(Math.max(0, rect.bottom), window.innerHeight - modalContent.clientHeight);

      modalContent.style.left = leftPosition + 'px';
      modalContent.style.top = topPosition + 'px';

      const modalRect = modalContent.getBoundingClientRect();

      if (modalRect.right > window.innerWidth) {
        leftPosition = window.innerWidth - modalRect.width - 10;
        if (leftPosition < 0) {
          leftPosition = 0;
        }
        modalContent.style.left = leftPosition + 'px';
      }

      modalContent.style.transform = 'none';
    });

    modalOverlay.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();

      if (!modalContent.contains(event.target)) {
        modalOverlay.style.display = 'none';
        minuteOptions.classList.remove('visible');
        runningLateOpen = false;
      }
    });

    modalContent.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
    });

    statusOptions.forEach(option => {
      option.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const status = option.getAttribute('data-status');
        if (status === 'running_late') {
          if (runningLateOpen) {
            minuteOptions.classList.remove('visible');
            runningLateOpen = false;
          } else {
            minuteOptions.classList.add('visible');
            runningLateOpen = true;
          }
        } else if (status === 'cancelled') {
          if (e.target.id === 'cancel-event-link') {
            modalOverlay.style.display = 'none';

            Swal.fire({
              title: 'Cancel Recurring Event?',
              text: 'Do you want to cancel only this single event or all future events? This action cannot be undone.',
              icon: 'warning',
              showCancelButton: true,
              confirmButtonText: 'Only This Event',
              cancelButtonText: 'All Future Events'
            }).then((result) => {
              if (result.isConfirmed) {
                // Only this event
                submitCancellation('<%= cancel_admin_calendar_booking_path(booking) %>', { only_this_event: '1' });
              } else if (result.dismiss === Swal.DismissReason.cancel) {
                // All future
                submitCancellation('<%= cancel_admin_calendar_booking_path(booking) %>', { all_future: '1' });
              }
            });
          } else if (e.target.id === 'cancel-booking-link') {
            modalOverlay.style.display = 'none';

            Swal.fire({
              title: 'Are you sure you want to cancel this booking?',
              text: 'The booking will be marked as cancelled and removed from the calendar. This action cannot be undone.',
              icon: 'warning',
              showCancelButton: true,
              confirmButtonText: 'Yes, cancel',
              cancelButtonText: 'No, keep it'
            }).then((result) => {
              if (result.isConfirmed) {
                submitCancellation(e.target.dataset.url);
              }
            });
          }

          function submitCancellation(url, extraParams = {}) {
            const formData = new FormData();
            formData.append('authenticity_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            for (let key in extraParams) {
              formData.append(key, extraParams[key]);
            }

            fetch(url, {
              method: 'POST',
              body: formData
            })
              .then(async (response) => {
                if (!response.ok) {
                  const data = await response.json().catch(() => ({}));
                  throw { status: response.status, data };
                }
                location.reload();
              })
              .catch(({ status, data }) => {
                modalOverlay.style.display = 'none';

                const message =
                  status === 403
                    ? 'You are not authorized to cancel this booking.'
                    : (data && data.message) || 'An error occurred while cancelling the booking.';

                Swal.fire('Error', message, 'error');
              });
          }
        } else {
          // modalOverlay.style.display = 'none';
          minuteOptions.classList.remove('visible');
          runningLateOpen = false;

          const data = {
            calendar_booking: {
              status: status,
            }
          };

          $.ajax({
            url: '/admin/calendar_bookings/' + bookingId + '/update_status',
            type: 'POST',
            data: data,
            success: function(response) {
              location.reload();
            },
            error: function(xhr, status, error) {
              modalOverlay.style.display = 'none';

              var text;
              if (error === 'Forbidden') {
                text = 'You do not have permission to change the status of this booking.';
              } else {
                text = 'An error occurred while updating the booking status.';
              }

              Swal.fire({
                icon: 'error',
                title: 'Error',
                text: text
              });
            }
          });
        }
      });
    });

    pillOptions.forEach(pill => {
      pill.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();

        const delay = pill.getAttribute('data-delay');
        const status = pill.getAttribute('data-status');
        // modalOverlay.style.display = 'none';
        minuteOptions.classList.remove('visible');
        runningLateOpen = false;

        const data = {
          calendar_booking: {
            status: status,
            running_late_delay: delay
          }
        };

        $.ajax({
          url: '/admin/calendar_bookings/' + bookingId + '/update_status',
          type: 'POST',
          data: data,
          success: function(response) {
            location.reload();
          },
          error: function(xhr, status, error) {
            modalOverlay.style.display = 'none';

            var text;
            if (error === 'Forbidden') {
              text = 'You do not have permission to change the status of this booking.';
            } else {
              text = 'An error occurred while updating the booking status.';
            }

            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: text
            });
          }
        });
      });
    });
  }, { once: true });
</script>
