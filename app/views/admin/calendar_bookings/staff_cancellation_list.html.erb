<div class="calendar-scss">
  <%= render 'admin/calendar_bookings/staff_calendar/header', from_cancellation_list: true %>
</div>

<div class="calendar-scss cancellation-list-bookings-table">
  <div class="cancellation-list-table-header">
    <div class="cancellation-list-table-cell">Customer name</div>
    <div class="cancellation-list-table-cell">Clinician</div>
    <div class="cancellation-list-table-cell">Appointment</div>
    <div class="cancellation-list-table-cell">Notes</div>
    <div class="cancellation-list-table-cell">Current Appointment Date</div>
    <div class="cancellation-list-table-cell">Current Appointment Time</div>
    <div class="cancellation-list-table-cell">Duration</div>
    <div class="cancellation-list-table-cell">Earliest Bookable</div>
    <div class="cancellation-list-table-cell"></div>
  </div>

  <div class="cancellation-list-table-body">
    <% @bookings.each do |booking| %>
      <div class="cancellation-list-table-row">
        <!-- Customer Name -->
        <div class="cancellation-list-table-cell">
          <%= booking.display_title %>
        </div>

        <!-- Clinician with Avatar -->
        <div class="cancellation-list-table-cell cancellation-list-clinician-cell">
          <% if booking.practitioner.image.attached? %>
            <%= image_tag(booking.practitioner.image, class: "cancellation-list-clinician-avatar") %>
          <% else %>
            <%= image_tag("default-avatar.webp", class: "cancellation-list-clinician-avatar") %>
          <% end %>
          <span><%= booking.practitioner.full_name %></span>
        </div>

        <!-- Appointment -->
        <div class="cancellation-list-table-cell">
          <%= link_to staff_calendar_admin_calendar_bookings_path(booking, mode: 'day', date: booking.start_time.to_date, hidden_dentists: params[:hidden_dentists], highlight_id: booking.id), class: 'inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border hover:text-accent-foreground h-9 px-3 rounded-full border-gray-200 bg-white text-gray-700 hover:bg-gray-50 text-sm font-medium shadow-sm' do %>
            View on Calendar
          <% end %>
        </div>

        <!-- Notes with Tooltip -->
        <div class="cancellation-list-table-cell cancellation-list-notes-cell">
          <% if booking.notes.present? && booking.notes != Admin::CalendarBookingsHelper::NO_NOTES_AVAILABLE %>
            <div class="cancellation-list-notes-icon">
              <%= image_tag("icons/note.svg", alt: "Notes", class: "cancellation-list-note-icon") %>
              <div class="cancellation-list-tooltip">
                <%= booking.notes.presence || 'No notes' %>
              </div>
            </div>
          <% end %>
        </div>

        <!-- Current Appointment Date -->
        <div class="cancellation-list-table-cell">
          <%= booking.start_time.strftime("%d/%m/%Y") %>
        </div>

        <!-- Current Appointment Time (UTC) -->
        <div class="cancellation-list-table-cell">
          <%= booking.start_time.in_time_zone.strftime("%H:%M") %>
        </div>

        <!-- Duration -->
        <div class="cancellation-list-table-cell">
          <%= booking.duration_in_minutes %> mins
        </div>

        <!-- Earliest Bookable -->
        <div class="cancellation-list-table-cell">
          <% if booking.lab_works.any? && booking.lab_works.last.lab_dockets.any? %>
            <% docket_return_time_with_allowance = (booking.lab_works.last.lab_dockets.last.date_return.to_time + 1.day) %>

            <%= [docket_return_time_with_allowance, Time.now].max.strftime("%d/%m/%Y") %>
          <% else %>
            <%= (Time.now + 1.day).strftime("%d/%m/%Y") %>
          <% end %>
        </div>

        <!-- Icon for More Options (Lines Icon) -->

        <div class="cancellation-list-table-cell cancellation-list-actions-cell">
          <div class="dropdown">
            <div class="dropdown-trigger">
              <%= image_tag("icons/lines.svg", alt: "More Options", class: "cancellation-list-lines-icon") %>
            </div>
            <div class="dropdown-menu">
              <ul>
                <li>
                  <%= link_to 'Remove from Cancellation List', remove_from_cancellation_list_admin_calendar_booking_path(booking), method: :post, data: { confirm: 'Are you sure you want to remove this booking from the cancellation list?' }, class: 'cursor-pointer flex w-full items-center gap-2 px-4 py-2 text-xs text-gray-800 hover:bg-gray-50' %>
                </li>
                <li>
                  <%= link_to cancellation_list_modal_admin_calendar_booking_path(booking), method: :get, remote: true, class: 'cursor-pointer flex w-full items-center gap-2 px-4 py-2 text-xs text-gray-800 hover:bg-gray-50' do %>
                    Cancellation List Criteria
                  <% end %>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>

<%= render 'admin/calendar_bookings/offcanvas', id: 'reschedule-offcanvas', title: 'Reschedule Appointment' %>
<%= render 'admin/calendar_bookings/offcanvas', id: 'search-offcanvas', title: 'Search Results' %>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.dropdown-trigger').forEach(function(trigger) {
      trigger.addEventListener('click', function(e) {
        e.stopPropagation();
        var dropdown = this.closest('.dropdown');
        dropdown.classList.toggle('open');
      });
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
      document.querySelectorAll('.dropdown').forEach(function(dropdown) {
        dropdown.classList.remove('open');
      });
    });
  }, { once: true });
</script>
