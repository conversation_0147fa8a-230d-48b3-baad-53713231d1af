<div class="flex w-full mb-6 gap-2">
  <label class="w-1/3 cursor-pointer">
    <input type="radio" id="treatment-mode" name="booking_mode" value="treatment" class="sr-only peer" checked>
    <span class="block py-2 text-center font-medium text-[14px] rounded-lg transition-colors duration-150 text-gray-600 hover:bg-gray-100 peer-checked:bg-[#a8d8ea] peer-checked:text-black">
      Treatment
    </span>
  </label>

  <label class="w-1/3 cursor-pointer">
    <input type="radio" id="event-mode" name="booking_mode" value="event" class="sr-only peer">
    <span class="block py-2 text-center font-medium text-[14px] rounded-lg transition-colors duration-150 text-gray-600 hover:bg-gray-100 peer-checked:bg-[#a8d8ea] peer-checked:text-black">
      Event
    </span>
  </label>

  <label class="w-1/3 cursor-pointer">
    <input type="radio" id="reserved-block-mode" name="booking_mode" value="reserved_block" class="sr-only peer">
    <span class="block py-2 text-center font-medium text-[14px] rounded-lg transition-colors duration-150 text-gray-600 hover:bg-gray-100 peer-checked:bg-[#a8d8ea] peer-checked:text-black">
      Reserved Block
    </span>
  </label>
</div>

<%= form_with model: @booking, url: admin_calendar_bookings_path, scope: 'calendar_booking', method: :post, local: true, id: 'new-appointment-form' do |f| %>
  <%= f.hidden_field :booking_type, value: 'treatment', id: 'calendar-booking-type' %>
  <%= f.hidden_field :practitioner_id, value: @practitioner.id, id: 'calendar-booking-practitioner-id' %>

  <div class="flex flex-col w-full gap-4 pb-4">
    <div class="flex w-full gap-4">
      <div class="relative flex-1">
        <%= f.date_field :date, value: @date_for_select, required: true, disabled: true, class: 'w-full h-11 px-3 py-2 text-sm text-gray-700 bg-[#F7F7F7] border border-gray-200 rounded-md' %>
        <i class="fa-regular fa-calendar-days absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"></i>
      </div>

      <div class="relative flex-1">
        <%= f.time_field :start_time, value: @booking.start_time&.strftime('%H:%M'), required: true, step: 900, disabled: true, class: 'bg-[#F7F7F7] w-full h-11 px-3 py-2 text-sm text-gray-700 border border-gray-200 rounded-md' %>
        <i class="fa-regular fa-clock absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"></i>
      </div>

      <div class="relative flex-1">
        <%= f.time_field :end_time, value: @booking.end_time&.strftime('%H:%M'), required: true, step: 900, disabled: true, class: 'bg-[#F7F7F7] w-full h-11 px-3 py-2 text-sm text-gray-700 border border-gray-200 rounded-md' %>
        <i class="fa-regular fa-clock absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"></i>
      </div>

      <div class="flex-1">
        <div class="flex items-center bg-[#F7F7F7] border border-gray-200 rounded-md h-11">
          <%= f.hidden_field :duration_in_minutes, value: @booking.duration_in_minutes %>
          <input type="text" value="<%= @booking.duration_in_minutes %> minutes" disabled class="w-full h-full px-3 py-2 text-sm text-gray-700 bg-transparent focus:outline-none" />
        </div>
      </div>
    </div>

    <div id="calendar-form-patient-select-container" class="w-full space-y-1">
      <p class="text-[12px] font-medium text-gray-700">Patient</p>
      <div class="relative w-full">
        <%= f.select :patient_id, [], { prompt: 'Search for a patient' }, {
              required: true,
              id: 'calendar-form-patient-select',
              class: 'w-full h-11 px-3 py-2 text-sm text-gray-700 bg-white border border-gray-200 rounded-md'
            } %>
      </div>

      <div id="new-appointment-selected-appointment-card"
           style="display: none;"
           class="w-full flex items-center justify-center my-2">
      </div>

      <div id="new-appointment-reset-selection-container" class="hidden ml-auto">
        <button type="button"
                class="cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 hover:bg-gray-100 h-9 rounded-md px-3 text-[12px] text-gray-500 hover:text-gray-700"
                id="new-appointment-reset-selection-button">
          Reset Selection
        </button>
      </div>
    </div>

    <div id="treatment-fields-container" class="flex w-full gap-4">
      <div class="w-3/7 space-y-1">
        <p class="text-[12px] font-medium text-gray-700">Treatment</p>
        <%= f.select :treatment_id,
              options_from_collection_for_select(@treatments, :id, :patient_friendly_name),
              { prompt: 'Choose Treatment' },
              {
                required: true,
                id: 'calendar-form-treatment-select',
                class: 'w-full h-11 px-3 py-2 text-sm text-gray-700 bg-white border border-gray-200 rounded-md'
              } %>
      </div>

      <div class="w-4/7 space-y-1">
        <p class="text-[12px] font-medium text-gray-700">Course of Treatment</p>
        <%= f.select :course_of_treatment_id,
              options_for_select([
                ['Do Not Open Course Of Treatment', nil],
                ['Create New Course Of Treatment', 'new']
              ], nil),
              { include_blank: false },
              {
                id: 'calendar-form-treatment-plan-select',
                class: 'w-full h-[37px] px-3 text-sm text-gray-700 bg-white focus:outline-none border border-gray-200 rounded-md'
              } %>
      </div>
    </div>

    <div id="reserved-block-fields-container" class="hidden">
      <%= render 'admin/calendar_bookings/reserved_block_fields', f: f %>
    </div>

    <div class="flex flex-col w-full gap-1">
      <%= f.text_area :notes, rows: 3, placeholder: 'Notes', class: 'w-full h-24 resize-y px-3 py-2 text-[14px] text-[#3F4753] border border-gray-200 rounded-md', id: 'new-appointments-notes-input' %>
    </div>

    <div id="recurrence-fields-container" class="flex flex-col w-full gap-4 hidden">
      <%= render 'admin/calendar_bookings/event_fields', f: f %>
    </div>

    <button id="add-appointment-button"
            class="inline-flex items-center justify-center gap-2 whitespace-nowrap ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 w-full py-3 bg-[#a8d8ea] hover:bg-[#8fcbdf] text-black rounded-full font-medium transition-all text-[14px]">
      Add Appointment
    </button>
  </div>
<% end %>

<div id="treatment-mode-tabs">
  <div class="flex w-full my-4 gap-2">
    <label class="w-1/2 cursor-pointer">
      <input type="radio" id="upcoming-charting-appointments-tab" name="charting_tab" class="sr-only peer" checked>
      <span class="block py-2 text-center font-medium text-[14px] rounded-lg transition-colors duration-150 text-gray-600 hover:bg-gray-100 peer-checked:bg-[#a8d8ea] peer-checked:text-black">
        Upcoming Appointments
      </span>
    </label>

    <label class="w-1/2 cursor-pointer">
      <input type="radio" id="cancellation-list-tab" name="charting_tab" class="sr-only peer">
      <span class="block py-2 text-center font-medium text-[14px] rounded-lg transition-colors duration-150 text-gray-600 hover:bg-gray-100 peer-checked:bg-[#a8d8ea] peer-checked:text-black">
        Cancellation List
      </span>
    </label>
  </div>

  <div id="treatment-mode-upcoming-charting-appointments-section" class="mt-4"></div>
  <div id="treatment-mode-cancellation-list-section" class="mt-4 hidden"></div>
</div>
