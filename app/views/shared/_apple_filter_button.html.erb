<%
  default_options = {
    id: nil,
    class: '',
    data: {},
    type: 'button'
  }

  merged_options = default_options.merge(options)

  data_attrs = merged_options[:data] || {}
  data_attrs[:color] = color

  button_classes = apple_filter_button_classes(color, active, merged_options[:class])
  style_attr = apple_filter_button_style(color, active)
%>

<button
  <% if merged_options[:id] %>id="<%= merged_options[:id] %>"<% end %>
  type="<%= merged_options[:type] %>"
  class="<%= button_classes %>"
  <%= "style=\"#{style_attr}\"".html_safe if style_attr.present? %>
  <% data_attrs.each do |key, value| %>
    data-<%= key %>="<%= value %>"
  <% end %>
>
  <% if icon %>
    <i class="<%= apple_filter_icon_class(color, active, icon) %>"></i>
  <% end %>
  <span class="tab-text text-xs font-medium tracking-wide <%= active ? 'ml-1.5' : 'opacity-0 w-0' %> transition-all duration-300"><%= text %></span>
</button>
