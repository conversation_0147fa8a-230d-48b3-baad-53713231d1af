<div class="relative notification-block mb-3" data-notification-id="<%= notification.id %>" data-date="<%= notification.created_at.strftime('%Y-%m-%d') %>">
  <div class="relative pt-0 pb-0">
    <%# if local_assigns[:stacked] || (defined?(notifications) && notifications.size > 1) %>
    <div class="absolute inset-0 z-0 <%= (local_assigns[:stacked] || (defined?(notifications) && notifications.size > 1)) ? '' : 'hidden' %>">
      <div class="absolute left-0 right-0 top-0 bottom-0 bg-white rounded-xl border border-[#D1D1F7] shadow-sm" style="transform: translateY(4px); z-index: -1; margin-left: 2px; margin-right: 2px;"></div>
      <div class="absolute left-0 right-0 top-0 bottom-0 bg-white rounded-xl border border-[#D1D1F7] shadow-sm" style="transform: translateY(8px); z-index: -2; margin-left: 4px; margin-right: 4px;"></div>
      <div class="absolute left-0 right-0 top-0 bottom-0 bg-white rounded-xl border border-[#D1D1F7] shadow-sm" style="transform: translateY(12px); z-index: -3; margin-left: 6px; margin-right: 6px;"></div>
    </div>
    <%# end %>
    <% if notification.data['style'] == 'attended' %>
      <%# calendar_booking = CalendarBooking.find_by(id: notification.data['booking_id']) %>
      <div class="relative rounded-lg border text-card-foreground w-full max-w-3xl border-green-200 bg-gradient-to-br from-green-50 to-green-100 shadow-lg z-10">
        <div class="flex flex-col space-y-1.5 p-6 pb-3 pt-4 px-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check h-5 w-5 text-green-600">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="m9 12 2 2 4-4"></path>
              </svg>
              <span class="text-sm font-semibold text-green-800">Patient In Waiting Room</span>
            </div>
            <button data-notification-id="<%= notification.id %>" data-index="0" data-action="mark_as_read" data-role="notification-close" class="h-8 w-8 rounded-full hover:bg-green-200 flex items-center justify-center transition-colors" aria-label="Close alert">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x h-5 w-5 text-green-600">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            </button>
          </div>
        </div>
        <div class="p-6 pt-0 space-y-4 px-4 pb-4">
          <div class="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user h-5 w-5 text-green-600">
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            <div>
              <p class="text-sm font-bold text-gray-900"><%= notification.data['sender'] %></p>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-5 w-5 text-green-600">
              <path d="M8 2v4"></path>
              <path d="M16 2v4"></path>
              <rect width="18" height="18" x="3" y="4" rx="2"></rect>
              <path d="M3 10h18"></path>
            </svg>
            <div>
              <p class="text-xs text-gray-600">Appointment Time</p>
              <div class="flex items-center gap-2">
                <p class="text-sm font-medium text-gray-900"><%= notification.data['start_time'] %>-<%= notification.data['end_time'] %></p>
                <span class="text-xs text-green-800 bg-green-200 px-2 py-0.5 rounded-md font-medium"><%= notification.data['duration'] %> mins</span>
              </div>
            </div>
          </div>
          <div>
            <% if notification.data['treatment'].present? %>
              <p class="text-xs text-gray-600 mb-2">Treatment</p>
              <div class="flex flex-wrap gap-2">
                <% (notification.data['treatment']).each do |treatment_name| %>
                  <span class="px-3 py-1 bg-green-200 text-green-800 text-sm rounded-full font-medium">
                    <%= treatment_name %>
                  </span>
                <% end %>
              </div>
            <% end %>
          </div>
          <div class="pt-3 border-t border-green-200">
            <div class="flex items-center justify-center gap-2 text-sm text-green-700 font-medium">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check h-4 w-4">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="m9 12 2 2 4-4"></path>
              </svg>
              <span>Ready for appointment</span>
            </div>
          </div>
        </div>
      </div>
    <% elsif notification.data['style'] == 'started' %>
      <% elapsed_minutes, progress_percent = notification_started_time_and_progress(notification.data['start_time']) %>
      <div class="relative rounded-lg border text-card-foreground w-full max-w-3xl border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100 shadow-lg z-10">
        <div class="flex flex-col space-y-1.5 p-6 pb-3 pt-4 px-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock h-5 w-5 text-orange-600 animate-pulse">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <span class="text-sm font-semibold text-orange-800">Appointment Should Have Started</span>
            </div>
            <button data-notification-id="<%= notification.id %>" data-index="0" data-action="mark_as_read" data-role="notification-close" class="h-8 w-8 rounded-full hover:bg-orange-200 flex items-center justify-center transition-colors" aria-label="Close alert">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x h-5 w-5 text-orange-600">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            </button>
          </div>
        </div>
        <div class="p-6 pt-0 space-y-4 px-4 pb-4">
          <div class="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user h-5 w-5 text-orange-600">
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            <div>
              <p class="text-sm font-bold text-gray-900"><%= notification.data['sender'] %></p>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-5 w-5 text-orange-600">
              <path d="M8 2v4"></path>
              <path d="M16 2v4"></path>
              <rect width="18" height="18" x="3" y="4" rx="2"></rect>
              <path d="M3 10h18"></path>
            </svg>
            <div>
              <p class="text-xs text-gray-600">Appointment Time</p>
              <div class="flex items-center gap-2">
                <p class="text-sm font-medium text-gray-900"><%= notification.data['start_time'] %>-<%= notification.data['end_time'] %></p>
                <span class="text-xs text-orange-800 bg-orange-200 px-2 py-0.5 rounded-md font-medium"><%= notification.data['duration'] %> mins</span>
              </div>
            </div>
          </div>
          <div>
            <% if notification.data['treatment'].present? %>
              <p class="text-xs text-gray-600 mb-2">Treatment</p>
              <div class="flex flex-wrap gap-2">
                <% (notification.data['treatment']).each do |treatment_name| %>
                  <span class="px-3 py-1 bg-orange-200 text-orange-800 text-sm rounded-full font-medium">
                    <%= treatment_name %>
                  </span>
                <% end %>
              </div>
            <% end %>
          </div>
          <div class="space-y-2 pt-3 border-t border-orange-200">
            <div class="flex justify-between items-baseline"><p class="text-xs text-gray-600">Time since appointment started:</p>
              <p class="text-sm font-semibold text-orange-700 tabular-nums"><%= "#{elapsed_minutes} min#{'s' unless elapsed_minutes == 1}" %></p>
            </div>
            <div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100"
                 class="relative w-full overflow-hidden rounded-full h-2.5 bg-orange-100 [&amp;&gt;div]:bg-orange-500">
              <div class="h-full w-full flex-1 bg-orange-500 transition-all" style="width: <%= progress_percent %>%;"></div>
            </div>
          </div>
        </div>
      </div>
    <% elsif notification.data['style'] == 'late' %>
      <% elapsed_minutes, progress_percent = notification_late_time_and_progress(notification.data['start_time']) %>
      <div class="relative rounded-lg border text-card-foreground w-full max-w-3xl border-red-200 bg-gradient-to-br from-red-50 to-red-100 shadow-lg z-10">
        <div class="flex flex-col space-y-1.5 p-6 pb-3 pt-4 px-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle-alert h-5 w-5 text-red-600 animate-pulse">
                <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path>
                <path d="M12 9v4"></path>
                <path d="M12 17h.01"></path>
              </svg>
              <span class="text-sm font-semibold text-red-800">Running Late For Appointment</span>
            </div>
            <button data-notification-id="<%= notification.id %>" data-index="0" data-action="mark_as_read" data-role="notification-close"  class="h-8 w-8 rounded-full hover:bg-red-200 flex items-center justify-center transition-colors" aria-label="Close alert">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x h-5 w-5 text-red-600">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg>
            </button>
          </div>
        </div>
        <div class="p-6 pt-0 space-y-4 px-4 pb-4">
          <div class="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user h-5 w-5 text-red-600">
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            <div>
              <p class="text-sm font-bold text-gray-900"><%= notification.data['sender'] %></p>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-5 w-5 text-red-600">
              <path d="M8 2v4"></path>
              <path d="M16 2v4"></path>
              <rect width="18" height="18" x="3" y="4" rx="2"></rect>
              <path d="M3 10h18"></path>
            </svg>
            <div>
              <p class="text-xs text-gray-600">Appointment Time</p>
              <div class="flex items-center gap-2">
                <p class="text-sm font-medium text-gray-900"><%= notification.data['start_time'] %>-<%= notification.data['end_time'] %></p>
                <span class="text-xs text-red-800 bg-red-200 px-2 py-0.5 rounded-md font-medium"><%= notification.data['duration'] %> mins</span>
              </div>
            </div>
          </div>
          <div>
            <% if notification.data['treatment'].present? %>
              <p class="text-xs text-gray-600 mb-2">Treatment</p>
              <div class="flex flex-wrap gap-2">
                <% (notification.data['treatment']).each do |treatment_name| %>
                  <span class="px-3 py-1 bg-red-200 text-red-800 text-sm rounded-full font-medium">
                    <%= treatment_name %>
                  </span>
                <% end %>
              </div>
            <% end %>
          </div>
          <div class="space-y-2 pt-3 border-t border-red-200">
            <div class="flex justify-between items-baseline">
              <p class="text-xs text-gray-600">Time since appointment started:</p>
              <p class="text-sm font-semibold text-red-700 tabular-nums"><%= "#{elapsed_minutes} min#{'s' unless elapsed_minutes == 1}" %> late</p>
            </div>
            <div aria-valuemax="100" aria-valuemin="0" role="progressbar" data-state="indeterminate" data-max="100"
                 class="relative w-full overflow-hidden rounded-full h-2.5 bg-red-100 [&amp;&gt;div]:bg-red-500">
              <div class="h-full w-full flex-1 bg-red-500 transition-all" style="width: <%= progress_percent %>%;"></div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
