<!-- Actions Sidebar -->
<div class="actions-sidebar bg-white shadow-lg flex flex-col h-full" id="actions-sidebar">
  <div class="actions-header flex items-center justify-between p-4 border-b border-gray-200">
    <div class="tab-container flex space-x-4">
      <div class="tab text-gray-500 hover:text-gray-700 pb-2">Today</div>
      <div class="tab active text-blue-600 font-medium border-b-2 border-blue-600 pb-2">All Actions</div>
    </div>
    <button class="close-button text-gray-500 hover:text-gray-700" id="close-actions">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    </button>
  </div>

  <div class="actions-content flex-1 overflow-y-auto p-4">
    <div class="flex justify-end items-center mb-4">
      <a href="#" class="inline-flex items-center justify-center rounded-md bg-orange-500 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2" data-modal-target="sidebarNewActionModal">+ New Action</a>
    </div>

    <div class="flex items-center justify-between px-4 pt-4 pb-3">
      <div dir="ltr" data-orientation="horizontal" class="w-full max-w-md">
        <div role="tablist" aria-orientation="horizontal" class="items-center justify-center text-muted-foreground grid grid-cols-6 h-9 rounded-full bg-gray-100 p-0.5" tabindex="0" data-orientation="horizontal" style="outline: none;">
          <button type="button" role="tab" aria-selected="true" data-filter="all" class="inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground rounded-full data-[state=active]:bg-white data-[state=active]:shadow-sm active-tab">All</button>
          <button type="button" role="tab" aria-selected="false" data-filter="task" class="inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground rounded-full data-[state=active]:bg-white data-[state=active]:shadow-sm">Tasks</button>
          <button type="button" role="tab" aria-selected="false" data-filter="reminder" class="inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground rounded-full data-[state=active]:bg-white data-[state=active]:shadow-sm">Reminders</button>
          <button type="button" role="tab" aria-selected="false" data-filter="alerts" class="inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground rounded-full data-[state=active]:bg-white data-[state=active]:shadow-sm">Alerts</button>
          <button type="button" role="tab" aria-selected="false" data-filter="callback" class="inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground rounded-full data-[state=active]:bg-white data-[state=active]:shadow-sm">Callbacks</button>
          <button type="button" role="tab" aria-selected="false" data-filter="complaint" class="inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground rounded-full data-[state=active]:bg-white data-[state=active]:shadow-sm">Complaints</button>
        </div>
      </div>
      <button class="flex items-center gap-2 bg-white/90 border border-gray-200/60 rounded-lg px-3 py-1.5 hover:bg-gray-50 transition-colors shadow-sm" type="button">
        <div class="flex items-center gap-2">
          <div class="relative">
            <span class="relative flex shrink-0 overflow-hidden h-7 w-7 rounded-full">
              <% if current_user.avatar.attached? %>
                <%= image_tag current_user.avatar, class: "aspect-square h-full w-full", alt: "Current User" %>
              <% else %>
                <div class="bg-blue-100 text-blue-500 h-full w-full flex items-center justify-center font-semibold text-sm">
                  <%= current_user.first_name.first.upcase %>
                </div>
              <% end %>
            </span>
          </div>
          <span class="text-sm font-medium text-gray-700"><%= current_user.full_name %></span>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 text-gray-500">
          <path d="m6 9 6 6 6-6"></path>
        </svg>
      </button>
    </div>

    <div class="actions-list space-y-4 mt-4">
      <% if @user_actions.present? %>
        <%
          # Sort actions: overdue first, then future due dates, then no due date
          now = Time.zone.now
          far_future = now + 100.years
          sorted_actions = @user_actions.where(completed: false).sort_by do |a|
            if a.date_due.nil?
              [3, far_future, -a.created_at.to_i] # No due date - last, newest first
            elsif a.date_due < now
              [1, a.date_due, -a.created_at.to_i] # Overdue - first, earliest due date first
            else
              [2, a.date_due, -a.created_at.to_i] # Future - middle, earliest due date first
            end
          end
          sorted_actions.each do |action| %>
          <div class="action-card bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow" data-action-type="<%= action.action_type %>">
            <div class="action-header flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <%= action_icon(action) %>
                <div class="action-type text-sm font-medium text-gray-700">
                  <%= action.action_type.capitalize %>
                </div>
              </div>
              <% if action.priority.present? %>
                <div class="action-priority">
                  <%= action_priority_badge(action, style: :sidebar) %>
                </div>
              <% end %>
            </div>

            <div class="action-content">
              <h3 class="text-sm font-medium text-gray-900 mb-1">
                <%= action.title.presence || action.action_type.capitalize %>
              </h3>
              <% if action.description.present? %>
                <p class="text-xs text-gray-600 mb-3 line-clamp-2">
                  <%= action.description %>
                </p>
              <% end %>

              <div class="action-meta flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <% if action.date_due.present? %>
                    <%
                      days_overdue = (Date.today - action.date_due.to_date).to_i
                      is_due_today = days_overdue == 0

                      if days_overdue > 0
                        badge_bg = 'bg-red-100'
                        badge_text = 'text-red-700'
                        badge_border = 'border-red-200'
                        status_text = "#{days_overdue}d overdue"
                      elsif is_due_today
                        badge_bg = 'bg-orange-100'
                        badge_text = 'text-white'
                        badge_border = 'border-orange-200'
                        status_text = 'Due Today'
                      else
                        days_until_due = (action.date_due.to_date - Date.today).to_i
                        badge_bg = 'bg-blue-50'
                        badge_text = 'text-blue-600'
                        badge_border = 'border-blue-200'
                        status_text = "Due in #{days_until_due}d"
                      end
                    %>
                    <span class="text-xs font-medium px-2 py-1 rounded-full <%= badge_bg %> <%= badge_text %> border <%= badge_border %>">
                      <%= status_text %>
                    </span>
                  <% else %>
                    <span class="text-xs text-gray-500">No due date</span>
                  <% end %>
                </div>

                <div class="flex items-center space-x-2">
                  <% if action.assigned_to.present? %>
                    <div class="flex -space-x-1">
                      <% action.assigned_to.first(2).each do |user| %>
                        <div class="w-6 h-6 rounded-full border-2 border-white overflow-hidden">
                          <% if user.avatar.attached? %>
                            <%= image_tag user.avatar, class: "w-full h-full object-cover", alt: user.full_name %>
                          <% else %>
                            <div class="bg-blue-100 text-blue-500 w-full h-full flex items-center justify-center text-xs font-semibold">
                              <%= user.first_name.first.upcase %>
                            </div>
                          <% end %>
                        </div>
                      <% end %>
                      <% if action.assigned_to.count > 2 %>
                        <div class="w-6 h-6 rounded-full border-2 border-white bg-gray-100 flex items-center justify-center">
                          <span class="text-xs text-gray-600">+<%= action.assigned_to.count - 2 %></span>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="empty-state text-center p-8">
          <p class="text-gray-500">No actions found</p>
        </div>
      <% end %>
    </div>
  </div>
</div>

<% # Render the shared modals %>
<%
  # Determine the appropriate actionable based on the current context
  if defined?(@card) && @card.present?
    actionable_type = "CrmCard"
    actionable_id = @card.id
    patient_id = @card.patient&.id
  elsif defined?(@patient) && @patient.present?
    actionable_type = "Patient"
    actionable_id = @patient.id
    patient_id = @patient.id
  else
    actionable_type = "User"
    actionable_id = current_user.id
    patient_id = nil
  end
%>
<%= render partial: "admin/shared/modals/new_action_modal", locals: {
  modal_id: "sidebarNewActionModal",
  actionable_type: actionable_type,
  actionable_id: actionable_id,
  patient_id: patient_id
} %>

<%= render partial: "admin/shared/modals/action_comment_modal", locals: {
  modal_id: "newActionCommentModal"
} %>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Filter tabs functionality - only within the actions sidebar
    const sidebar = document.getElementById('actions-sidebar');
    if (!sidebar) return;

    const filterButtons = sidebar.querySelectorAll('[data-filter]');
    const actionCards = sidebar.querySelectorAll('.action-card');

    filterButtons.forEach(button => {
      button.addEventListener('click', function() {
        // Remove active class from all buttons within sidebar only
        filterButtons.forEach(btn => {
          btn.classList.remove('active-tab');
          btn.setAttribute('aria-selected', 'false');
          btn.classList.remove('bg-white', 'shadow-sm');
        });

        // Add active class to clicked button
        this.classList.add('active-tab');
        this.setAttribute('aria-selected', 'true');
        this.classList.add('bg-white', 'shadow-sm');

        const filter = this.getAttribute('data-filter');

        // Show/hide action cards based on filter - only within sidebar
        actionCards.forEach(card => {
          if (filter === 'all' || card.getAttribute('data-action-type') === filter) {
            card.style.display = 'block';
          } else {
            card.style.display = 'none';
          }
        });
      });
    });

    // Close button functionality - only within the sidebar
    const closeButton = sidebar.querySelector('#close-actions');
    if (closeButton) {
      closeButton.addEventListener('click', function() {
        if (sidebar) {
          sidebar.classList.add('hidden');
        }
      });
    }
  });
</script>

<style>
  .active-tab {
    background-color: white;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
</style>
