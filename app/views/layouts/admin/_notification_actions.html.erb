<% is_alert = notification.title.to_s.downcase.include?('alert') || notification.title.to_s.downcase.include?('complaint') %>
<div class="relative notification-block mb-3" data-notification-id="<%= notification.id %>" data-date="<%= notification.created_at.strftime('%Y-%m-%d') %>">
  <div class="relative pt-0 pb-0">
    <%# if local_assigns[:stacked] || (defined?(notifications) && notifications.size > 1) %>
      <div class="absolute inset-0 z-0 <%= (local_assigns[:stacked] || (defined?(notifications) && notifications.size > 1)) ? '' : 'hidden' %>">
        <div class="absolute left-0 right-0 top-0 bottom-0 bg-white rounded-xl border border-[#FDE68A] shadow-sm" style="transform: translateY(4px); z-index: -1; margin-left: 2px; margin-right: 2px;"></div>
        <div class="absolute left-0 right-0 top-0 bottom-0 bg-white rounded-xl border border-[#FDE68A] shadow-sm" style="transform: translateY(8px); z-index: -2; margin-left: 4px; margin-right: 4px;"></div>
        <div class="absolute left-0 right-0 top-0 bottom-0 bg-white rounded-xl border border-[#FDE68A] shadow-sm" style="transform: translateY(12px); z-index: -3; margin-left: 6px; margin-right: 6px;"></div>
      </div>
    <%# end %>
    <div class="w-full rounded-xl shadow-[0_8px_30px_-10px_<%= is_alert ? 'rgba(239,68,68,0.12)' : 'rgba(245,158,11,0.12)' %>] hover:shadow-lg hover:translate-y-[-1px] border <%= is_alert ? 'border-red-300' : 'border-[#FDE68A]' %> transition-all duration-150 ease-out opacity-100 translate-x-0 relative z-10">
      <div class="flex flex-col w-full rounded-xl overflow-hidden">
        <div class="w-full h-1 <%= is_alert ? 'bg-gradient-to-r from-red-400 to-red-600' : 'bg-gradient-to-r from-[#FBBF24] to-[#D97706]' %>"></div>
        <div class="w-full <%= is_alert ? 'bg-gradient-to-b from-red-100 to-red-50' : 'bg-gradient-to-b from-[#FEF3C7] to-[#FFFBEB]' %>">
          <div class="relative z-10 p-3">
            <div class="flex justify-between items-center mb-1.5">
              <div class="flex items-center gap-3">
                <div class="relative">
                  <div class="w-10 h-10 rounded-full bg-gray-200 border border-gray-300 overflow-hidden relative">
                    <div class="w-full h-full flex items-center justify-center bg-amber-50">
                      <i class="fa-thin fa-list-check <%= is_alert ? 'text-red-400' : 'text-[#FBBF24]' %> text-xl"></i>
                    </div>
                  </div>
                </div>
                <h3 class="font-medium text-[14px] text-[#1A1A2E] tracking-tight"><%= notification.title %></h3>
              </div>
              <div class="flex items-center px-2 py-1 rounded-md border <%= is_alert ? 'bg-red-100 border-red-300' : 'bg-[#FDE68A] border-[#FCD34D]' %>">
                <span class="w-1.5 h-1.5 rounded-full <%= is_alert ? 'bg-red-400' : 'bg-[#FBBF24]' %> animate-pulse mr-1.5"></span>
                <span class="text-[12px] font-medium <%= is_alert ? 'text-red-700' : 'text-[#92400E]' %>"><%= notification.data['style'].titleize %></span>
              </div>
            </div>
<!--            <div class="flex items-center mb-2">-->
<!--              <h4 class="text-[12px] font-medium <%#= is_alert ? 'text-red-500' : 'text-[#FBBF24]' %>"><%#= notification.title %></h4>-->
<!--            </div>-->
            <p class="text-[12px] text-[#4E5D78] leading-snug mt-0.5 mb-2 w-full">
              <%= notification.message&.gsub("\n", " ")&.html_safe || notification.description&.gsub("\n", " ")&.html_safe %>
            </p>
            <div class="flex justify-between items-center mt-2">
              <div class="flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#64748B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
                <span class="text-[11px] text-[#64748B]"><%= formatted_notification_time(notification) %></span>
              </div>
              <div class="relative">
                <button class="notification-options-btn px-2.5 py-1 <%= is_alert ? 'bg-red-50 text-red-500 hover:bg-red-100' : 'bg-[#FFFBEB] text-[#FBBF24] hover:bg-[#FEF3C7]' %> text-[12px] font-medium rounded transition-colors flex items-center gap-1" data-notification-id="<%= notification.id %>">Options
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down transition-transform ">
                    <path d="m6 9 6 6 6-6"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="absolute top-[-10px] right-[-10px] z-[100]">
      <button data-notification-id="<%= notification.id %>" data-index="0" data-action="mark_as_read" data-role="notification-close"
              class="w-[24px] h-[24px] rounded-full <%= is_alert ? 'bg-red-50 hover:bg-red-100 border-red-300' : 'bg-[#FEFCE8] hover:bg-[#FDE68A] border-[#FDE68A]' %> flex items-center justify-center transition-colors shadow-sm border" aria-label="Close notification">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x <%= is_alert ? 'text-red-500' : 'text-[#F59E0B]' %>">
          <path d="M18 6 6 18"></path>
          <path d="m6 6 12 12"></path>
        </svg>
      </button>
    </div>
    <div class="notification-dropdown absolute right-0 mt-1 w-48 bg-white border <%= is_alert ? 'border-red-300' : 'border-[#FDE68A]' %> rounded shadow-md hidden z-150" data-dropdown-for="<%= notification.id %>">
      <% (notification.actions || []).each_with_index do |action, index| %>
        <button class="dropdown-item block w-full text-left px-4 py-2 text-sm <%= is_alert ? 'text-red-500' : 'text-[#FBBF24]' %> hover:bg-[#F0F0FF]"
                data-action="<%= action['action'] %>"
                data-index="<%= index %>"
                data-id="<%= notification.id %>">
          <%= action['text'] %>
        </button>
      <% end %>
    </div>
  </div>
</div>
