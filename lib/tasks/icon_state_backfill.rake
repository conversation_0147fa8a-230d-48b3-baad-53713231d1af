# frozen_string_literal: true

namespace :icon_state do
  desc 'Backfill icon state refresher jobs for records from the last 3 months'
  task backfill: :environment do
    puts 'Starting icon state backfill for records from the last 3 months...'

    calendar_bookings_count = 0
    charting_appointments_count = 0

    CalendarBooking.where('start_time >= ?', 3.months.ago).find_each do |booking|
      IconStateRefresherJob.perform_async('CalendarBooking', booking.id)
      calendar_bookings_count += 1
    end

    ChartingAppointment.where('created_at >= ?', 3.months.ago).find_each do |appointment|
      IconStateRefresherJob.perform_async('ChartingAppointment', appointment.id)
      charting_appointments_count += 1
    end

    puts 'Icon state backfill completed!'
    puts "CalendarBookings processed: #{calendar_bookings_count}"
    puts "ChartingAppointments processed: #{charting_appointments_count}"
    puts "Total jobs queued: #{calendar_bookings_count + charting_appointments_count}"
  end
end
