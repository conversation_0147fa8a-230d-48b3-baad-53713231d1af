# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.2].define(version: 2025_07_23_182951) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_trgm"
  enable_extension "plpgsql"

  create_table "action_comments", force: :cascade do |t|
    t.bigint "action_id"
    t.bigint "user_id", null: false
    t.text "comment"
    t.string "ancestry"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_id"], name: "index_action_comments_on_action_id"
    t.index ["ancestry"], name: "index_action_comments_on_ancestry"
    t.index ["user_id"], name: "index_action_comments_on_user_id"
  end

  create_table "actions", force: :cascade do |t|
    t.string "action_type", default: "task"
    t.string "actionable_type", null: false
    t.bigint "actionable_id", null: false
    t.bigint "created_by_id"
    t.json "assigned_to_json", default: []
    t.string "title"
    t.text "description"
    t.datetime "date_due"
    t.boolean "completed", default: false
    t.string "priority"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "deleted_at"
    t.bigint "calendar_booking_id"
    t.string "assignment_type"
    t.bigint "treatment_plan_id"
    t.bigint "appointment_id"
    t.index ["actionable_type", "actionable_id"], name: "index_actions_on_actionable"
    t.index ["calendar_booking_id"], name: "index_actions_on_calendar_booking_id"
    t.index ["created_by_id"], name: "index_actions_on_created_by_id"
    t.index ["deleted_at"], name: "index_actions_on_deleted_at"
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "alerts", force: :cascade do |t|
    t.string "name"
    t.string "alert_type", default: "accessibility"
    t.string "color"
    t.bigint "practice_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_alerts_on_practice_id"
  end

  create_table "announcements", force: :cascade do |t|
    t.jsonb "reactions", default: {}, null: false
    t.string "audience_roles"
    t.string "audience_teams"
    t.string "status", default: "draft"
    t.string "title", null: false
    t.string "topic", null: false
    t.text "text", null: false
    t.bigint "practice_id"
    t.bigint "author_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["author_id"], name: "index_announcements_on_author_id"
    t.index ["practice_id"], name: "index_announcements_on_practice_id"
  end

  create_table "appointment_notes", force: :cascade do |t|
    t.text "content"
    t.bigint "charting_appointment_id"
    t.bigint "user_id"
    t.bigint "practitioner_id"
    t.datetime "completed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "calendar_booking_id"
    t.string "note_type", default: "public"
    t.boolean "visible", default: true
    t.bigint "author_id"
    t.boolean "locked", default: false
    t.datetime "locked_at"
    t.bigint "nurse_id"
    t.index ["author_id"], name: "index_appointment_notes_on_author_id"
    t.index ["calendar_booking_id", "created_at"], name: "index_appointment_notes_on_calendar_booking_id_and_created_at"
    t.index ["calendar_booking_id", "note_type"], name: "index_appointment_notes_on_calendar_booking_id_and_note_type"
    t.index ["charting_appointment_id"], name: "index_appointment_notes_on_charting_appointment_id"
    t.index ["nurse_id"], name: "index_appointment_notes_on_nurse_id"
    t.index ["practitioner_id"], name: "index_appointment_notes_on_practitioner_id"
    t.index ["user_id"], name: "index_appointment_notes_on_user_id"
  end

  create_table "automation_actions", force: :cascade do |t|
    t.string "action_type", null: false
    t.string "value"
    t.string "additional_value"
    t.bigint "automation_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_id"], name: "index_automation_actions_on_automation_id"
  end

  create_table "automation_condition_values", force: :cascade do |t|
    t.string "value", null: false
    t.string "value_type"
    t.bigint "automation_condition_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_condition_id"], name: "index_automation_condition_values_on_automation_condition_id"
  end

  create_table "automation_conditions", force: :cascade do |t|
    t.string "field_name", null: false
    t.string "predicate", null: false
    t.bigint "automation_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_id"], name: "index_automation_conditions_on_automation_id"
  end

  create_table "automation_triggers", force: :cascade do |t|
    t.string "trigger_key", null: false
    t.string "value"
    t.bigint "automation_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["automation_id"], name: "index_automation_triggers_on_automation_id"
  end

  create_table "automations", force: :cascade do |t|
    t.string "name", null: false
    t.boolean "enabled", default: false
    t.bigint "practice_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_automations_on_practice_id"
  end

  create_table "bank_holidays", force: :cascade do |t|
    t.date "date"
    t.string "name"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_bank_holidays_on_practice_id"
  end

  create_table "base_charts", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["patient_id"], name: "index_base_charts_on_patient_id"
  end

  create_table "bewes", force: :cascade do |t|
    t.text "scores", default: [], array: true
    t.boolean "bleedings", default: [], array: true
    t.boolean "furcations", default: [], array: true
    t.string "bewe_tag"
    t.boolean "completed", default: false
    t.integer "patient_id"
    t.bigint "completed_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.index ["completed_by_id"], name: "index_bewes_on_completed_by_id"
    t.index ["import_source", "import_id"], name: "index_bewes_on_import_source_and_import_id", unique: true
  end

  create_table "bpes", force: :cascade do |t|
    t.text "scores", default: [], array: true
    t.string "next_assessment"
    t.string "bpe_tag"
    t.boolean "completed", default: false
    t.integer "patient_id"
    t.bigint "completed_by_id"
    t.bigint "practice_id"
    t.boolean "bleedings", default: [], array: true
    t.boolean "furcations", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.index ["completed_by_id"], name: "index_bpes_on_completed_by_id"
    t.index ["import_source", "import_id"], name: "index_bpes_on_import_source_and_import_id", unique: true
    t.index ["practice_id"], name: "index_bpes_on_practice_id"
  end

  create_table "calendar_booking_cancellations", force: :cascade do |t|
    t.datetime "start_time"
    t.datetime "end_time"
    t.integer "status", default: 0
    t.string "token"
    t.bigint "booking_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["booking_id"], name: "index_calendar_booking_cancellations_on_booking_id"
  end

  create_table "calendar_bookings", force: :cascade do |t|
    t.datetime "start_time", null: false
    t.datetime "end_time", null: false
    t.datetime "cancelled_at"
    t.string "notes"
    t.integer "status", default: 0
    t.integer "booking_type", default: 0
    t.boolean "is_on_cancellation_list"
    t.jsonb "cancellation_list_criteria", default: {}
    t.string "recurrent_identifier"
    t.string "event_color", default: "#D8D8D8"
    t.bigint "patient_id"
    t.bigint "practitioner_id", null: false
    t.bigint "booked_by_id", null: false
    t.bigint "practice_id", null: false
    t.bigint "treatment_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "meeting_id"
    t.string "waiting_room_job_id"
    t.boolean "late_cancellation", default: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.string "booked_by_type", default: "User", null: false
    t.integer "payment_request_status", default: 0
    t.decimal "payment_request_amount", precision: 10, scale: 2
    t.integer "running_late_delay"
    t.index ["booked_by_id", "booked_by_type"], name: "index_calendar_bookings_on_booked_by_id_and_booked_by_type"
    t.index ["import_source", "import_id"], name: "index_calendar_bookings_on_import_source_and_import_id", unique: true
    t.index ["meeting_id"], name: "index_calendar_bookings_on_meeting_id"
    t.index ["patient_id"], name: "index_calendar_bookings_on_patient_id"
    t.index ["practice_id"], name: "index_calendar_bookings_on_practice_id"
    t.index ["practitioner_id"], name: "index_calendar_bookings_on_practitioner_id"
    t.index ["treatment_id"], name: "index_calendar_bookings_on_treatment_id"
  end

  create_table "calendar_day_notes", force: :cascade do |t|
    t.date "date"
    t.text "note"
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "practice_id"
    t.string "recurrent_identifier"
    t.index ["practice_id"], name: "index_calendar_day_notes_on_practice_id"
    t.index ["user_id"], name: "index_calendar_day_notes_on_user_id"
  end

  create_table "calendar_reserved_block_types", force: :cascade do |t|
    t.bigint "calendar_setting_id", null: false
    t.bigint "treatment_id", null: false
    t.bigint "practice_id", null: false
    t.boolean "no_patient_required", default: false
    t.boolean "indicates_new", default: false
    t.string "color", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.boolean "online_bookable"
    t.boolean "deposit_required"
    t.boolean "only_bookable_within_reserved_slots"
    t.decimal "new_patient_deposit_size"
    t.integer "user_ids", array: true
    t.decimal "old_patient_deposit_size"
    t.text "treatment_description"
    t.index ["calendar_setting_id"], name: "index_calendar_reserved_block_types_on_calendar_setting_id"
    t.index ["import_source", "import_id"], name: "idx_on_import_source_import_id_16bcee39b3", unique: true
    t.index ["practice_id"], name: "index_calendar_reserved_block_types_on_practice_id"
    t.index ["treatment_id"], name: "index_calendar_reserved_block_types_on_treatment_id"
  end

  create_table "calendar_reserved_blocks", force: :cascade do |t|
    t.bigint "calendar_reserved_block_type_id", null: false
    t.bigint "practitioner_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "start_time", null: false
    t.datetime "end_time", null: false
    t.string "recurrent_identifier"
    t.index ["practitioner_id"], name: "index_calendar_reserved_blocks_on_practitioner_id"
    t.index ["recurrent_identifier"], name: "index_calendar_reserved_blocks_on_recurrent_identifier"
    t.index ["start_time"], name: "index_calendar_reserved_blocks_on_start_time"
  end

  create_table "calendar_settings", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "late_cancellation_window_hours", default: 24
    t.bigint "practice_id"
    t.index ["practice_id"], name: "index_calendar_settings_on_practice_id"
  end

  create_table "card_activities", force: :cascade do |t|
    t.bigint "card_id", null: false
    t.bigint "user_id", null: false
    t.string "activity_type", null: false
    t.text "description"
    t.jsonb "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["card_id", "created_at"], name: "index_card_activities_on_card_and_created_at"
    t.index ["card_id"], name: "index_card_activities_on_card_id"
    t.index ["user_id"], name: "index_card_activities_on_user_id"
  end

  create_table "card_comments", force: :cascade do |t|
    t.bigint "card_id", null: false
    t.bigint "user_id", null: false
    t.text "content", null: false
    t.string "ancestry"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ancestry"], name: "index_card_comments_on_ancestry"
    t.index ["card_id", "ancestry"], name: "index_card_comments_on_card_and_ancestry"
    t.index ["card_id"], name: "index_card_comments_on_card_id"
    t.index ["user_id"], name: "index_card_comments_on_user_id"
  end

  create_table "cbcts", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "course_of_treatment_id"
    t.json "teeth", default: []
    t.text "justification"
    t.date "date_taken"
    t.text "description"
    t.bigint "developed_by_id", null: false
    t.bigint "taken_by_id", null: false
    t.text "grade"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "charting_appointment_id"
    t.index ["charting_appointment_id"], name: "index_cbcts_on_charting_appointment_id"
    t.index ["course_of_treatment_id"], name: "index_cbcts_on_course_of_treatment_id"
    t.index ["developed_by_id"], name: "index_cbcts_on_developed_by_id"
    t.index ["patient_id"], name: "index_cbcts_on_patient_id"
    t.index ["taken_by_id"], name: "index_cbcts_on_taken_by_id"
  end

  create_table "charted_treatments", force: :cascade do |t|
    t.bigint "treatment_id", null: false
    t.bigint "practitioner_id"
    t.bigint "referrer_id"
    t.bigint "charting_appointment_id"
    t.text "notes"
    t.integer "duration"
    t.string "payment_plan_type"
    t.string "position"
    t.string "surface"
    t.boolean "child_tooth_flag", default: false
    t.string "pricing_type"
    t.decimal "override_price", precision: 10, scale: 2
    t.boolean "completed", default: false
    t.datetime "completed_at"
    t.boolean "charged", default: false
    t.datetime "charged_at"
    t.boolean "urgent", default: false
    t.boolean "free_repair", default: false
    t.string "regulation_11"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position_order", default: 0
    t.boolean "locked", default: false, null: false
    t.integer "invoice_id"
    t.bigint "base_chart_id"
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.string "discount_type"
    t.decimal "discount_amount", precision: 10, scale: 2
    t.bigint "nurse_id"
    t.index ["base_chart_id"], name: "index_charted_treatments_on_base_chart_id"
    t.index ["charting_appointment_id"], name: "index_charted_treatments_on_charting_appointment_id"
    t.index ["import_source", "import_id"], name: "index_charted_treatments_on_import_source_and_import_id", unique: true
    t.index ["invoice_id"], name: "index_charted_treatments_on_invoice_id"
    t.index ["nurse_id"], name: "index_charted_treatments_on_nurse_id"
    t.index ["practitioner_id"], name: "index_charted_treatments_on_practitioner_id"
    t.index ["referrer_id"], name: "index_charted_treatments_on_referrer_id"
    t.index ["treatment_id"], name: "index_charted_treatments_on_treatment_id"
  end

  create_table "charting_appointments", force: :cascade do |t|
    t.bigint "course_of_treatment_id"
    t.bigint "dentist_id"
    t.boolean "completed", default: false
    t.datetime "completed_at"
    t.boolean "locked", default: false
    t.datetime "locked_at"
    t.integer "position"
    t.string "appointment_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "calendar_booking_id"
    t.integer "time_until_booking_count", default: 0
    t.integer "time_until_booking_unit", default: 1
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.index ["calendar_booking_id"], name: "index_charting_appointments_on_calendar_booking_id"
    t.index ["course_of_treatment_id"], name: "index_charting_appointments_on_course_of_treatment_id"
    t.index ["dentist_id"], name: "index_charting_appointments_on_dentist_id"
    t.index ["import_source", "import_id"], name: "index_charting_appointments_on_import_source_and_import_id", unique: true
  end

  create_table "charting_files", force: :cascade do |t|
    t.string "file_type"
    t.string "grade"
    t.bigint "developed_by_id"
    t.bigint "taken_by_id"
    t.string "justification"
    t.string "other_justification"
    t.date "date_taken", default: -> { "CURRENT_DATE" }
    t.text "description"
    t.string "tooth_position"
    t.bigint "patient_id", null: false
    t.json "teeth", default: []
    t.bigint "charting_appointment_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["charting_appointment_id"], name: "index_charting_files_on_charting_appointment_id"
    t.index ["developed_by_id"], name: "index_charting_files_on_developed_by_id"
    t.index ["patient_id"], name: "index_charting_files_on_patient_id"
    t.index ["taken_by_id"], name: "index_charting_files_on_taken_by_id"
  end

  create_table "clinical_images", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "course_of_treatment_id", null: false
    t.json "teeth", default: []
    t.text "justification"
    t.date "date_taken"
    t.text "description"
    t.bigint "developed_by_id", null: false
    t.bigint "taken_by_id", null: false
    t.text "grade"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_of_treatment_id"], name: "index_clinical_images_on_course_of_treatment_id"
    t.index ["developed_by_id"], name: "index_clinical_images_on_developed_by_id"
    t.index ["patient_id"], name: "index_clinical_images_on_patient_id"
    t.index ["taken_by_id"], name: "index_clinical_images_on_taken_by_id"
  end

  create_table "communication_account_users", force: :cascade do |t|
    t.bigint "communication_account_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["communication_account_id", "user_id"], name: "idx_unique_account_user", unique: true
    t.index ["communication_account_id"], name: "index_communication_account_users_on_communication_account_id"
    t.index ["user_id"], name: "index_communication_account_users_on_user_id"
  end

  create_table "communication_accounts", force: :cascade do |t|
    t.bigint "practice_id", null: false
    t.string "account_type", null: false
    t.string "identifier", null: false
    t.string "display_name"
    t.boolean "is_default", default: false
    t.jsonb "credentials"
    t.jsonb "settings"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "active", default: true
    t.index ["active"], name: "index_communication_accounts_on_active"
    t.index ["practice_id", "account_type", "identifier"], name: "idx_unique_practice_account", unique: true
    t.index ["practice_id"], name: "index_communication_accounts_on_practice_id"
  end

  create_table "conversation_messages", force: :cascade do |t|
    t.bigint "conversation_id", null: false
    t.text "content"
    t.string "message_type"
    t.string "from"
    t.string "to"
    t.string "source"
    t.integer "author_id"
    t.string "category"
    t.string "delivery_status"
    t.datetime "scheduled"
    t.datetime "read_at"
    t.string "email_subject"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "label"
    t.string "external_id"
    t.bigint "lab_work_id"
    t.integer "reader_id"
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.text "failure_reason"
    t.index ["author_id"], name: "index_conversation_messages_on_author_id"
    t.index ["conversation_id"], name: "index_conversation_messages_on_conversation_id"
    t.index ["import_source", "import_id"], name: "index_conversation_messages_on_import_source_and_import_id", unique: true
    t.index ["lab_work_id"], name: "index_conversation_messages_on_lab_work_id"
    t.index ["read_at"], name: "index_conversation_messages_on_read_at"
    t.index ["reader_id"], name: "index_conversation_messages_on_reader_id"
  end

  create_table "conversations", force: :cascade do |t|
    t.integer "patient_id"
    t.string "name"
    t.boolean "flag", default: false
    t.boolean "star", default: false
    t.boolean "internal", default: false
    t.boolean "archived", default: false
    t.json "internal_recipients_json"
    t.bigint "practice_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "lab_id"
    t.index ["lab_id"], name: "index_conversations_on_lab_id"
    t.index ["practice_id"], name: "index_conversations_on_practice_id"
  end

  create_table "cot_categories", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_cot_categories_on_practice_id"
  end

  create_table "cot_payment_plans", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "practice_id", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.string "color", default: "blue"
    t.index ["import_source", "import_id"], name: "index_cot_payment_plans_on_import_source_and_import_id", unique: true
    t.index ["practice_id"], name: "index_cot_payment_plans_on_practice_id"
  end

  create_table "cot_payment_plans_treatments", force: :cascade do |t|
    t.bigint "treatment_id", null: false
    t.bigint "cot_payment_plan_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "data", default: {}
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.jsonb "duration_data", default: {}
    t.index ["cot_payment_plan_id"], name: "index_cot_payment_plans_treatments_on_cot_payment_plan_id"
    t.index ["import_source", "import_id"], name: "idx_on_import_source_import_id_8076de4fd2", unique: true
    t.index ["treatment_id"], name: "index_cot_payment_plans_treatments_on_treatment_id"
  end

  create_table "cot_template_note_users", force: :cascade do |t|
    t.bigint "cot_template_note_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cot_template_note_id"], name: "index_cot_template_note_users_on_cot_template_note_id"
    t.index ["user_id"], name: "index_cot_template_note_users_on_user_id"
  end

  create_table "cot_template_notes", force: :cascade do |t|
    t.string "title", null: false
    t.text "content", null: false
    t.bigint "user_id"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.index ["import_source", "import_id"], name: "index_cot_template_notes_on_import_source_and_import_id", unique: true
    t.index ["practice_id"], name: "index_cot_template_notes_on_practice_id"
    t.index ["user_id"], name: "index_cot_template_notes_on_user_id"
  end

  create_table "cot_templates", force: :cascade do |t|
    t.string "name"
    t.jsonb "appointments"
    t.jsonb "treatments"
    t.bigint "practice_id", null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_cot_templates_on_created_by_id"
    t.index ["practice_id"], name: "index_cot_templates_on_practice_id"
  end

  create_table "course_of_treatments", force: :cascade do |t|
    t.string "name"
    t.bigint "patient_id", null: false
    t.bigint "dentist_id", null: false
    t.bigint "treatment_coordinator_id"
    t.bigint "lead_clinician_id", null: false
    t.bigint "practice_id", null: false
    t.text "treatment_plan_notes"
    t.boolean "archived", default: false
    t.datetime "archived_at"
    t.datetime "completed_at"
    t.datetime "charged_date"
    t.boolean "accepted", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.bigint "cot_category_id"
    t.index ["cot_category_id"], name: "index_course_of_treatments_on_cot_category_id"
    t.index ["dentist_id"], name: "index_course_of_treatments_on_dentist_id"
    t.index ["import_source", "import_id"], name: "index_course_of_treatments_on_import_source_and_import_id", unique: true
    t.index ["lead_clinician_id"], name: "index_course_of_treatments_on_lead_clinician_id"
    t.index ["patient_id"], name: "index_course_of_treatments_on_patient_id"
    t.index ["practice_id"], name: "index_course_of_treatments_on_practice_id"
    t.index ["treatment_coordinator_id"], name: "index_course_of_treatments_on_treatment_coordinator_id"
  end

  create_table "crm_boards", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "practice_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_crm_boards_on_practice_id"
  end

  create_table "crm_cards", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.bigint "crm_list_id", null: false
    t.integer "position", default: 0
    t.json "labels", default: []
    t.json "audit", default: []
    t.bigint "patient_id"
    t.json "members", default: []
    t.bigint "treatment_id"
    t.datetime "last_contacted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "status", default: "active"
    t.datetime "completed_at"
    t.bigint "cot_category_id"
    t.index ["cot_category_id"], name: "index_crm_cards_on_cot_category_id"
    t.index ["crm_list_id", "status", "created_at"], name: "index_crm_cards_on_list_status_created"
    t.index ["crm_list_id", "status"], name: "index_crm_cards_on_list_and_status"
    t.index ["crm_list_id"], name: "index_crm_cards_on_crm_list_id"
    t.index ["last_contacted_at"], name: "index_crm_cards_on_last_contacted_at"
    t.index ["patient_id"], name: "index_crm_cards_on_patient_id"
    t.index ["status", "completed_at"], name: "index_crm_cards_on_status_and_completed_at"
    t.index ["status"], name: "index_crm_cards_on_status"
    t.index ["treatment_id"], name: "index_crm_cards_on_treatment_id"
  end

  create_table "crm_cards_checklists", id: false, force: :cascade do |t|
    t.bigint "crm_card_id", null: false
    t.bigint "crm_checklist_id", null: false
    t.index ["crm_card_id", "crm_checklist_id"], name: "index_crm_cards_checklists_on_crm_card_id_and_crm_checklist_id", unique: true
    t.index ["crm_checklist_id", "crm_card_id"], name: "index_crm_cards_checklists_on_crm_checklist_id_and_crm_card_id"
  end

  create_table "crm_cards_labels", id: false, force: :cascade do |t|
    t.bigint "crm_card_id", null: false
    t.bigint "crm_label_id", null: false
    t.index ["crm_card_id", "crm_label_id"], name: "index_crm_cards_labels_on_crm_card_id_and_crm_label_id", unique: true
    t.index ["crm_card_id"], name: "index_crm_cards_labels_on_crm_card_id"
    t.index ["crm_label_id"], name: "index_crm_cards_labels_on_crm_label_id"
  end

  create_table "crm_checklist_items", force: :cascade do |t|
    t.string "content", null: false
    t.boolean "completed", default: false
    t.integer "position", default: 0
    t.bigint "crm_checklist_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["crm_checklist_id", "position"], name: "index_crm_checklist_items_on_crm_checklist_id_and_position"
    t.index ["crm_checklist_id"], name: "index_crm_checklist_items_on_crm_checklist_id"
  end

  create_table "crm_checklists", force: :cascade do |t|
    t.string "title", default: "Checklist", null: false
    t.bigint "crm_card_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "crm_board_id"
    t.index ["crm_board_id"], name: "index_crm_checklists_on_crm_board_id"
    t.index ["crm_card_id"], name: "index_crm_checklists_on_crm_card_id"
  end

  create_table "crm_completion_zones", force: :cascade do |t|
    t.string "name", null: false
    t.string "color", default: "blue"
    t.bigint "crm_board_id", null: false
    t.integer "position", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["crm_board_id"], name: "index_crm_completion_zones_on_crm_board_id"
  end

  create_table "crm_custom_field_values", force: :cascade do |t|
    t.bigint "crm_card_id", null: false
    t.bigint "crm_custom_field_id", null: false
    t.text "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["crm_card_id", "crm_custom_field_id"], name: "index_crm_custom_field_values_on_card_and_field", unique: true
    t.index ["crm_card_id"], name: "index_crm_custom_field_values_on_crm_card_id"
    t.index ["crm_custom_field_id"], name: "index_crm_custom_field_values_on_crm_custom_field_id"
  end

  create_table "crm_custom_fields", force: :cascade do |t|
    t.bigint "crm_board_id", null: false
    t.string "name", null: false
    t.string "field_type", default: "text", null: false
    t.boolean "required", default: false
    t.json "options", default: []
    t.integer "position", default: 0
    t.boolean "show_on_card", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "prefix"
    t.string "postfix"
    t.string "unit_type"
    t.index ["crm_board_id", "name"], name: "index_crm_custom_fields_on_crm_board_id_and_name", unique: true
    t.index ["crm_board_id", "position"], name: "index_crm_custom_fields_on_board_and_position"
    t.index ["crm_board_id"], name: "index_crm_custom_fields_on_crm_board_id"
  end

  create_table "crm_labels", force: :cascade do |t|
    t.string "name", null: false
    t.string "color", null: false
    t.bigint "crm_board_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["crm_board_id"], name: "index_crm_labels_on_crm_board_id"
    t.index ["name", "crm_board_id"], name: "index_crm_labels_on_name_and_crm_board_id", unique: true
  end

  create_table "crm_list_restrictions", force: :cascade do |t|
    t.bigint "crm_list_id", null: false
    t.string "restriction_type"
    t.string "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["crm_list_id"], name: "index_crm_list_restrictions_on_crm_list_id"
  end

  create_table "crm_lists", force: :cascade do |t|
    t.string "title", null: false
    t.bigint "crm_board_id", null: false
    t.integer "position", default: 0
    t.boolean "is_completed_list", default: false
    t.boolean "is_default", default: false
    t.integer "summary_custom_field_id"
    t.string "summary_unit"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_archived_list", default: false
    t.integer "crm_cards_count", default: 0, null: false
    t.index ["crm_board_id", "position"], name: "index_crm_lists_on_board_and_position"
    t.index ["crm_board_id"], name: "index_crm_lists_on_crm_board_id"
    t.index ["summary_custom_field_id"], name: "index_crm_lists_on_summary_custom_field_id"
  end

  create_table "dashboard_widgets", force: :cascade do |t|
    t.integer "type_id"
    t.jsonb "settings", default: {}, null: false
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "dashboard_id", null: false
    t.index ["dashboard_id"], name: "index_dashboard_widgets_on_dashboard_id"
    t.index ["practice_id"], name: "index_dashboard_widgets_on_practice_id"
  end

  create_table "dashboards", force: :cascade do |t|
    t.string "title"
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_dashboards_on_user_id"
  end

  create_table "document_templates", force: :cascade do |t|
    t.bigint "practice_id", null: false
    t.string "name"
    t.text "text"
    t.string "template_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_document_templates_on_practice_id"
  end

  create_table "document_templates_treatments", force: :cascade do |t|
    t.bigint "document_template_id", null: false
    t.bigint "treatment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_template_id"], name: "index_document_templates_treatments_on_document_template_id"
    t.index ["treatment_id"], name: "index_document_templates_treatments_on_treatment_id"
  end

  create_table "document_types", force: :cascade do |t|
    t.boolean "manual"
    t.boolean "onboarding", default: false
    t.string "name"
    t.text "description"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_document_types_on_practice_id"
  end

  create_table "documents", force: :cascade do |t|
    t.boolean "ai_validated", default: false
    t.datetime "completed_at"
    t.datetime "approved_at"
    t.float "ai_confidence_score"
    t.text "ai_validation_message"
    t.bigint "user_id", null: false
    t.bigint "document_type_id", null: false
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_type_id"], name: "index_documents_on_document_type_id"
    t.index ["practice_id"], name: "index_documents_on_practice_id"
    t.index ["user_id"], name: "index_documents_on_user_id"
  end

  create_table "event_logs", force: :cascade do |t|
    t.jsonb "data"
    t.integer "event_type"
    t.jsonb "object_info"
    t.string "object_type"
    t.bigint "object_id"
    t.string "user_name"
    t.bigint "user_id"
    t.bigint "patient_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["object_type", "object_id"], name: "index_event_logs_on_object"
    t.index ["patient_id"], name: "index_event_logs_on_patient_id"
    t.index ["user_id"], name: "index_event_logs_on_user_id"
  end

  create_table "form_assignments", force: :cascade do |t|
    t.boolean "completed", default: false
    t.datetime "completed_at"
    t.integer "manual_document_category"
    t.json "answers", default: {}
    t.jsonb "response", default: {}
    t.string "manual_title"
    t.bigint "practice_id"
    t.bigint "onboarding_form_id"
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["onboarding_form_id"], name: "index_form_assignments_on_onboarding_form_id"
    t.index ["practice_id"], name: "index_form_assignments_on_practice_id"
    t.index ["user_id"], name: "index_form_assignments_on_user_id"
  end

  create_table "group_meeting_participants", force: :cascade do |t|
    t.bigint "group_meeting_id"
    t.bigint "user_id"
    t.index ["group_meeting_id"], name: "index_group_meeting_participants_on_group_meeting_id"
    t.index ["user_id"], name: "index_group_meeting_participants_on_user_id"
  end

  create_table "group_meetings", force: :cascade do |t|
    t.datetime "scheduled_at"
    t.integer "meeting_status", default: 0, null: false
    t.integer "meeting_type", default: 0, null: false
    t.jsonb "participant_locations", default: {}, null: false
    t.string "location"
    t.string "title"
    t.text "agenda"
    t.text "description"
    t.bigint "manager_id"
    t.bigint "meeting_id", null: false
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["manager_id"], name: "index_group_meetings_on_manager_id"
    t.index ["meeting_id"], name: "index_group_meetings_on_meeting_id"
    t.index ["practice_id"], name: "index_group_meetings_on_practice_id"
  end

  create_table "holidays", force: :cascade do |t|
    t.boolean "approved"
    t.datetime "start"
    t.datetime "end"
    t.datetime "approved_at"
    t.string "holiday_type"
    t.string "approved_by_id"
    t.string "half_day"
    t.string "event_start"
    t.string "event_end"
    t.text "notes"
    t.bigint "user_id", null: false
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_holidays_on_practice_id"
    t.index ["user_id"], name: "index_holidays_on_user_id"
  end

  create_table "hr_templates", force: :cascade do |t|
    t.string "title", null: false
    t.boolean "new_users_only", default: false, null: false
    t.bigint "practice_id"
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_hr_templates_on_created_by_id"
    t.index ["practice_id"], name: "index_hr_templates_on_practice_id"
  end

  create_table "hr_templates_onboarding_forms", force: :cascade do |t|
    t.bigint "hr_template_id", null: false
    t.bigint "onboarding_form_id", null: false
    t.index ["hr_template_id"], name: "index_hr_templates_onboarding_forms_on_hr_template_id"
    t.index ["onboarding_form_id"], name: "index_hr_templates_onboarding_forms_on_onboarding_form_id"
  end

  create_table "hr_templates_users", force: :cascade do |t|
    t.bigint "hr_template_id", null: false
    t.bigint "user_id", null: false
    t.index ["hr_template_id"], name: "index_hr_templates_users_on_hr_template_id"
    t.index ["user_id"], name: "index_hr_templates_users_on_user_id"
  end

  create_table "icon_states", force: :cascade do |t|
    t.bigint "calendar_booking_id"
    t.bigint "charting_appointment_id"
    t.integer "lab_work_state", default: 0, null: false
    t.integer "payments_state", default: 0, null: false
    t.integer "documents_state", default: 0, null: false
    t.integer "complaints_state", default: 0, null: false
    t.integer "medical_history_state", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["calendar_booking_id", "charting_appointment_id"], name: "idx_on_calendar_booking_id_charting_appointment_id_e873b92b7f", unique: true
    t.index ["calendar_booking_id"], name: "index_icon_states_on_calendar_booking_id"
    t.index ["charting_appointment_id"], name: "index_icon_states_on_charting_appointment_id"
  end

  create_table "invoice_items", force: :cascade do |t|
    t.bigint "invoice_id", null: false
    t.bigint "treatment_id"
    t.string "name"
    t.integer "quantity"
    t.decimal "unit_price", precision: 10, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.bigint "charted_treatment_id"
    t.index ["charted_treatment_id"], name: "index_invoice_items_on_charted_treatment_id"
    t.index ["import_source", "import_id"], name: "index_invoice_items_on_import_source_and_import_id", unique: true
    t.index ["invoice_id"], name: "index_invoice_items_on_invoice_id"
    t.index ["treatment_id"], name: "index_invoice_items_on_treatment_id"
  end

  create_table "invoices", force: :cascade do |t|
    t.bigint "created_by_id", null: false
    t.bigint "clinician_id"
    t.bigint "patient_id", null: false
    t.bigint "practice_id", null: false
    t.bigint "course_of_treatment_id"
    t.string "status"
    t.text "notes"
    t.decimal "discount", precision: 10, scale: 2
    t.string "discount_type"
    t.datetime "due_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.string "invoice_key"
    t.index ["clinician_id"], name: "index_invoices_on_clinician_id"
    t.index ["course_of_treatment_id"], name: "index_invoices_on_course_of_treatment_id"
    t.index ["created_by_id"], name: "index_invoices_on_created_by_id"
    t.index ["import_source", "import_id"], name: "index_invoices_on_import_source_and_import_id", unique: true
    t.index ["patient_id"], name: "index_invoices_on_patient_id"
    t.index ["practice_id"], name: "index_invoices_on_practice_id"
  end

  create_table "lab_docket_items", force: :cascade do |t|
    t.bigint "lab_docket_id", null: false
    t.jsonb "info", default: {}
    t.text "further_information"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lab_docket_id"], name: "index_lab_docket_items_on_lab_docket_id"
  end

  create_table "lab_docket_treatments", force: :cascade do |t|
    t.bigint "lab_docket_item_id"
    t.bigint "lab_item_id"
    t.integer "units"
    t.text "further_information"
    t.string "treatment_type"
    t.string "arch"
    t.string "materials"
    t.string "post_core"
    t.integer "num_wings"
    t.string "stage"
    t.string "post_core_materials"
    t.string "implant_system"
    t.string "abutment_material"
    t.string "margin"
    t.string "retention"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "treatment_type_custom_name"
    t.index ["lab_docket_item_id"], name: "index_lab_docket_treatments_on_lab_docket_item_id"
    t.index ["lab_item_id"], name: "index_lab_docket_treatments_on_lab_item_id"
  end

  create_table "lab_dockets", force: :cascade do |t|
    t.jsonb "docket_info", default: {}
    t.bigint "lab_work_id", null: false
    t.boolean "interoral_scan"
    t.boolean "photos_sent"
    t.boolean "physical_impressions_taken"
    t.boolean "draft", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "instructions"
    t.string "disinfected_with"
    t.bigint "disinfected_by_id"
    t.string "made_available"
    t.text "disinfectant_further_information"
    t.index ["disinfected_by_id"], name: "index_lab_dockets_on_disinfected_by_id"
    t.index ["lab_work_id"], name: "index_lab_dockets_on_lab_work_id"
  end

  create_table "lab_items", force: :cascade do |t|
    t.string "name"
    t.integer "price"
    t.boolean "is_unit", default: false
    t.bigint "lab_id", null: false
    t.integer "unit_price"
    t.integer "return_time"
    t.string "tooth_selection_type", default: "Tooth location"
    t.string "treatment_type", default: "Private"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lab_id"], name: "index_lab_items_on_lab_id"
  end

  create_table "lab_work_files", force: :cascade do |t|
    t.string "name"
    t.bigint "lab_work_id", null: false
    t.decimal "amount", precision: 10, scale: 2
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_lab_work_files_on_created_by_id"
    t.index ["lab_work_id"], name: "index_lab_work_files_on_lab_work_id"
  end

  create_table "lab_work_logs", force: :cascade do |t|
    t.string "title", null: false
    t.bigint "created_by_id"
    t.bigint "lab_work_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_lab_work_logs_on_created_by_id"
    t.index ["lab_work_id"], name: "index_lab_work_logs_on_lab_work_id"
  end

  create_table "lab_works", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "lab_id", null: false
    t.string "status", default: "Sent To Lab"
    t.bigint "practitioner_id", null: false
    t.bigint "charting_appointment_id"
    t.json "invoice_data", default: {}
    t.string "code"
    t.string "code_key"
    t.bigint "checked_in_by_id"
    t.bigint "quality_checked_by_id"
    t.bigint "practice_id", null: false
    t.decimal "total", precision: 10, scale: 2
    t.datetime "arrived_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["charting_appointment_id"], name: "index_lab_works_on_charting_appointment_id"
    t.index ["checked_in_by_id"], name: "index_lab_works_on_checked_in_by_id"
    t.index ["lab_id"], name: "index_lab_works_on_lab_id"
    t.index ["patient_id"], name: "index_lab_works_on_patient_id"
    t.index ["practice_id"], name: "index_lab_works_on_practice_id"
    t.index ["practitioner_id"], name: "index_lab_works_on_practitioner_id"
    t.index ["quality_checked_by_id"], name: "index_lab_works_on_quality_checked_by_id"
  end

  create_table "labs", force: :cascade do |t|
    t.string "name"
    t.string "email_address"
    t.string "contact_number"
    t.bigint "practice_id"
    t.string "address_line_1"
    t.string "address_line_2"
    t.string "city"
    t.string "postcode"
    t.integer "company_number"
    t.string "delivery_address_line_1"
    t.string "delivery_address_line_2"
    t.string "delivery_city"
    t.string "delivery_postcode"
    t.boolean "has_delivery_address", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_labs_on_practice_id"
  end

  create_table "letters", force: :cascade do |t|
    t.bigint "practice_id", null: false
    t.bigint "patient_id", null: false
    t.bigint "user_id", null: false
    t.bigint "signable_document_id"
    t.string "stannp_tracking_url"
    t.string "stannp_pdf_url"
    t.text "body"
    t.string "from_address_line_1"
    t.string "from_address_line_2"
    t.string "from_address_line_3"
    t.string "from_county"
    t.string "from_email_address"
    t.string "from_name"
    t.string "from_phone_number"
    t.string "from_postcode"
    t.string "from_region"
    t.string "from_town"
    t.string "from_website"
    t.string "to_address_line_1"
    t.string "to_address_line_2"
    t.string "to_address_line_3"
    t.string "to_county"
    t.string "to_name"
    t.string "to_postcode"
    t.string "to_region"
    t.string "to_town"
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["import_source", "import_id"], name: "index_letters_on_import_source_and_import_id", unique: true
    t.index ["patient_id"], name: "index_letters_on_patient_id"
    t.index ["practice_id"], name: "index_letters_on_practice_id"
    t.index ["signable_document_id"], name: "index_letters_on_signable_document_id"
    t.index ["user_id"], name: "index_letters_on_user_id"
  end

  create_table "medical_histories", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "created_by_id", null: false
    t.text "ai_summary"
    t.text "our_comments"
    t.text "patient_comments"
    t.text "additional_information"
    t.boolean "ai_alerts_done", default: false
    t.boolean "ai_changes_done", default: false
    t.boolean "ai_summary_done", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "key"
    t.jsonb "data", default: {}
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.index ["created_by_id"], name: "index_medical_histories_on_created_by_id"
    t.index ["import_source", "import_id"], name: "index_medical_histories_on_import_source_and_import_id", unique: true
    t.index ["key"], name: "index_medical_histories_on_key", unique: true
    t.index ["patient_id"], name: "index_medical_histories_on_patient_id"
  end

  create_table "medical_histories_medical_history_answers", force: :cascade do |t|
    t.bigint "medical_history_id", null: false
    t.bigint "medical_history_answer_id", null: false
    t.string "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["medical_history_answer_id"], name: "idx_on_medical_history_answer_id_6b9654b3a2"
    t.index ["medical_history_id"], name: "idx_on_medical_history_id_a2707124a2"
  end

  create_table "medical_history_ai_alerts", force: :cascade do |t|
    t.bigint "medical_history_id", null: false
    t.text "alert_text"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "question_id"
    t.index ["medical_history_id"], name: "index_medical_history_ai_alerts_on_medical_history_id"
    t.index ["question_id"], name: "index_medical_history_ai_alerts_on_question_id"
  end

  create_table "medical_history_ai_changes", force: :cascade do |t|
    t.bigint "medical_history_id", null: false
    t.text "change_text"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "question_id"
    t.index ["medical_history_id"], name: "index_medical_history_ai_changes_on_medical_history_id"
    t.index ["question_id"], name: "index_medical_history_ai_changes_on_question_id"
  end

  create_table "medical_history_answers", force: :cascade do |t|
    t.bigint "medical_history_question_id", null: false
    t.string "answer"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["medical_history_question_id"], name: "index_medical_history_answers_on_medical_history_question_id"
  end

  create_table "medical_history_questions", force: :cascade do |t|
    t.string "question"
    t.string "description"
    t.integer "question_type", default: 0
    t.integer "position"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "medical_history_answer_id"
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.index ["import_source", "import_id"], name: "index_medical_history_questions_on_import_source_and_import_id", unique: true
    t.index ["medical_history_answer_id"], name: "index_medical_history_questions_on_medical_history_answer_id"
    t.index ["practice_id"], name: "index_medical_history_questions_on_practice_id"
  end

  create_table "medications", force: :cascade do |t|
    t.string "name"
    t.string "nhs_url"
    t.json "nhs_info"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "code"
    t.string "brand_name"
  end

  create_table "meeting_comments", force: :cascade do |t|
    t.text "content"
    t.bigint "meeting_id"
    t.bigint "practice_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["meeting_id"], name: "index_meeting_comments_on_meeting_id"
    t.index ["practice_id"], name: "index_meeting_comments_on_practice_id"
    t.index ["user_id"], name: "index_meeting_comments_on_user_id"
  end

  create_table "meetings", force: :cascade do |t|
    t.bigint "created_by_id"
    t.boolean "staff"
    t.datetime "end"
    t.datetime "start"
    t.json "meeting_participants", default: {}
    t.json "users", default: []
    t.string "meeting_id"
    t.string "public_id"
    t.text "transcript"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "practice_id"
    t.index ["created_by_id"], name: "index_meetings_on_created_by_id"
    t.index ["practice_id"], name: "index_meetings_on_practice_id"
  end

  create_table "message_read_receipts", force: :cascade do |t|
    t.bigint "conversation_message_id", null: false
    t.bigint "user_id", null: false
    t.datetime "read_at", default: -> { "CURRENT_TIMESTAMP" }, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["conversation_message_id", "user_id"], name: "idx_on_conversation_message_id_user_id_445985fc88", unique: true
    t.index ["user_id"], name: "index_message_read_receipts_on_user_id"
  end

  create_table "nhs_contracts", force: :cascade do |t|
    t.bigint "practice_id", null: false
    t.string "name"
    t.string "nhs_username"
    t.string "nhs_password"
    t.string "nhs_site_id"
    t.string "nhs_location_id"
    t.string "contract_number"
    t.string "uda_target"
    t.decimal "uda_value"
    t.date "start"
    t.integer "expiry"
    t.boolean "pds_plus", default: false
    t.text "notes"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "jurisdiction"
    t.integer "sequential_serial_number_from"
    t.integer "message_reference_number_sequence_from"
    t.index ["practice_id"], name: "index_nhs_contracts_on_practice_id"
  end

  create_table "nhs_exemptions", force: :cascade do |t|
    t.string "exemption"
    t.boolean "evidence_seen", default: false
    t.date "expires"
    t.date "added"
    t.string "added_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.text "supporting_details"
    t.bigint "practice_id"
    t.string "dentally_id"
    t.index ["practice_id"], name: "index_nhs_exemptions_on_practice_id"
    t.index ["user_id"], name: "index_nhs_exemptions_on_user_id"
  end

  create_table "nhs_treatment_categories", force: :cascade do |t|
    t.string "treatment_categories"
    t.string "code"
    t.string "band"
    t.boolean "noins"
    t.integer "noin_min"
    t.integer "noin_max"
    t.decimal "uda_if_present_eng"
    t.decimal "uda_if_present_wales"
    t.boolean "multiples_england"
    t.boolean "multiples_wales"
    t.boolean "multiples_iom"
    t.integer "multiple_more_than"
    t.decimal "multiple_uda_value"
    t.boolean "applicable_to_england"
    t.boolean "applicable_to_wales"
    t.boolean "applicable_to_iom"
    t.string "cannot_combine"
    t.string "must_be_present_if_code_is_present"
    t.string "must_be_present_if_code_is_not_present"
    t.string "remove_if_present"
    t.string "remove_if_not_present"
    t.boolean "ver_checked_on_entry"
    t.string "mandatory_if_age_under"
    t.string "mandatory_if_age_over"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "multiple_combine"
    t.boolean "mandatory_if_code_is_present", default: false
    t.boolean "mandatory_if_code_is_not_present", default: false
    t.boolean "gets_dropped", default: false
  end

  create_table "nhs_udas", force: :cascade do |t|
    t.string "band"
    t.string "uda_value"
    t.date "date_from"
    t.date "date_to"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "no_end_date", default: false
    t.string "region"
    t.string "regulation_11_30"
    t.string "regulation_11_60"
    t.string "patient_price_england"
    t.string "patient_price_wales"
    t.string "patient_price_iom"
  end

  create_table "notification_templates", force: :cascade do |t|
    t.bigint "practice_id", null: false
    t.string "name"
    t.string "subject"
    t.text "text"
    t.string "section"
    t.boolean "default", default: false
    t.string "delivery_method"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "title", default: ""
    t.index ["practice_id"], name: "index_notification_templates_on_practice_id"
  end

  create_table "notifications", force: :cascade do |t|
    t.string "recipient_type", null: false
    t.bigint "recipient_id", null: false
    t.string "title", null: false
    t.text "description"
    t.string "icon"
    t.string "badge"
    t.text "actions"
    t.datetime "read_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "highlight_after"
    t.integer "shake_every"
    t.boolean "persistent", default: false
    t.boolean "waiting_room", default: false
    t.datetime "scheduled_for"
    t.jsonb "data", default: {}, null: false
    t.index ["recipient_type", "recipient_id"], name: "index_notifications_on_recipient"
    t.index ["recipient_type", "recipient_id"], name: "index_notifications_on_recipient_type_and_recipient_id"
    t.index ["scheduled_for"], name: "index_notifications_on_scheduled_for"
  end

  create_table "onboarding_answers", force: :cascade do |t|
    t.text "content"
    t.bigint "onboarding_question_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["onboarding_question_id"], name: "index_onboarding_answers_on_onboarding_question_id"
  end

  create_table "onboarding_forms", force: :cascade do |t|
    t.boolean "approval_required", default: false
    t.boolean "recurring", default: false
    t.boolean "signature_required", default: false
    t.integer "document_category", default: 0, null: false
    t.integer "form_type", default: 0
    t.integer "recurring_every"
    t.integer "recurring_period", default: 0, null: false
    t.string "title"
    t.bigint "user_id", null: false
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_onboarding_forms_on_practice_id"
    t.index ["user_id"], name: "index_onboarding_forms_on_user_id"
  end

  create_table "onboarding_questions", force: :cascade do |t|
    t.text "content"
    t.string "field_type", default: "text"
    t.bigint "onboarding_form_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["onboarding_form_id"], name: "index_onboarding_questions_on_onboarding_form_id"
  end

  create_table "opgs", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "course_of_treatment_id"
    t.json "teeth", default: []
    t.text "justification"
    t.date "date_taken"
    t.text "description"
    t.bigint "developed_by_id", null: false
    t.bigint "taken_by_id", null: false
    t.text "grade"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "charting_appointment_id"
    t.index ["charting_appointment_id"], name: "index_opgs_on_charting_appointment_id"
    t.index ["course_of_treatment_id"], name: "index_opgs_on_course_of_treatment_id"
    t.index ["developed_by_id"], name: "index_opgs_on_developed_by_id"
    t.index ["patient_id"], name: "index_opgs_on_patient_id"
    t.index ["taken_by_id"], name: "index_opgs_on_taken_by_id"
  end

  create_table "patient_alerts", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "alert_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["alert_id"], name: "index_patient_alerts_on_alert_id"
    t.index ["patient_id", "alert_id"], name: "index_patient_alerts_on_patient_id_and_alert_id", unique: true
    t.index ["patient_id"], name: "index_patient_alerts_on_patient_id"
  end

  create_table "patient_asset_labels", force: :cascade do |t|
    t.string "label"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "patient_assets", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.string "assignable_type"
    t.bigint "assignable_id"
    t.string "label"
    t.boolean "favourite", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "deleted_at"
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.bigint "medical_history_id"
    t.boolean "archived", default: false
    t.bigint "lab_docket_id"
    t.index ["assignable_type", "assignable_id"], name: "index_patient_assets_on_assignable"
    t.index ["deleted_at"], name: "index_patient_assets_on_deleted_at"
    t.index ["import_source", "import_id"], name: "index_patient_assets_on_import_source_and_import_id", unique: true
    t.index ["lab_docket_id"], name: "index_patient_assets_on_lab_docket_id"
    t.index ["medical_history_id"], name: "index_patient_assets_on_medical_history_id"
    t.index ["patient_id"], name: "index_patient_assets_on_patient_id"
  end

  create_table "patient_cot_payment_plans", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "cot_payment_plan_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cot_payment_plan_id"], name: "index_patient_cot_payment_plans_on_cot_payment_plan_id"
    t.index ["patient_id", "cot_payment_plan_id"], name: "idx_on_patient_id_cot_payment_plan_id_73c7979687", unique: true
    t.index ["patient_id"], name: "index_patient_cot_payment_plans_on_patient_id"
  end

  create_table "patient_gps", force: :cascade do |t|
    t.string "name"
    t.string "practice_name"
    t.string "address_line_1"
    t.string "address_line_2"
    t.string "city"
    t.string "county"
    t.string "postcode"
    t.string "country", default: "GB"
    t.bigint "practice_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_patient_gps_on_practice_id"
  end

  create_table "patient_notes", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.integer "user_id", null: false
    t.bigint "patient_note_id"
    t.string "title"
    t.text "text"
    t.string "color", default: "yellow"
    t.boolean "pinned", default: false
    t.boolean "updated", default: false
    t.boolean "archived", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.index ["import_source", "import_id"], name: "index_patient_notes_on_import_source_and_import_id", unique: true
    t.index ["patient_id"], name: "index_patient_notes_on_patient_id"
    t.index ["patient_note_id"], name: "index_patient_notes_on_patient_note_id"
    t.index ["user_id"], name: "index_patient_notes_on_user_id"
  end

  create_table "patients", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.string "middle_name"
    t.string "email"
    t.string "title"
    t.string "previous_last_name"
    t.string "preferred_name"
    t.date "date_of_birth"
    t.string "ethnicity"
    t.string "biological_sex"
    t.string "gender"
    t.string "pronouns"
    t.string "ni_number"
    t.string "insurance_number"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "mobile_phone"
    t.string "postcode"
    t.boolean "sms_consent", default: false
    t.boolean "email_consent", default: false
    t.boolean "whatsapp_consent", default: false
    t.boolean "instagram_consent", default: false
    t.boolean "facebook_consent", default: false
    t.boolean "marketing_consent", default: false
    t.boolean "hide_base_and_history_treatments", default: false
    t.string "treatment_plan_code"
    t.string "treatment_plan_verification_code"
    t.string "address_line_1"
    t.string "address_line_2"
    t.string "city"
    t.string "county"
    t.string "country", default: "GB"
    t.string "pin"
    t.string "encrypted_password", default: ""
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.boolean "force_password_reset", default: false
    t.string "email_verification_code"
    t.string "school_name"
    t.string "school_phone_number"
    t.string "school_address_line_1"
    t.string "school_address_line_2"
    t.string "school_county"
    t.string "school_country", default: "GB"
    t.string "school_city"
    t.string "school_postcode"
    t.bigint "patient_gp_id"
    t.boolean "archived", default: false, null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.decimal "opening_balance", precision: 10, scale: 2, default: "0.0"
    t.bigint "active_cot_payment_plan_id"
    t.string "diagnocat_id"
    t.bigint "current_practice_id"
    t.datetime "consent_updated_at"
    t.string "emergency_contact_name"
    t.string "emergency_contact_number"
    t.string "alternative_phone"
    t.string "work_phone"
    t.boolean "temporary", default: false
    t.integer "created_by_id"
    t.boolean "on_stop", default: false, null: false
    t.datetime "secure_code_sent_at"
    t.datetime "entered_into_portal_at"
    t.string "verification_method", default: ""
    t.bigint "witnessed_by_id"
    t.string "occupation"
    t.index ["active_cot_payment_plan_id"], name: "index_patients_on_active_cot_payment_plan_id"
    t.index ["archived"], name: "index_patients_on_archived"
    t.index ["current_practice_id"], name: "index_patients_on_current_practice_id"
    t.index ["import_source", "import_id"], name: "index_patients_on_import_source_and_import_id", unique: true
    t.index ["on_stop"], name: "index_patients_on_on_stop"
    t.index ["patient_gp_id"], name: "index_patients_on_patient_gp_id"
    t.index ["reset_password_token"], name: "index_patients_on_reset_password_token", unique: true
    t.index ["temporary"], name: "index_patients_on_temporary"
    t.index ["witnessed_by_id"], name: "index_patients_on_witnessed_by_id"
  end

  create_table "patients_users", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["patient_id"], name: "index_patients_users_on_patient_id"
    t.index ["user_id"], name: "index_patients_users_on_user_id"
  end

  create_table "payment_plans", force: :cascade do |t|
    t.string "plan_type"
    t.string "frequency"
    t.integer "weekly_day"
    t.text "notes"
    t.bigint "patient_id", null: false
    t.string "main_payment_method_id"
    t.string "backup_payment_method_id"
    t.bigint "practice_id"
    t.bigint "course_of_treatment_id"
    t.bigint "invoice_id"
    t.boolean "cancelled", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["course_of_treatment_id"], name: "index_payment_plans_on_course_of_treatment_id"
    t.index ["invoice_id"], name: "index_payment_plans_on_invoice_id"
    t.index ["patient_id"], name: "index_payment_plans_on_patient_id"
    t.index ["practice_id"], name: "index_payment_plans_on_practice_id"
  end

  create_table "payments", force: :cascade do |t|
    t.bigint "invoice_id"
    t.bigint "patient_id", null: false
    t.bigint "course_of_treatment_id"
    t.decimal "amount", precision: 10, scale: 2
    t.string "payment_method"
    t.string "status"
    t.string "payment_type"
    t.string "stripe_payment_intent_id"
    t.text "summary"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "payment_date"
    t.string "name"
    t.string "currency", default: "GBP"
    t.bigint "transactioned_by_id"
    t.string "payment_mean"
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.bigint "payment_plan_id"
    t.datetime "due_date"
    t.bigint "calendar_booking_id"
    t.index ["calendar_booking_id"], name: "index_payments_on_calendar_booking_id"
    t.index ["course_of_treatment_id"], name: "index_payments_on_course_of_treatment_id"
    t.index ["import_source", "import_id"], name: "index_payments_on_import_source_and_import_id", unique: true
    t.index ["invoice_id"], name: "index_payments_on_invoice_id"
    t.index ["patient_id"], name: "index_payments_on_patient_id"
    t.index ["payment_plan_id"], name: "index_payments_on_payment_plan_id"
    t.index ["transactioned_by_id"], name: "index_payments_on_transactioned_by_id"
  end

  create_table "perio_alerts", force: :cascade do |t|
    t.bigint "perio_exam_id", null: false
    t.string "title"
    t.text "description"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["perio_exam_id"], name: "index_perio_alerts_on_perio_exam_id"
    t.index ["practice_id"], name: "index_perio_alerts_on_practice_id"
  end

  create_table "perio_changes", force: :cascade do |t|
    t.bigint "perio_exam_id", null: false
    t.text "description"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["perio_exam_id"], name: "index_perio_changes_on_perio_exam_id"
    t.index ["practice_id"], name: "index_perio_changes_on_practice_id"
  end

  create_table "perio_exams", force: :cascade do |t|
    t.json "six_packet_data"
    t.json "plaque_and_bleeding_data"
    t.bigint "patient_id", null: false
    t.bigint "provided_by_id"
    t.text "summary"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.index ["import_source", "import_id"], name: "index_perio_exams_on_import_source_and_import_id", unique: true
    t.index ["patient_id"], name: "index_perio_exams_on_patient_id"
    t.index ["practice_id"], name: "index_perio_exams_on_practice_id"
    t.index ["provided_by_id"], name: "index_perio_exams_on_provided_by_id"
  end

  create_table "practices", force: :cascade do |t|
    t.string "name"
    t.string "email"
    t.string "phone"
    t.string "address_line_1"
    t.string "address_line_2"
    t.string "city"
    t.string "postcode"
    t.string "primary_color"
    t.string "secondary_color"
    t.string "tertiary_color"
    t.integer "num_surgeries"
    t.string "website"
    t.decimal "latitude"
    t.decimal "longitude"
    t.json "opening_hours", default: {}
    t.string "facebook_page_id"
    t.string "facebook_page_token"
    t.string "pandadoc_key"
    t.string "azure_url"
    t.string "azure_token"
    t.string "stripe_account_id"
    t.datetime "stripe_tos_accepted_at"
    t.string "stripe_tos_accepted_ip"
    t.string "stannp_key"
    t.string "sinch_project_id"
    t.string "sinch_app_id"
    t.string "outbound_email"
    t.string "medical_history_success_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "facilities", default: []
    t.text "about_us"
    t.text "expertise"
    t.boolean "two_factor_enabled", default: true
    t.json "two_factor_disabled_data"
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.integer "number_of_surgeries"
    t.index ["import_source", "import_id"], name: "index_practices_on_import_source_and_import_id", unique: true
  end

  create_table "practices_patients", force: :cascade do |t|
    t.bigint "practice_id", null: false
    t.bigint "patient_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "stripe_customer_id"
    t.index ["patient_id"], name: "index_practices_patients_on_patient_id"
    t.index ["practice_id", "patient_id"], name: "index_practices_patients_on_practice_id_and_patient_id", unique: true
    t.index ["practice_id"], name: "index_practices_patients_on_practice_id"
  end

  create_table "practices_users", force: :cascade do |t|
    t.bigint "practice_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id", "user_id"], name: "index_practices_users_on_practice_id_and_user_id", unique: true
    t.index ["practice_id"], name: "index_practices_users_on_practice_id"
    t.index ["user_id"], name: "index_practices_users_on_user_id"
  end

  create_table "prescription_medications", force: :cascade do |t|
    t.bigint "prescription_id", null: false
    t.bigint "medication_id", null: false
    t.string "dosage"
    t.integer "days"
    t.string "times_per_day"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["medication_id"], name: "index_prescription_medications_on_medication_id"
    t.index ["prescription_id"], name: "index_prescription_medications_on_prescription_id"
  end

  create_table "prescriptions", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "dentist_id", null: false
    t.date "date"
    t.text "details"
    t.bigint "practice_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "browser_data"
    t.datetime "signed_at"
    t.index ["dentist_id"], name: "index_prescriptions_on_dentist_id"
    t.index ["patient_id"], name: "index_prescriptions_on_patient_id"
    t.index ["practice_id"], name: "index_prescriptions_on_practice_id"
  end

  create_table "recall_dentists", force: :cascade do |t|
    t.bigint "recall_id", null: false
    t.bigint "dentist_id", null: false
    t.index ["dentist_id"], name: "index_recall_dentists_on_dentist_id"
    t.index ["recall_id"], name: "index_recall_dentists_on_recall_id"
  end

  create_table "recall_schedules", force: :cascade do |t|
    t.datetime "recurrence_start_date", null: false
    t.datetime "recurrence_end_date"
    t.boolean "repeat_indefinitely", default: false, null: false
    t.integer "interval", null: false
    t.integer "interval_unit", default: 0, null: false
    t.decimal "price", precision: 10, scale: 2
    t.integer "duration"
    t.text "notes"
    t.bigint "created_by_id"
    t.bigint "patient_id", null: false
    t.bigint "practice_id", null: false
    t.bigint "cot_payment_plan_id"
    t.bigint "treatment_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cot_payment_plan_id"], name: "index_recall_schedules_on_cot_payment_plan_id"
    t.index ["created_by_id"], name: "index_recall_schedules_on_created_by_id"
    t.index ["patient_id"], name: "index_recall_schedules_on_patient_id"
    t.index ["practice_id"], name: "index_recall_schedules_on_practice_id"
    t.index ["treatment_id"], name: "index_recall_schedules_on_treatment_id"
  end

  create_table "recall_schedules_dentists", force: :cascade do |t|
    t.bigint "recall_schedule_id", null: false
    t.bigint "dentist_id", null: false
    t.index ["dentist_id"], name: "index_recall_schedules_dentists_on_dentist_id"
    t.index ["recall_schedule_id"], name: "index_recall_schedules_dentists_on_recall_schedule_id"
  end

  create_table "recalls", force: :cascade do |t|
    t.datetime "due_date"
    t.datetime "patient_reminder_sent_at"
    t.decimal "price", precision: 10, scale: 2
    t.integer "duration"
    t.text "notes"
    t.bigint "created_by_id"
    t.bigint "patient_id", null: false
    t.bigint "practice_id"
    t.bigint "cot_payment_plan_id"
    t.bigint "calendar_booking_id"
    t.bigint "treatment_id"
    t.bigint "recall_schedule_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.index ["calendar_booking_id"], name: "index_recalls_on_calendar_booking_id"
    t.index ["cot_payment_plan_id"], name: "index_recalls_on_cot_payment_plan_id"
    t.index ["created_by_id"], name: "index_recalls_on_created_by_id"
    t.index ["import_source", "import_id"], name: "index_recalls_on_import_source_and_import_id", unique: true
    t.index ["patient_id"], name: "index_recalls_on_patient_id"
    t.index ["practice_id"], name: "index_recalls_on_practice_id"
    t.index ["recall_schedule_id"], name: "index_recalls_on_recall_schedule_id"
    t.index ["treatment_id"], name: "index_recalls_on_treatment_id"
  end

  create_table "refunds", force: :cascade do |t|
    t.decimal "amount"
    t.bigint "payment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "stripe_refund_id"
    t.index ["payment_id"], name: "index_refunds_on_payment_id"
  end

  create_table "registered_devices", force: :cascade do |t|
    t.bigint "patient_id"
    t.string "name", null: false
    t.text "device_signature", null: false
    t.string "device_token", null: false
    t.datetime "last_used_at"
    t.boolean "active", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "practice_id"
    t.string "status", default: "available"
    t.index ["device_token"], name: "index_registered_devices_on_device_token", unique: true
    t.index ["patient_id", "device_signature"], name: "index_registered_devices_on_patient_and_signature", unique: true
    t.index ["patient_id"], name: "index_registered_devices_on_patient_id"
    t.index ["practice_id"], name: "index_registered_devices_on_practice_id"
  end

  create_table "roles", force: :cascade do |t|
    t.string "name", null: false
    t.boolean "admin"
    t.integer "permissions", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "roles_users", force: :cascade do |t|
    t.bigint "role_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["role_id"], name: "index_roles_users_on_role_id"
    t.index ["user_id"], name: "index_roles_users_on_user_id"
  end

  create_table "shift_intervals", force: :cascade do |t|
    t.bigint "shift_id"
    t.integer "interval_type", default: 0, null: false
    t.datetime "started_at"
    t.datetime "ended_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["shift_id"], name: "index_shift_intervals_on_shift_id"
  end

  create_table "shifts", force: :cascade do |t|
    t.boolean "repeat_indefinitely", default: false
    t.boolean "repeat_shift", default: false
    t.date "end_repeat_date"
    t.date "date"
    t.datetime "event_start"
    t.datetime "event_end"
    t.integer "surgery_id"
    t.integer "user_id"
    t.integer "working_time_adjustment"
    t.integer "break_time_adjustment"
    t.integer "overtime_adjustment"
    t.string "description"
    t.string "recurrent_identifier"
    t.string "working_location"
    t.string "event_type"
    t.string "custom_description"
    t.string "status", default: "scheduled"
    t.string "repeat_days", default: [], array: true
    t.string "alternating_shift"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "shift_type", default: 0, null: false
    t.index ["event_end"], name: "index_shifts_on_event_end"
    t.index ["event_start"], name: "index_shifts_on_event_start"
    t.index ["event_type"], name: "index_shifts_on_event_type"
    t.index ["practice_id"], name: "index_shifts_on_practice_id"
    t.index ["status"], name: "index_shifts_on_status"
    t.index ["user_id"], name: "index_shifts_on_user_id"
  end

  create_table "signable_documents", force: :cascade do |t|
    t.string "title"
    t.string "document_type"
    t.bigint "patient_id", null: false
    t.bigint "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "document_template_id"
    t.text "document_html"
    t.bigint "practice_id"
    t.bigint "treatment_plan_option_id"
    t.index ["created_by_id"], name: "index_signable_documents_on_created_by_id"
    t.index ["document_template_id"], name: "index_signable_documents_on_document_template_id"
    t.index ["patient_id"], name: "index_signable_documents_on_patient_id"
    t.index ["practice_id"], name: "index_signable_documents_on_practice_id"
    t.index ["treatment_plan_option_id"], name: "index_signable_documents_on_treatment_plan_option_id"
  end

  create_table "signature_requests", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "signable_document_id", null: false
    t.bigint "created_by_id", null: false
    t.string "status", default: "pending"
    t.datetime "expires_at"
    t.string "verification_method"
    t.string "access_token"
    t.boolean "security_questions_setup", default: false
    t.datetime "completed_at"
    t.string "ip_address"
    t.string "user_agent"
    t.string "device_type"
    t.string "os_name"
    t.string "os_version"
    t.string "browser_name"
    t.string "browser_version"
    t.string "sinch_fingerprint"
    t.boolean "staff_override", default: false
    t.bigint "witnessed_by_id"
    t.string "signature_type"
    t.string "signed_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "notification_sent_at"
    t.json "date_fields", default: []
    t.bigint "sent_by_id"
    t.datetime "sent_at"
    t.boolean "viewed", default: false
    t.datetime "due_by"
    t.bigint "charting_appointment_id"
    t.text "template_text"
    t.datetime "viewed_at"
    t.datetime "signed_at"
    t.index ["access_token"], name: "index_signature_requests_on_access_token", unique: true
    t.index ["charting_appointment_id"], name: "index_signature_requests_on_charting_appointment_id"
    t.index ["created_by_id"], name: "index_signature_requests_on_created_by_id"
    t.index ["patient_id"], name: "index_signature_requests_on_patient_id"
    t.index ["sent_by_id"], name: "index_signature_requests_on_sent_by_id"
    t.index ["signable_document_id"], name: "index_signature_requests_on_signable_document_id"
    t.index ["witnessed_by_id"], name: "index_signature_requests_on_witnessed_by_id"
  end

  create_table "signature_verifications", force: :cascade do |t|
    t.bigint "signature_request_id", null: false
    t.string "verification_type"
    t.boolean "successful"
    t.jsonb "verification_data"
    t.datetime "verified_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["signature_request_id"], name: "index_signature_verifications_on_signature_request_id"
  end

  create_table "sinch_numbers", force: :cascade do |t|
    t.string "number"
    t.bigint "practice_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_sinch_numbers_on_practice_id"
  end

  create_table "site_settings", force: :cascade do |t|
    t.string "site_name"
    t.string "brand_color", default: "#53D5E9"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "use_bank_holidays", default: false, null: false
    t.boolean "multisite", default: false
    t.datetime "facebook_expires_at"
    t.datetime "microsoft_expires_at"
    t.integer "point_expiry_months"
    t.integer "stock_holding_time"
    t.integer "voicemail_wait_time"
    t.json "working_days", default: "[]"
    t.string "contact_email"
    t.string "contact_phone_number"
    t.string "email_footer_style"
    t.string "email_footer_tagline"
    t.string "facebook"
    t.string "facebook_access_token"
    t.string "facebook_refresh_token"
    t.string "facebook_token"
    t.string "google_reviews_account_id"
    t.string "google_reviews_location_id"
    t.string "instagram"
    t.string "inventory_verification_option"
    t.string "microsoft_access_token"
    t.string "microsoft_refresh_token"
    t.string "tiktok"
    t.string "youtube"
    t.text "contact_address"
  end

  create_table "stls", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "taken_by_id", null: false
    t.bigint "developed_by_id"
    t.bigint "charting_appointment_id"
    t.bigint "course_of_treatment_id"
    t.date "date_taken"
    t.string "grade"
    t.string "justification"
    t.text "description"
    t.string "tooth_position"
    t.json "teeth"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["charting_appointment_id"], name: "index_stls_on_charting_appointment_id"
    t.index ["course_of_treatment_id"], name: "index_stls_on_course_of_treatment_id"
    t.index ["developed_by_id"], name: "index_stls_on_developed_by_id"
    t.index ["patient_id"], name: "index_stls_on_patient_id"
    t.index ["taken_by_id"], name: "index_stls_on_taken_by_id"
  end

  create_table "task_comments", force: :cascade do |t|
    t.integer "lead_id"
    t.json "mentions", default: [], null: false
    t.json "dismissed_by", default: []
    t.string "comment"
    t.string "customer_id"
    t.string "ancestry"
    t.string "title"
    t.string "asset_folder_id"
    t.string "asset_file_id"
    t.text "assigned_users", default: "[]"
    t.bigint "practice_id"
    t.bigint "task_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ancestry"], name: "index_task_comments_on_ancestry"
    t.index ["practice_id"], name: "index_task_comments_on_practice_id"
    t.index ["task_id"], name: "index_task_comments_on_task_id"
    t.index ["user_id"], name: "index_task_comments_on_user_id"
  end

  create_table "tasks", force: :cascade do |t|
    t.datetime "date_due", precision: nil
    t.integer "holiday_id"
    t.integer "lead_id"
    t.string "asset_file_id"
    t.string "asset_folder_id"
    t.string "additional_users"
    t.string "contact_id"
    t.string "created_by_id"
    t.string "customer_id"
    t.string "description"
    t.string "order_id"
    t.string "support_ticket_id"
    t.string "status"
    t.string "task_priority"
    t.string "task_type"
    t.string "title"
    t.string "user_id"
    t.bigint "document_id"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.index ["document_id"], name: "index_tasks_on_document_id"
    t.index ["import_source", "import_id"], name: "index_tasks_on_import_source_and_import_id", unique: true
    t.index ["practice_id"], name: "index_tasks_on_practice_id"
    t.index ["task_type", "status", "customer_id"], name: "index_tasks_on_task_type_and_status_and_customer_id"
  end

  create_table "treatment_categories", force: :cascade do |t|
    t.string "name"
    t.integer "position"
    t.bigint "practice_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.index ["import_source", "import_id"], name: "index_treatment_categories_on_import_source_and_import_id", unique: true
    t.index ["practice_id"], name: "index_treatment_categories_on_practice_id"
  end

  create_table "treatment_plan_form_treatments", force: :cascade do |t|
    t.bigint "treatment_plan_form_id", null: false
    t.bigint "treatment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["treatment_id"], name: "index_treatment_plan_form_treatments_on_treatment_id"
    t.index ["treatment_plan_form_id"], name: "index_treatment_plan_form_treatments_on_treatment_plan_form_id"
  end

  create_table "treatment_plan_forms", force: :cascade do |t|
    t.string "name"
    t.text "text"
    t.bigint "practice_id"
    t.string "form_type", default: "Patient Consent"
    t.datetime "archived_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_treatment_plan_forms_on_practice_id"
  end

  create_table "treatment_plan_information_treatments", force: :cascade do |t|
    t.bigint "treatment_plan_information_id", null: false
    t.bigint "treatment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["treatment_id"], name: "index_treatment_plan_information_treatments_on_treatment_id"
    t.index ["treatment_plan_information_id"], name: "idx_on_treatment_plan_information_id_71319eb3ba"
  end

  create_table "treatment_plan_informations", force: :cascade do |t|
    t.string "name"
    t.text "before_text", default: ""
    t.text "after_text", default: ""
    t.bigint "practice_id"
    t.text "overall_text", default: ""
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "template_type"
    t.index ["practice_id"], name: "index_treatment_plan_informations_on_practice_id"
  end

  create_table "treatment_plan_options", force: :cascade do |t|
    t.bigint "patient_id"
    t.text "reason"
    t.text "services_provided"
    t.text "outcome"
    t.string "status", default: "New"
    t.datetime "signed_at"
    t.text "oral_health_notes"
    t.bigint "created_by_id"
    t.text "proposed_course_of_treatment_notes"
    t.bigint "treatment_coordinator_id"
    t.bigint "main_clinician_id"
    t.datetime "sent_at"
    t.string "pandadoc_id"
    t.bigint "treatment_plan_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "team_member_ids", default: []
    t.json "treatment_plan_information_ids", default: []
    t.json "treatment_plan_form_ids", default: []
    t.json "treatment_plan_story_ids", default: []
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.bigint "course_of_treatment_id"
    t.string "title"
    t.bigint "cot_category_id"
    t.boolean "draft", default: true, null: false
    t.string "signature_type"
    t.string "signature_text"
    t.jsonb "charted_treatment_prices", default: {}
    t.datetime "viewed_at"
    t.index ["cot_category_id"], name: "index_treatment_plan_options_on_cot_category_id"
    t.index ["course_of_treatment_id"], name: "index_treatment_plan_options_on_course_of_treatment_id"
    t.index ["created_by_id"], name: "index_treatment_plan_options_on_created_by_id"
    t.index ["import_source", "import_id"], name: "index_treatment_plan_options_on_import_source_and_import_id", unique: true
    t.index ["main_clinician_id"], name: "index_treatment_plan_options_on_main_clinician_id"
    t.index ["patient_id"], name: "index_treatment_plan_options_on_patient_id"
    t.index ["treatment_coordinator_id"], name: "index_treatment_plan_options_on_treatment_coordinator_id"
    t.index ["treatment_plan_id"], name: "index_treatment_plan_options_on_treatment_plan_id"
  end

  create_table "treatment_plan_stories", force: :cascade do |t|
    t.string "name"
    t.text "before_text"
    t.text "after_text"
    t.string "patient_name"
    t.text "patient_quote"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["practice_id"], name: "index_treatment_plan_stories_on_practice_id"
  end

  create_table "treatment_plan_templates", force: :cascade do |t|
    t.string "name"
    t.integer "template_type", default: 0, null: false
    t.text "text"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.index ["practice_id"], name: "index_treatment_plan_templates_on_practice_id"
    t.index ["user_id"], name: "index_treatment_plan_templates_on_user_id"
  end

  create_table "treatment_plans", force: :cascade do |t|
    t.string "name"
    t.bigint "practice_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.integer "status", default: 0
    t.datetime "signature_date"
    t.index ["import_source", "import_id"], name: "index_treatment_plans_on_import_source_and_import_id", unique: true
    t.index ["practice_id"], name: "index_treatment_plans_on_practice_id"
  end

  create_table "treatments", force: :cascade do |t|
    t.string "code"
    t.string "description"
    t.string "nomenclature"
    t.text "patient_description"
    t.text "notes"
    t.text "staff_description"
    t.string "patient_friendly_name"
    t.string "region"
    t.boolean "appear_on_invoice", default: false
    t.boolean "missing_tooth", default: false
    t.boolean "base_chart_list", default: false
    t.boolean "remove_treatment_when_completed", default: false
    t.boolean "remove_tooth_when_completed", default: false
    t.string "treatment_folder"
    t.integer "duration"
    t.boolean "bridge_treatment", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "treatment_category_id"
    t.bigint "practice_id"
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.boolean "available_for_recall", default: false, null: false
    t.index ["import_source", "import_id"], name: "index_treatments_on_import_source_and_import_id", unique: true
    t.index ["practice_id"], name: "index_treatments_on_practice_id"
    t.index ["treatment_category_id"], name: "index_treatments_on_treatment_category_id"
  end

  create_table "user_favorite_reports", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "report_path", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "report_path"], name: "index_user_favorite_reports_on_user_id_and_report_path", unique: true
    t.index ["user_id"], name: "index_user_favorite_reports_on_user_id"
  end

  create_table "user_favorite_treatments", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "treatment_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["treatment_id"], name: "index_user_favorite_treatments_on_treatment_id"
    t.index ["user_id", "treatment_id"], name: "index_user_favorite_treatments_on_user_id_and_treatment_id", unique: true
    t.index ["user_id"], name: "index_user_favorite_treatments_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "provider", default: "email", null: false
    t.string "uid", default: "", null: false
    t.text "tokens"
    t.string "first_name"
    t.string "last_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "archived", default: false
    t.string "mobile_phone"
    t.string "title"
    t.string "mfa_method", default: "SMS"
    t.string "pin"
    t.integer "initial_holiday_allowance"
    t.integer "holiday_year_start_month"
    t.boolean "breaks", default: false
    t.integer "break_duration"
    t.integer "break_interval"
    t.boolean "breaks_paid", default: false
    t.string "diagnocat_email"
    t.string "diagnocat_password"
    t.text "diagnocat_password_ciphertext"
    t.jsonb "login_restrictions", default: {"0"=>{"anytime"=>"1"}, "1"=>{"anytime"=>"1"}, "2"=>{"anytime"=>"1"}, "3"=>{"anytime"=>"1"}, "4"=>{"anytime"=>"1"}, "5"=>{"anytime"=>"1"}, "6"=>{"anytime"=>"1"}}
    t.boolean "accrue_holidays", default: false
    t.string "gdc_number"
    t.boolean "show_treatment_plans", default: true
    t.string "override_pin"
    t.boolean "auto_privacy_enabled", default: false
    t.boolean "auto_logout_enabled", default: false
    t.integer "privacy_timeout", default: 15
    t.integer "logout_timeout", default: 30
    t.string "address_line_1"
    t.string "address_line_2"
    t.string "import_id"
    t.integer "import_source"
    t.jsonb "import_data", default: {}
    t.string "online_booking_description"
    t.boolean "lab_user", default: false, null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["import_source", "import_id"], name: "index_users_on_import_source_and_import_id", unique: true
    t.index ["lab_user"], name: "index_users_on_lab_user"
  end

  create_table "whatsapp_buttons", force: :cascade do |t|
    t.bigint "whatsapp_component_id", null: false
    t.string "button_type"
    t.string "text"
    t.string "content"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["whatsapp_component_id"], name: "index_whatsapp_buttons_on_whatsapp_component_id"
  end

  create_table "whatsapp_components", force: :cascade do |t|
    t.bigint "whatsapp_template_id", null: false
    t.string "component_type"
    t.text "text"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["whatsapp_template_id"], name: "index_whatsapp_components_on_whatsapp_template_id"
  end

  create_table "whatsapp_templates", force: :cascade do |t|
    t.string "name"
    t.bigint "practice_id", null: false
    t.string "category"
    t.boolean "submitted", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "automations_only", default: false
    t.index ["name", "practice_id"], name: "index_whatsapp_templates_on_name_and_practice_id", unique: true
    t.index ["practice_id"], name: "index_whatsapp_templates_on_practice_id"
  end

  create_table "xrays", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "course_of_treatment_id"
    t.json "teeth", default: []
    t.text "justification"
    t.date "date_taken"
    t.text "description"
    t.bigint "developed_by_id", null: false
    t.bigint "taken_by_id", null: false
    t.text "grade"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "charting_appointment_id"
    t.index ["charting_appointment_id"], name: "index_xrays_on_charting_appointment_id"
    t.index ["course_of_treatment_id"], name: "index_xrays_on_course_of_treatment_id"
    t.index ["developed_by_id"], name: "index_xrays_on_developed_by_id"
    t.index ["patient_id"], name: "index_xrays_on_patient_id"
    t.index ["taken_by_id"], name: "index_xrays_on_taken_by_id"
  end

  add_foreign_key "actions", "calendar_bookings"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "alerts", "practices"
  add_foreign_key "announcements", "practices"
  add_foreign_key "announcements", "users", column: "author_id"
  add_foreign_key "appointment_notes", "calendar_bookings", on_delete: :cascade
  add_foreign_key "appointment_notes", "charting_appointments"
  add_foreign_key "appointment_notes", "users"
  add_foreign_key "appointment_notes", "users", column: "author_id"
  add_foreign_key "appointment_notes", "users", column: "nurse_id"
  add_foreign_key "appointment_notes", "users", column: "practitioner_id"
  add_foreign_key "automation_actions", "automations"
  add_foreign_key "automation_condition_values", "automation_conditions"
  add_foreign_key "automation_conditions", "automations"
  add_foreign_key "automation_triggers", "automations"
  add_foreign_key "automations", "practices"
  add_foreign_key "bank_holidays", "practices"
  add_foreign_key "base_charts", "patients"
  add_foreign_key "bewes", "users", column: "completed_by_id"
  add_foreign_key "bpes", "practices"
  add_foreign_key "bpes", "users", column: "completed_by_id"
  add_foreign_key "calendar_booking_cancellations", "calendar_bookings", column: "booking_id"
  add_foreign_key "calendar_bookings", "meetings"
  add_foreign_key "calendar_bookings", "patients"
  add_foreign_key "calendar_bookings", "practices"
  add_foreign_key "calendar_bookings", "treatments"
  add_foreign_key "calendar_bookings", "users", column: "practitioner_id"
  add_foreign_key "calendar_day_notes", "practices"
  add_foreign_key "calendar_day_notes", "users"
  add_foreign_key "calendar_reserved_block_types", "calendar_settings"
  add_foreign_key "calendar_reserved_block_types", "practices"
  add_foreign_key "calendar_reserved_block_types", "treatments"
  add_foreign_key "calendar_reserved_blocks", "calendar_reserved_block_types"
  add_foreign_key "calendar_reserved_blocks", "users", column: "practitioner_id"
  add_foreign_key "calendar_settings", "practices"
  add_foreign_key "card_activities", "crm_cards", column: "card_id"
  add_foreign_key "card_activities", "users"
  add_foreign_key "card_comments", "crm_cards", column: "card_id"
  add_foreign_key "card_comments", "users"
  add_foreign_key "cbcts", "course_of_treatments"
  add_foreign_key "cbcts", "patients"
  add_foreign_key "cbcts", "users", column: "developed_by_id"
  add_foreign_key "cbcts", "users", column: "taken_by_id"
  add_foreign_key "charted_treatments", "base_charts"
  add_foreign_key "charted_treatments", "charting_appointments"
  add_foreign_key "charted_treatments", "invoices", on_delete: :nullify
  add_foreign_key "charted_treatments", "patients", column: "referrer_id"
  add_foreign_key "charted_treatments", "treatments"
  add_foreign_key "charted_treatments", "users", column: "nurse_id"
  add_foreign_key "charted_treatments", "users", column: "practitioner_id"
  add_foreign_key "charting_appointments", "calendar_bookings"
  add_foreign_key "charting_appointments", "course_of_treatments"
  add_foreign_key "charting_appointments", "users", column: "dentist_id"
  add_foreign_key "charting_files", "charting_appointments"
  add_foreign_key "charting_files", "patients"
  add_foreign_key "charting_files", "users", column: "developed_by_id"
  add_foreign_key "charting_files", "users", column: "taken_by_id"
  add_foreign_key "clinical_images", "course_of_treatments"
  add_foreign_key "clinical_images", "patients"
  add_foreign_key "clinical_images", "users", column: "developed_by_id"
  add_foreign_key "clinical_images", "users", column: "taken_by_id"
  add_foreign_key "communication_account_users", "communication_accounts"
  add_foreign_key "communication_account_users", "users"
  add_foreign_key "communication_accounts", "practices"
  add_foreign_key "conversation_messages", "conversations"
  add_foreign_key "conversations", "practices"
  add_foreign_key "cot_categories", "practices"
  add_foreign_key "cot_payment_plans", "practices"
  add_foreign_key "cot_payment_plans_treatments", "cot_payment_plans"
  add_foreign_key "cot_payment_plans_treatments", "treatments"
  add_foreign_key "cot_template_note_users", "cot_template_notes"
  add_foreign_key "cot_template_note_users", "users"
  add_foreign_key "cot_template_notes", "practices"
  add_foreign_key "cot_template_notes", "users"
  add_foreign_key "cot_templates", "practices"
  add_foreign_key "cot_templates", "users", column: "created_by_id"
  add_foreign_key "course_of_treatments", "cot_categories"
  add_foreign_key "course_of_treatments", "patients"
  add_foreign_key "course_of_treatments", "practices"
  add_foreign_key "course_of_treatments", "users", column: "dentist_id"
  add_foreign_key "course_of_treatments", "users", column: "lead_clinician_id"
  add_foreign_key "course_of_treatments", "users", column: "treatment_coordinator_id"
  add_foreign_key "crm_boards", "practices"
  add_foreign_key "crm_cards", "cot_categories"
  add_foreign_key "crm_cards", "crm_lists"
  add_foreign_key "crm_cards", "patients"
  add_foreign_key "crm_cards", "treatments"
  add_foreign_key "crm_cards_labels", "crm_cards"
  add_foreign_key "crm_cards_labels", "crm_labels"
  add_foreign_key "crm_checklist_items", "crm_checklists"
  add_foreign_key "crm_checklists", "crm_cards"
  add_foreign_key "crm_completion_zones", "crm_boards"
  add_foreign_key "crm_custom_field_values", "crm_cards"
  add_foreign_key "crm_custom_field_values", "crm_custom_fields"
  add_foreign_key "crm_custom_fields", "crm_boards"
  add_foreign_key "crm_labels", "crm_boards"
  add_foreign_key "crm_lists", "crm_boards"
  add_foreign_key "dashboard_widgets", "dashboards"
  add_foreign_key "dashboard_widgets", "practices"
  add_foreign_key "dashboards", "users"
  add_foreign_key "document_templates", "practices"
  add_foreign_key "document_templates_treatments", "document_templates"
  add_foreign_key "document_templates_treatments", "treatments"
  add_foreign_key "document_types", "practices"
  add_foreign_key "documents", "document_types"
  add_foreign_key "documents", "practices"
  add_foreign_key "documents", "users"
  add_foreign_key "form_assignments", "onboarding_forms"
  add_foreign_key "form_assignments", "practices"
  add_foreign_key "form_assignments", "users"
  add_foreign_key "group_meeting_participants", "group_meetings"
  add_foreign_key "group_meeting_participants", "users"
  add_foreign_key "group_meetings", "meetings"
  add_foreign_key "group_meetings", "practices"
  add_foreign_key "group_meetings", "users", column: "manager_id"
  add_foreign_key "hr_templates", "practices"
  add_foreign_key "hr_templates", "users", column: "created_by_id"
  add_foreign_key "hr_templates_onboarding_forms", "hr_templates"
  add_foreign_key "hr_templates_onboarding_forms", "onboarding_forms"
  add_foreign_key "hr_templates_users", "hr_templates"
  add_foreign_key "hr_templates_users", "users"
  add_foreign_key "icon_states", "calendar_bookings"
  add_foreign_key "icon_states", "charting_appointments"
  add_foreign_key "invoice_items", "charted_treatments"
  add_foreign_key "invoice_items", "invoices"
  add_foreign_key "invoice_items", "treatments"
  add_foreign_key "invoices", "course_of_treatments"
  add_foreign_key "invoices", "patients"
  add_foreign_key "invoices", "practices"
  add_foreign_key "invoices", "users", column: "clinician_id"
  add_foreign_key "invoices", "users", column: "created_by_id"
  add_foreign_key "lab_docket_items", "lab_dockets"
  add_foreign_key "lab_docket_treatments", "lab_docket_items"
  add_foreign_key "lab_docket_treatments", "lab_items"
  add_foreign_key "lab_dockets", "lab_works"
  add_foreign_key "lab_items", "labs"
  add_foreign_key "lab_work_files", "lab_works"
  add_foreign_key "lab_work_files", "users", column: "created_by_id"
  add_foreign_key "lab_work_logs", "lab_works"
  add_foreign_key "lab_work_logs", "users", column: "created_by_id"
  add_foreign_key "lab_works", "charting_appointments"
  add_foreign_key "lab_works", "labs"
  add_foreign_key "lab_works", "patients"
  add_foreign_key "lab_works", "practices"
  add_foreign_key "lab_works", "users", column: "checked_in_by_id"
  add_foreign_key "lab_works", "users", column: "practitioner_id"
  add_foreign_key "lab_works", "users", column: "quality_checked_by_id"
  add_foreign_key "labs", "practices"
  add_foreign_key "letters", "patients"
  add_foreign_key "letters", "practices"
  add_foreign_key "letters", "signable_documents"
  add_foreign_key "letters", "users"
  add_foreign_key "medical_histories", "patients"
  add_foreign_key "medical_histories", "users", column: "created_by_id"
  add_foreign_key "medical_histories_medical_history_answers", "medical_histories"
  add_foreign_key "medical_histories_medical_history_answers", "medical_history_answers"
  add_foreign_key "medical_history_ai_alerts", "medical_histories"
  add_foreign_key "medical_history_ai_alerts", "medical_history_questions", column: "question_id"
  add_foreign_key "medical_history_ai_changes", "medical_histories"
  add_foreign_key "medical_history_ai_changes", "medical_history_questions", column: "question_id"
  add_foreign_key "medical_history_answers", "medical_history_questions"
  add_foreign_key "medical_history_questions", "medical_history_answers"
  add_foreign_key "medical_history_questions", "practices"
  add_foreign_key "meeting_comments", "meetings"
  add_foreign_key "meeting_comments", "practices"
  add_foreign_key "meeting_comments", "users"
  add_foreign_key "meetings", "practices"
  add_foreign_key "message_read_receipts", "conversation_messages"
  add_foreign_key "message_read_receipts", "users"
  add_foreign_key "notification_templates", "practices"
  add_foreign_key "onboarding_answers", "onboarding_questions"
  add_foreign_key "onboarding_forms", "practices"
  add_foreign_key "onboarding_forms", "users"
  add_foreign_key "onboarding_questions", "onboarding_forms"
  add_foreign_key "opgs", "course_of_treatments"
  add_foreign_key "opgs", "patients"
  add_foreign_key "opgs", "users", column: "developed_by_id"
  add_foreign_key "opgs", "users", column: "taken_by_id"
  add_foreign_key "patient_alerts", "alerts"
  add_foreign_key "patient_alerts", "patients"
  add_foreign_key "patient_assets", "lab_dockets"
  add_foreign_key "patient_assets", "medical_histories"
  add_foreign_key "patient_cot_payment_plans", "cot_payment_plans"
  add_foreign_key "patient_cot_payment_plans", "patients"
  add_foreign_key "patient_gps", "practices"
  add_foreign_key "patient_notes", "patient_notes"
  add_foreign_key "patient_notes", "patients"
  add_foreign_key "patient_notes", "users"
  add_foreign_key "patients", "cot_payment_plans", column: "active_cot_payment_plan_id"
  add_foreign_key "patients", "patient_gps"
  add_foreign_key "patients", "practices", column: "current_practice_id"
  add_foreign_key "patients", "users", column: "witnessed_by_id"
  add_foreign_key "patients_users", "patients"
  add_foreign_key "patients_users", "users"
  add_foreign_key "payment_plans", "course_of_treatments"
  add_foreign_key "payment_plans", "invoices"
  add_foreign_key "payment_plans", "patients"
  add_foreign_key "payments", "calendar_bookings"
  add_foreign_key "payments", "course_of_treatments"
  add_foreign_key "payments", "invoices"
  add_foreign_key "payments", "patients"
  add_foreign_key "payments", "payment_plans"
  add_foreign_key "payments", "users", column: "transactioned_by_id"
  add_foreign_key "perio_alerts", "perio_exams"
  add_foreign_key "perio_alerts", "practices"
  add_foreign_key "perio_changes", "perio_exams"
  add_foreign_key "perio_changes", "practices"
  add_foreign_key "perio_exams", "patients"
  add_foreign_key "perio_exams", "practices"
  add_foreign_key "perio_exams", "users", column: "provided_by_id"
  add_foreign_key "practices_patients", "patients"
  add_foreign_key "practices_patients", "practices"
  add_foreign_key "practices_users", "practices"
  add_foreign_key "practices_users", "users"
  add_foreign_key "recall_dentists", "recalls"
  add_foreign_key "recall_dentists", "users", column: "dentist_id"
  add_foreign_key "recall_schedules", "cot_payment_plans"
  add_foreign_key "recall_schedules", "patients"
  add_foreign_key "recall_schedules", "practices"
  add_foreign_key "recall_schedules", "treatments"
  add_foreign_key "recall_schedules", "users", column: "created_by_id"
  add_foreign_key "recall_schedules_dentists", "recall_schedules"
  add_foreign_key "recall_schedules_dentists", "users", column: "dentist_id"
  add_foreign_key "recalls", "calendar_bookings"
  add_foreign_key "recalls", "cot_payment_plans"
  add_foreign_key "recalls", "patients"
  add_foreign_key "recalls", "practices"
  add_foreign_key "recalls", "recall_schedules"
  add_foreign_key "recalls", "treatments"
  add_foreign_key "recalls", "users", column: "created_by_id"
  add_foreign_key "refunds", "payments"
  add_foreign_key "registered_devices", "patients"
  add_foreign_key "registered_devices", "practices"
  add_foreign_key "roles_users", "roles"
  add_foreign_key "roles_users", "users"
  add_foreign_key "signable_documents", "document_templates"
  add_foreign_key "signable_documents", "patients"
  add_foreign_key "signable_documents", "treatment_plan_options"
  add_foreign_key "signable_documents", "users", column: "created_by_id"
  add_foreign_key "signature_requests", "charting_appointments"
  add_foreign_key "signature_requests", "patients"
  add_foreign_key "signature_requests", "signable_documents"
  add_foreign_key "signature_requests", "users", column: "created_by_id"
  add_foreign_key "signature_requests", "users", column: "sent_by_id"
  add_foreign_key "signature_requests", "users", column: "witnessed_by_id"
  add_foreign_key "signature_verifications", "signature_requests"
  add_foreign_key "stls", "charting_appointments"
  add_foreign_key "stls", "course_of_treatments"
  add_foreign_key "stls", "patients"
  add_foreign_key "stls", "users", column: "developed_by_id"
  add_foreign_key "stls", "users", column: "taken_by_id"
  add_foreign_key "task_comments", "practices"
  add_foreign_key "task_comments", "tasks"
  add_foreign_key "task_comments", "users"
  add_foreign_key "tasks", "documents"
  add_foreign_key "tasks", "practices"
  add_foreign_key "treatment_categories", "practices"
  add_foreign_key "treatment_plan_form_treatments", "treatment_plan_forms"
  add_foreign_key "treatment_plan_form_treatments", "treatments"
  add_foreign_key "treatment_plan_forms", "practices"
  add_foreign_key "treatment_plan_information_treatments", "treatment_plan_informations"
  add_foreign_key "treatment_plan_information_treatments", "treatments"
  add_foreign_key "treatment_plan_informations", "practices"
  add_foreign_key "treatment_plan_options", "cot_categories"
  add_foreign_key "treatment_plan_options", "course_of_treatments"
  add_foreign_key "treatment_plan_options", "patients"
  add_foreign_key "treatment_plan_options", "treatment_plans"
  add_foreign_key "treatment_plan_options", "users", column: "created_by_id"
  add_foreign_key "treatment_plan_options", "users", column: "main_clinician_id"
  add_foreign_key "treatment_plan_options", "users", column: "treatment_coordinator_id"
  add_foreign_key "treatment_plan_stories", "practices"
  add_foreign_key "treatment_plan_templates", "practices"
  add_foreign_key "treatment_plan_templates", "users"
  add_foreign_key "treatment_plans", "practices"
  add_foreign_key "treatments", "treatment_categories"
  add_foreign_key "user_favorite_reports", "users"
  add_foreign_key "user_favorite_treatments", "treatments"
  add_foreign_key "user_favorite_treatments", "users"
  add_foreign_key "whatsapp_buttons", "whatsapp_components"
  add_foreign_key "xrays", "course_of_treatments"
  add_foreign_key "xrays", "patients"
  add_foreign_key "xrays", "users", column: "developed_by_id"
  add_foreign_key "xrays", "users", column: "taken_by_id"
end
