class CreateIconStates < ActiveRecord::Migration[7.2]
  def change
    create_table :icon_states do |t|
      t.references :calendar_booking, null: true, foreign_key: true
      t.references :charting_appointment, null: true, foreign_key: true

      t.integer :lab_work_state, null: false, default: 0
      t.integer :payments_state, null: false, default: 0
      t.integer :documents_state, null: false, default: 0
      t.integer :complaints_state, null: false, default: 0
      t.integer :medical_history_state, null: false, default: 0

      t.index [:calendar_booking_id, :charting_appointment_id], unique: true

      t.timestamps
    end
  end
end
