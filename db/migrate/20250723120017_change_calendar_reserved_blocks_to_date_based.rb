class ChangeCalendarReservedBlocksToDateBased < ActiveRecord::Migration[7.1]
  def change
    remove_column :calendar_reserved_blocks, :day_of_week, :integer

    remove_column :calendar_reserved_blocks, :start_time, :time
    add_column :calendar_reserved_blocks, :start_time, :datetime, null: false

    remove_column :calendar_reserved_blocks, :end_time, :time
    add_column :calendar_reserved_blocks, :end_time, :datetime, null: false

    add_column :calendar_reserved_blocks, :repeat_event, :boolean, default: false
    add_column :calendar_reserved_blocks, :repeat_days, :integer, array: true, default: []
    add_column :calendar_reserved_blocks, :repeat_pattern, :string
    add_column :calendar_reserved_blocks, :end_repeat_date, :date
    add_column :calendar_reserved_blocks, :repeat_indefinitely, :boolean, default: false
    add_column :calendar_reserved_blocks, :recurrent_identifier, :string

    add_index :calendar_reserved_blocks, :start_time
    add_index :calendar_reserved_blocks, :recurrent_identifier
  end
end
