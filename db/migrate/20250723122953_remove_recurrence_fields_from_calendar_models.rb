class RemoveRecurrenceFieldsFromCalendarModels < ActiveRecord::Migration[7.1]
  def change
    remove_column :calendar_bookings, :repeat_event, :boolean
    remove_column :calendar_bookings, :repeat_days, :string, array: true
    remove_column :calendar_bookings, :end_repeat_date, :date
    remove_column :calendar_bookings, :repeat_indefinitely, :boolean

    remove_column :calendar_reserved_blocks, :repeat_event, :boolean
    remove_column :calendar_reserved_blocks, :repeat_days, :integer, array: true
    remove_column :calendar_reserved_blocks, :repeat_pattern, :string
    remove_column :calendar_reserved_blocks, :end_repeat_date, :date
    remove_column :calendar_reserved_blocks, :repeat_indefinitely, :boolean
  end
end
