# frozen_string_literal: true

source 'https://rubygems.org'

gem 'dotenv-rails', groups: %i[development test]
gem 'importmap-rails'
gem 'jbuilder'
gem 'pg', '~> 1.1'
gem 'puma', '>= 5.0'
gem 'rails', '~> 7.2.2', '>= 7.2.2.1'
gem 'rubocop', '~> 1.35.0'
gem 'rubocop-rails', '~> 2.8'
gem 'rubocop-rspec', '~> 2.12'
gem 'sprockets-rails'
gem 'stimulus-rails'
gem 'turbo-rails'
gem 'tzinfo-data', platforms: %i[mswin mswin64 mingw x64_mingw jruby]

gem 'ffi'
gem 'nokogiri', '~> 1.18.0'

gem 'devise'
gem 'devise_token_auth'
gem 'omniauth'

gem 'acts_as_list'

gem 'aws-sdk-s3', '~> 1.114'
gem 'cocoon'
gem 'countries'
gem 'cssbundling-rails'
gem 'groupdate'
gem 'httparty'
gem 'icalendar'
gem 'jsbundling-rails'
gem 'mailgun-ruby'
gem 'money'
gem 'pundit'
gem 'ransack'
gem 'rqrcode', '2.2.0'
gem 'sassc-rails'
gem 'sentry-rails'
gem 'sidekiq'
gem 'sidekiq-scheduler'
gem 'sidekiq-unique-jobs'
gem 'stripe'
gem 'truncate_html'
gem 'wicked'
gem 'wicked_pdf'
gem 'will_paginate'
gem 'wkhtmltopdf-binary', '= ********'

source 'https://token:<EMAIL>/basic/fontawesome-pro/ruby/' do
  gem 'font-awesome-pro-sass', '6.6.0'
end

gem 'bootsnap', require: false

group :development, :test do
  gem 'brakeman', require: false
  gem 'byebug'
  gem 'debug', platforms: %i[mri mswin mswin64 mingw x64_mingw], require: 'debug/prelude'
  gem 'faker'
  gem 'letter_opener'
  gem 'letter_opener_web'
  gem 'pry'
  gem 'rubocop-rails-omakase', require: false
end

group :development do
  gem 'annotate'
  gem 'bullet'
  gem 'web-console'
end

group :test do
  gem 'capybara'
  gem 'selenium-webdriver'
end

gem 'activerecord-import'
gem 'ancestry'
gem 'benchmark'
gem 'browser'
gem 'combine_pdf', '=1.0.29'
gem 'email_reply_parser'
gem 'google-cloud-ai_platform'
gem 'hiredis-client'
gem 'levenshtein', '~> 0.2.2'
gem 'parallel'
gem 'paranoia'
gem 'prawn'
gem 'pusher'
gem 'rack-cors'
gem 'rack-mini-profiler'
gem 'rubyzip'
gem 'sentry-ruby'
