Metrics/BlockLength:
  Exclude:
    - app/services/import/dentally_importer.rb
    - app/services/calendar/availability_calculator.rb
    - app/sidekiq/notify_lab_work_not_arrived_job.rb
    - app/sidekiq/notify_not_in_surgery_status_job.rb
    - app/sidekiq/signature_request_job.rb
    - app/models/calendar_booking.rb
Metrics/BlockNesting:
  Exclude:
    - app/controllers/users/passwords_controller.rb
    - app/controllers/admin/calendar_bookings_controller.rb
Metrics/ModuleLength:
  Exclude:
    - app/helpers/admin/general_settings_helper.rb
Metrics/CyclomaticComplexity:
  Enabled: false
Metrics/PerceivedComplexity:
  Enabled: false
Security/Open:
  Enabled: false
Rails/InverseOf:
  Enabled: false
Style/MultilineBlockChain:
  Exclude:
    - app/controllers/admin/calendar_bookings_controller.rb
Style/GuardClause:
  Enabled: false
Naming/AccessorMethodName:
  Enabled: false
Security/Eval:
  Enabled: false
Lint/FloatComparison:
  Enabled: false
Style/HashLikeCase:
  Enabled: false
Rails/HelperInstanceVariable:
  Enabled: false
Rails/OutputSafety:
  Exclude:
    - app/helpers/patients/signature_helper.rb
Layout/LineLength:
  Exclude:
    - app/models/calendar_booking.rb
